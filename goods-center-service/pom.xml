<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.wosai.smartbiz</groupId>
    <artifactId>goods-center-service</artifactId>

    <parent>
        <groupId>com.wosai.smartbiz</groupId>
        <artifactId>goods-center</artifactId>
        <version>3.60.0</version>
    </parent>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.wosai.smartbiz</groupId>
            <artifactId>order-center-api</artifactId>
        </dependency>
        <!--api-->
        <dependency>
            <groupId>com.wosai.smartbiz</groupId>
            <artifactId>goods-center-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wosai.smartbiz</groupId>
            <artifactId>user-center</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wosai.smartbiz</groupId>
            <artifactId>goods-center-main</artifactId>
        </dependency>
        <!--instrument-->
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-instrumentation</artifactId>
        </dependency>
        <!--kafka-->
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-kafka</artifactId>
        </dependency>
        <!--web rpc-->
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-web-rpc</artifactId>
        </dependency>
        <!-- es-->
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-data-elasticsearch</artifactId>
        </dependency>
        <!-- redis-->
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-data-redis</artifactId>
        </dependency>
        <!--test-->
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- uc-->
        <dependency>
            <groupId>com.wosai.uc</groupId>
            <artifactId>uc-user-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>core-crypto-api</artifactId>
        </dependency>

        <!-- qrcode -->
        <dependency>
            <groupId>com.wosai.market</groupId>
            <artifactId>merchant-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-activity-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.market</groupId>
            <artifactId>mcc-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.market</groupId>
            <artifactId>awesome-goods-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>smart-goods-common</artifactId>
                    <groupId>com.wosai.smart.goods</groupId>
                </exclusion>
            </exclusions>

        </dependency>

        <dependency>
            <groupId>com.wosai.market</groupId>
            <artifactId>awesome-goods-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>smart-goods-common</artifactId>
                    <groupId>com.wosai.smart.goods</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>uitem-core-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>5.6.2</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>core-business-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.upay</groupId>
                    <artifactId>upay-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-common</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.print</groupId>
            <artifactId>galaxy-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.market</groupId>
            <artifactId>awesome-message-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>wosai-brave153-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>app-gated-api</artifactId>
            <version>1.9.0</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.market.user</groupId>
            <artifactId>customer-user-api</artifactId>
        </dependency>

        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>logging-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-toolkit-logback-1.x</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eclipse.paho</groupId>
            <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>sales-terminal-order-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>itsys-common</artifactId>
                    <groupId>com.wosai.sales</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.ft</groupId>
            <artifactId>ft-order-databus-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-webmvc</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                    <artifactId>tomcat-embed-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>customer-relation-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.market</groupId>
            <artifactId>tethys-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>crm-databus</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.it</groupId>
            <artifactId>oms-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>sales-system-profit-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>sales-system-service-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.iot</groupId>
                    <artifactId>shouqianba-iot-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>sales-system-gateway-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shouqianba.workflow</groupId>
            <artifactId>sp-workflow-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.market</groupId>
            <artifactId>boss-circle-user-api</artifactId>
            <version>3.1.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-annotation</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-extension</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>app-push-api</artifactId>
            <version>2.9.3-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.middleware</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-qrcode-api</artifactId>
            <version>1.6.1</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>3.5.2</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.market</groupId>
            <artifactId>full-trace-test-config</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.market</groupId>
            <artifactId>data-events-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.pantheon</groupId>
                    <artifactId>wosai-common-databus</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.pantheon</groupId>
                    <artifactId>wosai-common-instrumentation</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.middleware</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shouqianba</groupId>
            <artifactId>marketing-saas-external-platform-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.smart.goods</groupId>
            <artifactId>smart-goods-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.smart.goods</groupId>
            <artifactId>smart-standard-goods-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.cua</groupId>
            <artifactId>third-mapping-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-toolkit-sentinel</artifactId>
            <version>1.8.1</version>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>aop-gateway-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.invoice.app.backend</groupId>
            <artifactId>invoice-app-backend-api</artifactId>
            <version>1.2.3</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai</groupId>
                    <artifactId>vip-common-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.giftcard</groupId>
                    <artifactId>giftcard-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <artifactId>marketing-saas-merchant-api</artifactId>
            <groupId>com.wosai</groupId>
        </dependency>

        <dependency>
            <groupId>com.shouqianba</groupId>
            <artifactId>mkss-customer-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.marketing.prepaid</groupId>
            <artifactId>marketing-saas-prepaid-card-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shouqianba.smart</groupId>
            <artifactId>licence-manager-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.iot</groupId>
            <artifactId>shouqianba-iot-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>dc-service-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.market</groupId>
            <artifactId>outer-service-adapter-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>bank-info-api</artifactId>
        </dependency>

        <!--redisson-->
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>3.19.1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.redisson</groupId>
                    <artifactId>redisson-spring-data-30</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-boot-starter-actuator</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-data-22</artifactId>
            <version>3.19.1</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.it</groupId>
            <artifactId>renewal-center-api</artifactId>
            <version>1.0.23</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>goods-center-service</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${springboot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.avro</groupId>
                <artifactId>avro-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.wosai.middleware</groupId>
                <artifactId>wosai-logging-maven-plugin</artifactId>
                <version>1.4.0-SNAPSHOT</version>
                <configuration>
                    <enableCallerData>true</enableCallerData>
                    <!-- Pattern 需要自己配置显式打印 tid，线上JSON自带有tid -->
                    <patternLayout>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] [%tid] %-5level %logger{36}.%M - %msg%n</patternLayout>
                    <profiles>
                        <profile>
                            <name>prod</name>
                            <level>INFO</level>
                            <references>
                                <ref>FT_CONSOLE_JSON</ref> <!-- 输出到标准输出，格式是JSON -->
                            </references>
                        </profile>
                        <profile>
                            <name>beta,mock</name>
                            <level>INFO</level>
                            <references>
                                <ref>FT_FILE</ref> <!-- 输出到文件，默认格式是PATTERN (1.4.0改动) -->
                                <ref>FT_CONSOLE_PATTERN</ref> <!-- 输出到标准输出，格式是格式化字符串 (1.2.0新增功能) -->
                            </references>
                        </profile>
                        <profile>
                            <name>default</name>   <!-- 在本地开发调试时，在IDE中设置 active profile为default -->
                            <level>INFO</level>
                            <references>
                                <ref>FT_CONSOLE_PATTERN</ref>
                            </references>
                        </profile>
                    </profiles>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>generate-logback-spring</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!--<plugin>
                <groupId>org.apache.avro</groupId>
                <artifactId>avro-maven-plugin</artifactId>
                <version>1.8.2</version>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>schema</goal>
                        </goals>
                        <configuration>
                            <sourceDirectory>${project.basedir}/src/main/resources/avro/</sourceDirectory>
                            <outputDirectory>${project.basedir}/src/main/java/</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>-->
        </plugins>
    </build>

</project>
