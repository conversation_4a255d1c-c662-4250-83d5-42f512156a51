package com.wosai.smartbiz.merchant.user;

import com.alibaba.fastjson.JSON;
import com.wosai.app.dto.app.AppLoginAuthReq;
import com.wosai.app.dto.app.AppLoginReq;
import com.wosai.app.dto.app.TokenReq;
import com.wosai.app.dto.app.device.AppLoginInfo;
import com.wosai.app.dto.login.UcDeviceReq;
import com.wosai.app.service.app.AppBackendUserService;
import com.wosai.app.service.app.LoginDeviceService;
import com.wosai.smartbiz.Application;
import com.wosai.smartbiz.uc.utils.common.MD5Utils;
import com.wosai.smartbiz.user.user.enums.sqb.SqbDeviceTypeEnum;
import com.wosai.smartbiz.user.user.enums.sqb.SqbPlatformEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles("beta")
public class AppUserServiceTests {

    @Autowired
    private AppBackendUserService sqbAppBackendUserService;

    @Autowired
    private LoginDeviceService sqbLoginDeviceService;

    @Test
    public void testLogin() {
        String userPhone = "13958099751";
        String userPwd = "a123456";
        String terminalSn = "xxxxxx123456agcefs";
        UcDeviceReq ucDeviceReq = new UcDeviceReq();
        ucDeviceReq.setPlatform(SqbPlatformEnum.SMART.getCode());
        ucDeviceReq.setDevice_fingerprint(terminalSn);
        ucDeviceReq.setDevice_type(SqbDeviceTypeEnum.CASHIER.getCode());
        ucDeviceReq.setDevice_name(null);
        ucDeviceReq.setDevice_brand(null);
        ucDeviceReq.setDevice_model(null);
        ucDeviceReq.setIP(null);

        AppLoginReq appLoginReq = new AppLoginReq();
        appLoginReq.setUsername(userPhone);
        appLoginReq.setPassword(MD5Utils.encryptWithMD5(userPwd));
        appLoginReq.setIp(null);
        appLoginReq.setUc_device(ucDeviceReq);

        AppLoginInfo appLoginInfo = sqbAppBackendUserService.loginWithPassword(appLoginReq);
        System.out.println("appLoginInfo:" + JSON.toJSONString(appLoginInfo));
    }


    @Test
    public void testLogin4AuthCode() {
        String userPhone = "13958099751";
        String authCode = "123456";
        String terminalSn = "xxxxxx123456agcefs";
        UcDeviceReq ucDeviceReq = new UcDeviceReq();
        ucDeviceReq.setPlatform(SqbPlatformEnum.SMART.getCode());
        ucDeviceReq.setDevice_fingerprint(terminalSn);
        ucDeviceReq.setDevice_type(SqbDeviceTypeEnum.CASHIER.getCode());
        ucDeviceReq.setDevice_name(null);
        ucDeviceReq.setDevice_brand(null);
        ucDeviceReq.setDevice_model(null);
        ucDeviceReq.setIP(null);

        AppLoginAuthReq appLoginAuthReq = new AppLoginAuthReq();
        appLoginAuthReq.setUsername(userPhone);
        appLoginAuthReq.setAuth_code(authCode);
        appLoginAuthReq.setIp(null);
        appLoginAuthReq.setUc_device(ucDeviceReq);

        AppLoginInfo appLoginInfo = sqbAppBackendUserService.loginWithAuthCode(appLoginAuthReq);
        System.out.println("appLoginInfo:" + JSON.toJSONString(appLoginInfo));
    }


    @Test
    public void testLogout() {
        String token = "13958099751";
        String terminalSn = "xxxxxx123456agcefs";
        UcDeviceReq ucDeviceReq = new UcDeviceReq();
        ucDeviceReq.setPlatform(SqbPlatformEnum.SMART.getCode());
        ucDeviceReq.setDevice_fingerprint(terminalSn);
        ucDeviceReq.setDevice_type(SqbDeviceTypeEnum.CASHIER.getCode());
        ucDeviceReq.setDevice_name(null);
        ucDeviceReq.setDevice_brand(null);
        ucDeviceReq.setDevice_model(null);
        ucDeviceReq.setIP(null);

        TokenReq tokenReq = new TokenReq();
        tokenReq.setToken(token);
        tokenReq.setUc_device(ucDeviceReq);

        sqbAppBackendUserService.logout(tokenReq);
        System.out.println("tokenReq:" + JSON.toJSONString(tokenReq));
    }
}
