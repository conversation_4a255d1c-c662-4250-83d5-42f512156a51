package com.wosai.smartbiz.merchant.user;

import com.alibaba.fastjson.JSON;
import com.wosai.app.dto.V2.UcMerchantUserInfo;
import com.wosai.app.dto.V2.UcMerchantUserSimpleInfo;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.smartbiz.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles("beta")
public class MerchantUserServiceV2Tests {

    @Autowired
    private MerchantUserServiceV2 sqbMerchantUserServiceV2;

    @Test
    public void testActiveUser() {
        try {
            String merchantUserId = "2222b444-8fa5-4c7c-a0b6-bd84482bd140";
            UcMerchantUserSimpleInfo ucMerchantUserSimpleInfo = sqbMerchantUserServiceV2.activeUser(merchantUserId);
            System.out.println("ucMerchantUserSimpleInfo:" + JSON.toJSONString(ucMerchantUserSimpleInfo));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    @Test
    public void testGetMerchantUserByCellphone() {
        try {
            String cellphone = "18773221770";
            List<UcMerchantUserInfo> userInfoList = sqbMerchantUserServiceV2.getMerchantUserByCellphone(cellphone);
            System.out.println("userInfoList:" + JSON.toJSONString(userInfoList));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    @Test
    public void testGetSimpleInfoByCellphone() {
        try {
            String cellphone = "18773221770";
            List<UcMerchantUserSimpleInfo> userSimpleInfoList = sqbMerchantUserServiceV2.getSimpleInfoByCellphone(cellphone);
            System.out.println("userSimpleInfoList:" + JSON.toJSONString(userSimpleInfoList));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    @Test
    public void testGetMerchantUserByUcUserId() {
        try {
            String ucUserId = "9f22956b-5959-4053-9474-68afb73671dd";
            List<UcMerchantUserInfo> userInfoList = sqbMerchantUserServiceV2.getMerchantUserByUcUserId(ucUserId);
            System.out.println("userInfoList:" + JSON.toJSONString(userInfoList));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}
