package com.wosai.smartbiz.merchant.user;

import com.alibaba.fastjson.JSON;
import com.wosai.app.dto.V2.UcMerchantUserSimpleInfo;
import com.wosai.app.dto.login.LoginInfo;
import com.wosai.app.dto.login.MchUserLoginReq;
import com.wosai.app.dto.login.UcDeviceReq;
import com.wosai.app.service.MchUserLoginService;
import com.wosai.smartbiz.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles("beta")
public class LoginServiceTests {

    @Autowired
    private MchUserLoginService sqbMchUserLoginService;

    @Test
    public void testLogin() {
        try {
            MchUserLoginReq mchUserLoginReq = new MchUserLoginReq();
            mchUserLoginReq.setUsername("18773221770");
            mchUserLoginReq.setPassword("JjSmt4");

            UcDeviceReq ucDeviceReq = new UcDeviceReq();
            ucDeviceReq.setDevice_type(0);
            ucDeviceReq.setDevice_name("收银机");
            ucDeviceReq.setDevice_fingerprint("a123456789123456789");
            mchUserLoginReq.setUc_device(ucDeviceReq);

            LoginInfo loginInfo = sqbMchUserLoginService.login(mchUserLoginReq);
            System.out.println("loginInfo:" + JSON.toJSONString(loginInfo));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}
