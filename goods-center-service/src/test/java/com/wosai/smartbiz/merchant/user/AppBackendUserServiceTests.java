package com.wosai.smartbiz.merchant.user;

import com.alibaba.fastjson.JSON;
import com.wosai.app.dto.app.authCode.AppAuthCodeReq;
import com.wosai.app.dto.app.authCode.SendAuthCodeResp;
import com.wosai.app.service.app.AppBackendUserService;
import com.wosai.app.service.app.LoginDeviceService;
import com.wosai.smartbiz.Application;
import com.wosai.smartbiz.uc.constants.Constants;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles("beta")
public class AppBackendUserServiceTests {

    @Autowired
    private AppBackendUserService sqbAppBackendUserService;

    @Autowired
    private LoginDeviceService sqbLoginDeviceService;

    @Test
    public void testSendAuthCode() {
        String userPhone = "13958099751";
        AppAuthCodeReq authCodeReq = new AppAuthCodeReq();
        authCodeReq.setIdentifier(userPhone);
        authCodeReq.setScene(Constants.CashierLoginConstants.AUTH_CODE_LOGIN_SCENE);
        authCodeReq.setRiskData("abc");
        SendAuthCodeResp sendAuthCodeResp = sqbAppBackendUserService.sendAuthCodeWithRisk(authCodeReq);
        System.out.println("sendAuthCodeResp:" + JSON.toJSONString(sendAuthCodeResp));
    }
}
