package com.wosai.smartbiz;

import com.wosai.smartbiz.goods.direct.StoreTableDAO;
import com.wosai.smartbiz.goods.domain.repast.table.StoreTableDO;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles("beta")
public class MybatisInterceptorTest {
    @Autowired
    private StoreTableDAO storeTableDAO;

    @Autowired
    private SqlSessionFactory sqlSessionFactory;

    @Test
    public void fakeTableInterceptorSelect() {
        // 看有哪些interceptor, FakeTableInterceptor在顺序上应该出现在PageInterceptor后面
        List<Interceptor> interceptors = sqlSessionFactory.getConfiguration().getInterceptors();
        StoreTableDO byTableId = storeTableDAO.getByTableId(121341241L);
    }
}
