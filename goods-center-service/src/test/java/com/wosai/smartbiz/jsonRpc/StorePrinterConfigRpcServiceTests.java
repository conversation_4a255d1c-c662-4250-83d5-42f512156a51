package com.wosai.smartbiz.jsonRpc;

import com.alibaba.fastjson.JSON;
import com.wosai.smartbiz.Application;
import com.wosai.smartbiz.converter.gds.StorePrinterConfigConverter;
import com.wosai.smartbiz.dto.req.AppEdgeStoreBaseRequest;
import com.wosai.smartbiz.dto.req.printer.StorePrinterConfigRpcSaveReq;
import com.wosai.smartbiz.gds.jsonrpc.StorePrinterConfigRpcService;
import com.wosai.smartbiz.uc.manager.StorePrinterConfigService;
import com.wosai.smartbiz.user.user.dto.StorePrinterConfigDTO;
import com.wosai.smartbiz.user.user.enums.print.PrintModeEnum;
import com.wosai.smartbiz.user.user.enums.print.PrintRangeEnum;
import com.wosai.smartbiz.user.user.enums.print.PrinterTypeEnum;
import com.wosai.smartbiz.user.user.vo.printer.AppEdgeStorePrinterConfigVO;
import com.wosai.smartbiz.user.user.vo.printer.PrintCategoryVO;
import com.wosai.smartbiz.user.user.vo.printer.PrintSpuVO;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.core.service.StoreService;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021/1/14
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles("beta")
public class StorePrinterConfigRpcServiceTests {

    @Autowired
    private StorePrinterConfigService storePrinterConfigService;

    @Autowired
    private StorePrinterConfigRpcService storePrinterConfigRpcService;

    private final static String merchantId = "eb3bda9a-372a-41b8-a2f6-69a91c5926b4";

    private final static String storeId = "bd40dd6d-e021-443c-8b2c-ddf8db770857";

    private final static String userId = "1c29449b-7241-45fa-bdf4-6d8e64a2625b";

    @Test
    public void testSave(){

        StorePrinterConfigRpcSaveReq req = new StorePrinterConfigRpcSaveReq();
        req.setMerchant_id(merchantId);
        req.setStoreId(storeId);
        req.setId(userId);
        req.setConfigId(52L);
        req.setPrinterName("ZHQ测试打印机");
        req.setPrinterType(PrinterTypeEnum.XINYE);
        req.setPrinterIp("*************");
        req.setPaperSize(58);

        req.setPrintFront(Boolean.TRUE);
        req.setFrontPrintNum(1);
        req.setPrintPointMenuReceipt(Boolean.TRUE);
        req.setPointMenuReceiptPrintNum(2);
        req.setPrintAreaRange(PrintRangeEnum.PART);
        req.setPrintAreaIdList(Lists.newArrayList("18", "19"));

        req.setPrintBack(Boolean.TRUE);
        req.setBackPrintNum(3);
        req.setPrintModes(Lists.newArrayList(PrintModeEnum.ALL_IN_ONE, PrintModeEnum.ONE_FOR_ONE));

        req.setPrintSpuRange(PrintRangeEnum.PART);
        req.setPrintCategoryList(Lists.newArrayList(
                PrintCategoryVO.builder()
                        .categoryId("b11fb573-52f0-4281-81b9-8e5cbd6a843e")
                        .categoryName("热菜III")
                        .build())
        );
        req.setPrintSpuList(Lists.newArrayList(
                PrintSpuVO.builder()
                        .spuId("2d2a9945-9e2f-4666-8157-cba515f057a9")
                        .spuTitle("拍黄瓜")
                        .build()
        ));

        req.setPrintTakeawayReceipt(Boolean.TRUE);
        req.setTakeawayReceiptPrintNum(4);

        storePrinterConfigRpcService.save(req);

        AppEdgeStoreBaseRequest request = new AppEdgeStoreBaseRequest();
        request.setMerchant_id(merchantId);
        request.setStoreId(storeId);

        List<AppEdgeStorePrinterConfigVO> result = storePrinterConfigRpcService.listByStoreId(request);
        System.out.println(JSON.toJSONString(result));

    }

    @Test
    public void testListByStoreId(){

        AppEdgeStoreBaseRequest request = new AppEdgeStoreBaseRequest();
        request.setMerchant_id("c1063b47-f70c-4d4d-9997-0e1fd5d137b7");
        request.setStoreId("2c3781be-a74d-46b0-a28a-fe1455f9ad6b");

        List<AppEdgeStorePrinterConfigVO> result = storePrinterConfigRpcService.listByStoreId(request);
        System.out.println(JSON.toJSONString(result));
    }


    @Autowired
    private StoreService storeService;

    @Test
    public void testList(){
        ListResult result = storeService.getStoreListByMerchantId("c1063b47-f70c-4d4d-9997-0e1fd5d137b7", null, null);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testCashierVO() {

        List<StorePrinterConfigDTO> dtoList = storePrinterConfigService.listConfigByStoreId(merchantId, storeId);

        System.out.println(JSON.toJSONString(dtoList.stream()
                .map(StorePrinterConfigConverter::toCashierVO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList())));

    }

}
