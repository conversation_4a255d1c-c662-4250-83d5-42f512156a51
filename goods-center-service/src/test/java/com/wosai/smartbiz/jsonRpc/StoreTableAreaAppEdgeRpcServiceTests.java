package com.wosai.smartbiz.jsonRpc;

import com.alibaba.fastjson.JSON;
import com.wosai.smartbiz.Application;
import com.wosai.smartbiz.dto.req.AppEdgeStoreBaseRequest;
import com.wosai.smartbiz.dto.req.table.area.StoreTableAreaBatchSaveReq;
import com.wosai.smartbiz.dto.req.table.area.StoreTableAreaQueryReq;
import com.wosai.smartbiz.dto.req.table.jsonrpc.StoreTableAreaSaveRpcReq;
import com.wosai.smartbiz.dto.vo.table.StoreAreaTableGroupVO;
import com.wosai.smartbiz.dto.vo.table.StoreTableAreaRpcVO;
import com.wosai.smartbiz.gds.jsonrpc.StoreTableAreaAppEdgeRpcService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/1/15
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles(value = "beta")
public class StoreTableAreaAppEdgeRpcServiceTests {

    @Autowired
    private StoreTableAreaAppEdgeRpcService storeTableAreaAppEdgeRpcService;

    private final static String merchantId = "eb3bda9a-372a-41b8-a2f6-69a91c5926b4";

    private final static String storeId = "bd40dd6d-e021-443c-8b2c-ddf8db770857";

    private final static String userId = "1c29449b-7241-45fa-bdf4-6d8e64a2625b";

    @Test
    public void testSaveBatch(){

        StoreTableAreaBatchSaveReq req = new StoreTableAreaBatchSaveReq();
        req.setMerchant_id(merchantId);
        req.setStoreId(storeId);
        req.setId(userId);

        storeTableAreaAppEdgeRpcService.saveBatch(req);
    }

    @Test
    public void testList() {

        AppEdgeStoreBaseRequest request = new AppEdgeStoreBaseRequest();
        request.setMerchant_id(merchantId);
        request.setStoreId(storeId);
        request.setUc_user_id(userId);

        List<StoreTableAreaRpcVO> result = storeTableAreaAppEdgeRpcService.list(request);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testGroup() {

        StoreTableAreaQueryReq request = new StoreTableAreaQueryReq();
        request.setMerchant_id(merchantId);
        request.setStoreId(storeId);
        request.setUc_user_id(userId);

        List<StoreAreaTableGroupVO> result = storeTableAreaAppEdgeRpcService.group(request);
        System.out.println(JSON.toJSONString(result));

    }

    @Test
    public void testSave() {

        StoreTableAreaSaveRpcReq req = new StoreTableAreaSaveRpcReq();
        req.setMerchant_id(merchantId);
        req.setStoreId(storeId);
        req.setId(userId);
        req.setAreaName("五楼");

        storeTableAreaAppEdgeRpcService.save(req);

    }

}
