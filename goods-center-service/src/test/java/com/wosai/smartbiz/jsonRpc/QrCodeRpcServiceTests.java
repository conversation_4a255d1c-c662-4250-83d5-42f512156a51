package com.wosai.smartbiz.jsonRpc;

import com.wosai.smartbiz.Application;
import com.wosai.smartbiz.dto.req.qrcode.QrcodeUnbindRequest;
import com.wosai.smartbiz.gds.jsonrpc.QrCodeAppEdgeRpcService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @create 2021/3/10
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles("beta")
public class QrCodeRpcServiceTests {

    @Autowired
    private QrCodeAppEdgeRpcService qrCodeAppEdgeRpcService;

    @Test
    public void testUnbind() {

        QrcodeUnbindRequest request = new QrcodeUnbindRequest();
        request.setMerchant_id("c1063b47-f70c-4d4d-9997-0e1fd5d137b7");
        request.setBiz_store_id("2c3781be-a74d-46b0-a28a-fe1455f9ad6b");
        request.setBindRelationId(342L);

        Boolean result = qrCodeAppEdgeRpcService.unbind(request);
        System.out.println(result);
    }

}
