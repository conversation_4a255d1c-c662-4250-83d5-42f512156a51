package com.wosai.smartbiz.jsonRpc.retail;

import com.alibaba.fastjson.JSON;
import com.wosai.smart.goods.dto.CursorResult;
import com.wosai.smartbiz.Application;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.base.apisix.cashier.CashierUser;
import com.wosai.smartbiz.gds.dto.CashierRetailProduct;
import com.wosai.smartbiz.retail.dto.CashierRetailProductSyncParam;
import com.wosai.smartbiz.retail.jsonrpc.CashierRetailProductRpcService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 零售收银机的商品服务单测
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
public class CashierRetailProductRpcServiceTest {

    @Autowired
    private CashierRetailProductRpcService cashierRetailProductRpcService;

    @Test
    public void testSyncAll() {

        CashierApiRequest<CashierRetailProductSyncParam> request = new CashierApiRequest<>();
        CashierRetailProductSyncParam param = new CashierRetailProductSyncParam();
        request.setBody(param);
        request.setUser(new CashierUser());
        param.setStoreId("2f041d04-d889-48bc-a5aa-955a6c57876f");
        param.setMinWaterLine(0L);
        param.setPageSize(100);
        CursorResult<CashierRetailProduct> result = cashierRetailProductRpcService.syncAll(request);
        System.out.println("结果：" + JSON.toJSONString(result));

    }

}
