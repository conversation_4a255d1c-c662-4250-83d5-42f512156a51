package com.wosai.smartbiz.jsonRpc;

import com.alibaba.fastjson.JSON;
import com.wosai.market.mcc.api.dto.request.FindConfigByNameRequest;
import com.wosai.market.mcc.api.service.ConfigRemoteService;
import com.wosai.smartbiz.Application;
import com.wosai.smartbiz.dto.req.remark.RemarkRpcQueryReq;
import com.wosai.smartbiz.dto.req.remark.RemarkRpcSaveReq;
import com.wosai.smartbiz.dto.vo.remark.RemarkVO;
import com.wosai.smartbiz.enums.MccConfigEnum;
import com.wosai.smartbiz.gds.jsonrpc.RemarkRpcService;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/1/7
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles("beta")
public class RemarkRpcServiceTests {

    @Autowired
    private RemarkRpcService remarkRpcService;

    @Autowired
    private ConfigRemoteService sqbConfigRemoteService;

    private static final String merchantId = "eb3bda9a-372a-41b8-a2f6-69a91c5926b4";

    private static final String storeId = "2c3781be-a74d-46b0-a28a-fe1455f9ad6b";

    @Test
    public void testSave() {

        RemarkRpcSaveReq req = new RemarkRpcSaveReq();
        req.setMerchant_id(merchantId);
        req.setType(MccConfigEnum.ORDER_REMARK.name());
        req.setTextList(Lists.newArrayList("AAA", "BBB", "CCC"));
        remarkRpcService.save(req);
    }

    @Test
    public void testQuery() {

        RemarkRpcQueryReq req = new RemarkRpcQueryReq();
        req.setMerchant_id(merchantId);
        req.setType(MccConfigEnum.ORDER_REMARK.name());

        List<RemarkVO> result = remarkRpcService.listByQuery(req);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testMcc() {

        FindConfigByNameRequest request = new FindConfigByNameRequest();
        request.setAppId("ufood");
        request.setOwnerType("storeId");
        request.setOwnerId("744ff81b54c2-ce8a-8024-22fe-70a3548d");
        request.setName("XXXX");

        System.out.println(sqbConfigRemoteService.findByName(request));
    }

}
