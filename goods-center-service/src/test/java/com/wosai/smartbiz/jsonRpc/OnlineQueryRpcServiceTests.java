package com.wosai.smartbiz.jsonRpc;

import com.alibaba.fastjson.JSON;
import com.wosai.data.crow.api.model.query.SingleTagEntityRecord;
import com.wosai.data.crow.api.service.OnlineQueryService;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.service.StoreService;
import com.wosai.smartbiz.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @create 2022/6/22
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles("beta")
public class OnlineQueryRpcServiceTests {

    @Autowired
    private OnlineQueryService onlineQueryService;

    @Test
    public void getSingleTagEntityRecordById() {
        SingleTagEntityRecord tag = onlineQueryService.getSingleTagEntityRecordById(
                "446143d3-697e-49bf-a12c-441c7e2447f0",
                "76be16ce-c9cd-4d12-a1de-03c13cc6f86f",
                Arrays.asList("85443d5d-ad5c-47d0-809e-e30be797509c"),
                false);
        System.out.println(JSON.toJSONString(tag));
    }



}
