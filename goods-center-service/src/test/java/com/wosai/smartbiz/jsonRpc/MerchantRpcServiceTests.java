package com.wosai.smartbiz.jsonRpc;

import com.alibaba.fastjson.JSON;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.mc.service.StoreService;
import com.wosai.smartbiz.Application;
import com.wosai.smartbiz.uc.constants.Constants;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @create 2021/3/10
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles("beta")
public class MerchantRpcServiceTests {

    @Autowired
    private MerchantService sqbMerchantService;

}
