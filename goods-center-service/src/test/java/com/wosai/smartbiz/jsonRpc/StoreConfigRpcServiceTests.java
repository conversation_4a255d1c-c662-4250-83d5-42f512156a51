package com.wosai.smartbiz.jsonRpc;

import com.alibaba.fastjson.JSON;
import com.wosai.market.mcc.api.dto.request.CreateConfigRequest;
import com.wosai.market.mcc.api.enums.AppId;
import com.wosai.market.mcc.api.enums.OwnerType;
import com.wosai.market.mcc.api.service.ConfigRemoteService;
import com.wosai.smartbiz.Application;
import com.wosai.smartbiz.dto.ConfigNameValue;
import com.wosai.smartbiz.dto.req.config.StoreConfigQueryReq;
import com.wosai.smartbiz.dto.req.config.StoreConfigSaveRpcReq;
import com.wosai.smartbiz.enums.MccConfigEnum;
import com.wosai.smartbiz.goods.constants.MccConfigConstants;
import com.wosai.smartbiz.gds.jsonrpc.StoreConfigRpcService;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @create 2021/1/19
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles("beta")
public class StoreConfigRpcServiceTests {

    @Autowired
    private StoreConfigRpcService storeConfigRpcService;

    private static final String merchantId = "eb3bda9a-372a-41b8-a2f6-69a91c5926b4";

    private static final String storeId = "bd40dd6d-e021-443c-8b2c-ddf8db770857";

    @Test
    public void testSave() {

        StoreConfigSaveRpcReq req = new StoreConfigSaveRpcReq();
        req.setMerchant_id(merchantId);
        req.setStoreId(storeId);
        req.setConfigList(Lists.newArrayList(
                ConfigNameValue.builder()
                        .name(MccConfigEnum.MANUAL_INPUT_TAKEOUT_NO.getName())
                        .value(MccConfigConstants.SWITCH_CLOSE)
                        .build(),
                ConfigNameValue.builder()
                        .name(MccConfigEnum.CASHIER_AUTO_SETTLE_AFTER_PAY.getName())
                        .value("1")
                        .build(),
                ConfigNameValue.builder()
                        .name(MccConfigEnum.AUTO_CLEAN_AFTER_SETTLE.getName())
                        .value("0")
                        .build(),
                ConfigNameValue.builder()
                        .name(MccConfigEnum.ORDER_SEQ_START_VALUE.getName())
                        .value("1")
                        .build(),
                ConfigNameValue.builder()
                        .name(MccConfigEnum.ORDER_SEQ_END_VALUE.getName())
                        .value("200")
                        .build()
        ));

        storeConfigRpcService.save(req);

    }

    @Test
    public void testList() {

        StoreConfigQueryReq req = new StoreConfigQueryReq();
        req.setMerchant_id(merchantId);
        req.setStoreId(storeId);
        req.setConfigNameList(Lists.newArrayList(
                MccConfigEnum.ORDER_SEQ_START_VALUE.getName(),
                MccConfigEnum.ORDER_SEQ_END_VALUE.getName(),
                MccConfigEnum.MANUAL_INPUT_TAKEOUT_NO.getName(),
                MccConfigEnum.CASHIER_AUTO_SETTLE_AFTER_PAY.getName(),
                MccConfigEnum.AUTO_CLEAN_AFTER_SETTLE.getName(),
                MccConfigEnum.CASHIER_MODE.getName()
        ));

        System.out.println(JSON.toJSONString(storeConfigRpcService.listByName(req)));
    }

    @Autowired
    private ConfigRemoteService sqbConfigRemoteService;

    @Test
    public void testNull() {

        CreateConfigRequest request = new CreateConfigRequest();
        request.setAppId(AppId.UFOOD.getAppId());
        request.setOwnerType(OwnerType.STORE_ID.getOwnerType());
        request.setOwnerId("2c3781be-a74d-46b0-a28a-fe1455f9ad6b");
        request.setName("cash_register");
        request.setValue(null);
        request.setEnabled(Boolean.FALSE);

        sqbConfigRemoteService.upsertConfigByName(request);

    }

}
