package com.wosai.smartbiz.jsonRpc;

import com.alibaba.fastjson.JSON;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.service.StoreService;
import com.wosai.smartbiz.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @create 2021/3/10
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles("beta")
public class StoreRpcServiceTests {

    @Autowired
    private StoreService sqbStoreService;

    @Test
    public void getStoreById() {
        String storeId = "dc4d59587f7a-9fa8-5ba4-b01e-a8c897da";
        StoreInfo storeInfo = sqbStoreService.getStoreById(storeId, null);
        System.out.println(JSON.toJSONString(storeInfo));
    }

}
