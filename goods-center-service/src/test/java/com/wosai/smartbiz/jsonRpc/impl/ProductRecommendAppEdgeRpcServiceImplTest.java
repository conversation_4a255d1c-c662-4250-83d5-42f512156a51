package com.wosai.smartbiz.jsonRpc.impl;

import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.dto.ProductRecommendDTO;
import com.wosai.smartbiz.dto.req.ProductRecommendRequest;
import com.wosai.smartbiz.goods.query.ItemQuery;
import com.wosai.smartbiz.gds.jsonrpc.impl.ProductRecommendAppEdgeRpcServiceImpl;
import com.wosai.smartbiz.gds.service.ProductRecommendService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * <AUTHOR>
 * @BelongProjecet goods-center
 * @BelongPackage com.wosai.smartbiz.jsonrpc.impl
 * @Copyleft 2013-3102
 * @Date 2022/6/24 13:55
 * @Description
 */

class ProductRecommendAppEdgeRpcServiceImplTest {
    @Mock
    ProductRecommendService productRecommendService;
    @InjectMocks
    ProductRecommendAppEdgeRpcServiceImpl productRecommendAppEdgeRpcServiceImpl;


    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    @DisplayName("门店 ID为空")
    void testSaveStoreProductRecommend() {
        // assert throw exception
        ParamException paramException = assertThrows(ParamException.class, () -> {
            ProductRecommendRequest request = new ProductRecommendRequest(Arrays.<ProductRecommendDTO>asList(new ProductRecommendDTO("spuId", Integer.valueOf(0))), Long.valueOf(1), new ItemQuery(), true, "orderType");
            productRecommendAppEdgeRpcServiceImpl.saveStoreProductRecommend(request);
        });
        // assert exception message
        Assertions.assertEquals("请选择一个门店进行推荐商品", paramException.getMessage());
    }

    @ParameterizedTest
    @ValueSource(ints = {16, 17, 20, 31})
    @DisplayName("推荐商品数量超过配置值")
    void testSaveStoreProductRecommendOverMax(int recomendCount) {
        // 用反射强制给最大值配置一个值 15
        ReflectionTestUtils.setField(productRecommendAppEdgeRpcServiceImpl, "productRecommendSizeMax", 15);
        ProductRecommendRequest request = new ProductRecommendRequest(Arrays.<ProductRecommendDTO>asList(new ProductRecommendDTO("spuId", Integer.valueOf(0))), Long.valueOf(1), new ItemQuery(), true, "orderType");
        request.setStoreId("fakeid");
        List<ProductRecommendDTO> recommends = new ArrayList<>();
        for (int i = 0; i < recomendCount; i++) {
            recommends.add(new ProductRecommendDTO("spuId", Integer.valueOf(i)));
        }
        request.setRecommends(recommends);

        // assert throw exception
        ParamException paramException = assertThrows(ParamException.class, () -> {
            productRecommendAppEdgeRpcServiceImpl.saveStoreProductRecommend(request);
        });
        // assert exception message
        Assertions.assertEquals("推荐商品数量超过限制", paramException.getMessage());
    }

    @ParameterizedTest
    @ValueSource(ints = {1,5,10,15})
    @DisplayName("推荐商品数量小于等于配置值")
    void testSaveStoreProductRecommendUnderMax(int recomendCount) {
        // 用反射强制给最大值配置一个值 15
        ReflectionTestUtils.setField(productRecommendAppEdgeRpcServiceImpl, "productRecommendSizeMax", 15);
        ProductRecommendRequest request = new ProductRecommendRequest(Arrays.<ProductRecommendDTO>asList(new ProductRecommendDTO("spuId", Integer.valueOf(0))), Long.valueOf(1), new ItemQuery(), true, "orderType");
        request.setStoreId("fakeid");
        List<ProductRecommendDTO> recommends = new ArrayList<>();
        for (int i = 0; i < recomendCount; i++) {
            recommends.add(new ProductRecommendDTO("spuId", Integer.valueOf(i)));
        }
        request.setRecommends(recommends);
        productRecommendAppEdgeRpcServiceImpl.saveStoreProductRecommend(request);
        Mockito.verify(productRecommendService, Mockito.times(1)).saveStoreProductRecommend(anyString(), anyList());
    }


}

