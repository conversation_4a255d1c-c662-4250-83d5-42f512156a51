package com.wosai.smartbiz.user.terminal;

import com.alibaba.fastjson.JSON;
import com.wosai.smartbiz.Application;
import com.wosai.smartbiz.hills.Client;
import com.wosai.smartbiz.hills.Request;
import com.wosai.smartbiz.hills.Response;
import com.wosai.smartbiz.hills.constant.HttpHeader;
import com.wosai.smartbiz.hills.constant.SystemHeader;
import com.wosai.smartbiz.hills.enums.Method;
import com.wosai.upay.core.service.TerminalService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles("beta")
public class UserTerminalTest {
    private final Logger LOGGER = LoggerFactory.getLogger(UserTerminalTest.class);
    @Autowired
    private TerminalService sqbCoreBusinessTerminalService;

    @Test
    public void test(){
        try {
            String storeId = "f1bf7e40-a848-4ff4-ba24-b46000c31069";
            String deviceFp = "cashier_" + storeId;
            String createTerminalSnCreatePath = "/api/terminal/createActivitedTerminal7";
            Map<String, String> headers = new HashMap<>();
            headers.put(HttpHeader.HTTP_HEADER_ACCEPT, "application/json");
            headers.put(SystemHeader.X_Ca_Stage, "RELEASE");

            Request request = new Request(Method.POST_FORM, "http://192.168.100.27:3001", createTerminalSnCreatePath, "digitalBusinessII", "d46e&kRg8r3Gb%PQ", 5000);
            request.setHeaders(headers);
            request.setSignHeaderPrefixList(new ArrayList<>());

            Map<String, String> bodyMap = new HashMap<>();
            bodyMap.put("clientSn", deviceFp);
            bodyMap.put("vendorAppAppid", "2020102600003264");

            bodyMap.put("name", "帽帽门店二");
            bodyMap.put("sdkVer", "1.0.0");
            bodyMap.put("deviceFp", deviceFp);
            bodyMap.put("storeSn", "21590000000717780");
            bodyMap.put("osVer", "1.0.0");
            bodyMap.put("longitude", "120.103104");
            bodyMap.put("latitude", "30.308587");

            request.setBodys(bodyMap);

            LOGGER.info("terminal_create_request, query:{}, body:{}, requestHeader:{}", null, JSON.toJSONString(bodyMap), JSON.toJSONString(headers));
            //调用服务端
            Response response = Client.execute(request);
            LOGGER.info("terminal_create_response: {}", JSON.toJSONString(response));
        } catch (Exception ex) {
            ex.printStackTrace();
        }

    }



    @Test
    public void getTerminalByDeviceFingerprint(){
        try {
            String storeId = "f1bf7e40-a848-4ff4-ba24-b46000c31069";
            String deviceFp = "cashier_" + storeId;
            Map<String, Object> map = sqbCoreBusinessTerminalService.getTerminalByDeviceFingerprint(deviceFp);
            LOGGER.info("getTerminalByDeviceFingerprint deviceInfo: {}", JSON.toJSONString(map));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}
