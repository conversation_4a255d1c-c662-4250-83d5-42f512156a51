package com.wosai.smartbiz.user.center;

import com.wosai.smartbiz.Application;
import com.wosai.smartbiz.uc.manager.MerchantV2Service;
import com.wosai.smartbiz.uc.manager.StoreV2Service;
import com.wosai.smartbiz.user.user.vo.CashierStoreVO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles("beta")
public class Login2ServiceTest {

    @Autowired
    private MerchantV2Service merchantV2Service;
    @Autowired
    private StoreV2Service storeV2Service;

    @Test
    public void testIsStaffMerchantFalse(){
        String storeId = "c1063b47-f70c-4d4d-9997-0e1fd5d137b1";
        CashierStoreVO cashierStoreVO = storeV2Service.getCashierStoreByStoreId(storeId);
        boolean staffMerchant = merchantV2Service.staffMerchant(cashierStoreVO);
        Assert.assertFalse(staffMerchant);
    }

    @Test
    public void testIsStaffMerchantTrue(){
        String storeId = "c1063b47-f70c-4d4d-9997-0e1fd5d137b1";
        CashierStoreVO cashierStoreVO = storeV2Service.getCashierStoreByStoreId(storeId);
        boolean staffMerchant = merchantV2Service.staffMerchant(cashierStoreVO);
        Assert.assertTrue(staffMerchant);
    }
}
