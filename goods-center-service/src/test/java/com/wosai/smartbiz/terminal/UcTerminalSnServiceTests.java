package com.wosai.smartbiz.terminal;

import com.alibaba.fastjson.JSON;
import com.wosai.smartbiz.Application;
import com.wosai.smartbiz.uc.dto.PaymentTerminalDTO;
import com.wosai.smartbiz.uc.services.UcTerminalSnService;
import com.wosai.smartbiz.user.user.request.UcCreateTerminalSnRequest;
import com.wosai.smartbiz.user.user.request.UcUnbindTerminalSnRequest;
import com.wosai.upay.core.service.TerminalService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2021/9/16
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles(value = "beta")
public class UcTerminalSnServiceTests {

    @Autowired
    private UcTerminalSnService ucTerminalSnService;

    @Autowired
    private TerminalService sqbCoreBusinessTerminalService;

    @Test
    public void testCreateTerminalSnV2() {
        String storeId = "123";
        UcCreateTerminalSnRequest terminalSnRequest = new UcCreateTerminalSnRequest();
        terminalSnRequest.setVendorAppAppid("2020102600003264");
        terminalSnRequest.setStoreSn("21590000000709523");
        terminalSnRequest.setClientSn("DA08P94K40066");
        terminalSnRequest.setDeviceFp("DA08P94K40066");
        terminalSnRequest.setName("收银机支付终端");
        terminalSnRequest.setOsVer("1.0.0");
        terminalSnRequest.setSdkVer("1.0.0");

        PaymentTerminalDTO paymentTerminalDTO = ucTerminalSnService.createTerminalSnV2(storeId, terminalSnRequest);
        System.out.println(JSON.toJSONString(paymentTerminalDTO));
    }

    @Test
    public void testUnbindTerminalSnV2() {
        UcUnbindTerminalSnRequest unbindTerminalSnRequest = new UcUnbindTerminalSnRequest();
        unbindTerminalSnRequest.setVendorAppAppid("2020102600003264");
        unbindTerminalSnRequest.setDeviceFp("DA08P94K40066");

        Boolean unbindResult = ucTerminalSnService.unbindTerminalSnV2(unbindTerminalSnRequest);
        System.out.println(unbindResult);
    }


    @Test
    public void testGetTerminalByDeviceFp(){
        String deviceFp = "DA08P94K40066";
        Map map = sqbCoreBusinessTerminalService.getTerminalByDeviceFingerprint(deviceFp);
        System.out.println(JSON.toJSONString(map));
    }
}
