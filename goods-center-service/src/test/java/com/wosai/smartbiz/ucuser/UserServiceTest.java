package com.wosai.smartbiz.ucuser;

import com.alibaba.fastjson.JSON;
import com.wosai.smartbiz.Application;
import com.wosai.uc.dto.CreateUcUserReq;
import com.wosai.uc.dto.UcUserInfoResp;
import com.wosai.uc.service.UcUserService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles("beta")
public class UserServiceTest {

    @Autowired
    private UcUserService sqbUcUserService;

    @Test
    public void testCreateUcUser() {

        CreateUcUserReq createUcUserReq = CreateUcUserReq.builder()
                .identifier("13958099751")
                .identity_type(1)
                .password("a123456")
                .app("trade")
                .nickname("小红帽2")
                .gender(0)
                .avatar("https://wosai-images.oss-cn-hangzhou.aliyuncs.com/00/002e896626e003bd726065edec011caa65d5b3.jpg")
                .status(1)
                .build();
        UcUserInfoResp ucUserInfoResp = sqbUcUserService.createUcUser(createUcUserReq);
        Assert.notNull(ucUserInfoResp);
        System.out.println(JSON.toJSONString(ucUserInfoResp));
    }

    @Test
    public void testGetUcUserInfoById() {

        UcUserInfoResp ucUserInfoResp = sqbUcUserService.getUcUserInfoById("63a42218-9539-4de0-9c88-2f6c7fc810ac");
        Assert.notNull(ucUserInfoResp);
        System.out.println(JSON.toJSONString(ucUserInfoResp));
    }
}
