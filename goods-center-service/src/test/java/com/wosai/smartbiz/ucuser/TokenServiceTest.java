package com.wosai.smartbiz.ucuser;

import com.alibaba.fastjson.JSON;
import com.wosai.smartbiz.Application;
import com.wosai.uc.dto.RegisterTokenReq;
import com.wosai.uc.dto.TokenInfo;
import com.wosai.uc.dto.login.LoginReq;
import com.wosai.uc.service.UcTokenInnerService;
import com.wosai.uc.service.UcTokenService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles("beta")
public class TokenServiceTest {

    @Autowired
    com.wosai.uc.service.LoginService sqbLoginService;

    @Autowired
    UcTokenService sqbUcTokenService;

    @Autowired
    UcTokenInnerService sqbUcTokenInnerService;

    @Test
    public void testLogin() {
        try {
            LoginReq loginReq = new LoginReq();
            loginReq.setIdentifier("13958099751");
            loginReq.setPassword("a123456");
            loginReq.setApp("trade");

            TokenInfo tokenInfo = sqbLoginService.login(loginReq);
            Assert.notNull(tokenInfo);
            System.out.println("tokenInfo:" + JSON.toJSONString(tokenInfo));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    @Test
    public void testRegisterToken() {
        try {
            RegisterTokenReq registerTokenReq = new RegisterTokenReq();
            registerTokenReq.setUc_user_id("63a42218-9539-4de0-9c88-2f6c7fc810ac");
            registerTokenReq.setDevice_fingerprint("123456789");
            registerTokenReq.setToken_valid_times(1800L);

            TokenInfo tokenInfo = sqbUcTokenInnerService.registerToken(registerTokenReq);
            Assert.notNull(tokenInfo);
            System.out.println("tokenInfo:" + JSON.toJSONString(tokenInfo));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    @Test
    public void testPub(){
        try {
            String pub = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAx4/5dVVer4S95dkX15g7ohJM3bdoxFnb6ozCI2dMm2is7bT7v9SvzGIZXoSU7OxxVcfHBzJWzdtEe56ARF9g0dEyuwILISzkklCkLdwgXvFcZm7xbM1YApTMaCjEs4zfDCXVl0Dmo+PjL41sGUT9yiImKlirHXu/w2RvkPwIR+VOceUPY2CFw974EROw2EPKQ4+DCVE6ynSzmVX9zqWrceywgTv+S9mNd9jV2rKkFFEWgp4sJxfaaYCJKdfeB/cxZVY5Iw1igQzjLuXgej4AZJwPlnYeXZePr3KfgHx+M6NQ6bwNgQ9X841IWr4c6eAezRGgN+C7yMWgXCeim4miuQIDAQAB";

            PublicKey pk = getPublicKey(pub);

            System.out.println("aaaaa:" + pk.toString());
        } catch (Exception ex) {

            System.out.println("bbbbb:" + ex.getMessage());
            ex.printStackTrace();
        }
    }

    @Test
    public void testPri(){
        try {
            String pri = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDHj/l1VV6vhL3l2RfXmDuiEkzdt2jEWdvqjMIjZ0ybaKzttPu/1K/MYhlehJTs7HFVx8cHMlbN20R7noBEX2DR0TK7AgshLOSSUKQt3CBe8VxmbvFszVgClMxoKMSzjN8MJdWXQOaj4+MvjWwZRP3KIiYqWKsde7/DZG+Q/AhH5U5x5Q9jYIXD3vgRE7DYQ8pDj4MJUTrKdLOZVf3Opatx7LCBO/5L2Y132NXasqQUURaCniwnF9ppgIkp194H9zFlVjkjDWKBDOMu5eB6PgBknA+Wdh5dl4+vcp+AfH4zo1DpvA2BD1fzjUhavhzp4B7NEaA34LvIxaBcJ6KbiaK5AgMBAAECggEBALhl696kdKqAYKwbDnHTo/6dJFOW3wLX+WJgkB8Am4PalDfNjPXzxEF5v/FbVvUZgZv+zoRy2ky7OCt2CRWkgCVARb0RJVGRVt9wDlSl229/XJ72ErUzhQhVkA8K5L74Az4JU7TAbp2vvEQ7TFCJNGzrYxKr/elvaIFWs9RnXgt3TfVmcYufu60ilB4YtF7oCUywXymlY1mz9XbQAV2WyWLUm+3nCRWfC60jdUulKSnDjy+rPoxOo/j5lZ5akDiR7hMJW3iA09fx/9NbgCS1efPyFsmOA8p+nJ471JnKEbRzj6dej/1lRrVt13h83+KIHujzI5PTpK5uXn69YWvD0+UCgYEA/Amq/nuyKodEc5k93L7SzYbUzvqOewRrUXcN62ek8+3dlRKdXYZl+V4Dv0/AXzElXWMbxb4SaWX3/v19KK7uV1OMo8psMfA7Nir7XokSOW1Une9jogd2bHpysg7rzLSHw4zXnDpgpXfgsTsHNJAZeb7V73GWGca2wOL8vpXDsZ8CgYEAyrMePcqe39Jp9Lufa2vt6B31CcXV22l+K11JMP935pKqLsU5BrVlKn2clR6q33r1Ingvqgvx/5S9HRoPvsy7+B4+azH24d+J3ZdSFfS3Fs/Ldpd9ov57CH6eTeo9BamLq+OttndsaBPWgXG7xIAcNcs1Cd8y1aQ2nYEBdheQvKcCgYBBCvmu/RsosInwzz5Qxoo+cipfU/gLNUPA3VmhQ7klHmDNxOUJqtKMPT/4UkuSBWvgIuCa8odr3d3XV3HCWwq1TKGB745mgNm3C/adGgCzA00QCSSMPyLHsf4Bh6pAxOI4jBNkhQhGC57X/iTI4ttAOQia55RWCJtHQgz53kqHfwKBgQC4Ckm68O2vauypOHFWp3gq+Igzh1zVCsNzvA9oXYwP3bqHHKraXHiuS76P5nhe0NUhpt4gFxhyZioO8kq5gkpozYz+5y6xhrU2bN4q8TYLkD+8PW/lPgmXfN9e/v7I1VfriZ1/vo6/MMPiY9GIaIk0jnxpqi4NrDuqZiddgaf4oQKBgDz/2yP57IFalxXXUj074QwkL0XJ3mywTHezFqkO0IeOFmn/ELHiVW+/cDmmvHqi3nSfTUcAqUV1lOWQxCodN06lDCxl9FsZnp8gbZAFhEXrhINhR/wh5fua46BYkKf1mYog/lxzq6c6f12Fs2T4AjYXrALyQhBIEbnNE7LuyBo1";

            PrivateKey pr = getPrivateKey(pri);

            System.out.println("11111:" + pr.toString());
        } catch (Exception ex) {

            System.out.println("33333:" + ex.getMessage());
            ex.printStackTrace();
        }
    }



    /**
     * 解码PublicKey
     * @param key
     * @return
     */
    private PublicKey getPublicKey(String key) {
        try {
            byte[] byteKey = Base64.getDecoder().decode(key);
            X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(byteKey);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            return keyFactory.generatePublic(x509EncodedKeySpec);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
    /**
     * 解码PrivateKey
     * @param key
     * @return
     */
    private PrivateKey getPrivateKey(String key) {
        try {
            byte[] byteKey = Base64.getDecoder().decode(key);
            PKCS8EncodedKeySpec x509EncodedKeySpec = new PKCS8EncodedKeySpec(byteKey);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            return keyFactory.generatePrivate(x509EncodedKeySpec);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
