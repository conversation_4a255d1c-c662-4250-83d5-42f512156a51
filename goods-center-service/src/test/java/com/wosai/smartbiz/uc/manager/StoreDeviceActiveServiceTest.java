package com.wosai.smartbiz.uc.manager;

import com.wosai.market.awesome.message.enums.NoticeType;
import com.wosai.smartbiz.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles("beta")
public class StoreDeviceActiveServiceTest {
    @Autowired
    private StoreDeviceActiveService storeDeviceActiveService;

    @Test
    public void sendStoreCashierMessageTest(){
        Map<String, Object> contentMap = new HashMap<>();
        contentMap.put("storeId", "7f63c25a-2ff9-4480-b0b4-8784cfca251b");
        contentMap.put("unbindTime", System.currentTimeMillis());
        storeDeviceActiveService.sendStoreCashierMessage("385cd393-53ad-4a6a-9515-1670a580ef21", "7f63c25a-2ff9-4480-b0b4-8784cfca251b", NoticeType.CASHIER_UNBIND, contentMap);
    }

}