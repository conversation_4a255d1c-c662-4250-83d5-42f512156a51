package com.wosai.smartbiz.uc.manager;

import com.wosai.smartbiz.Application;
import com.wosai.smartbiz.goods.domain.SoundCashDO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles("local")
public class SoundCashServiceTest {
    @Autowired
    private SoundCashService soundCashService;

    @Test
    public void createTest() {
        SoundCashDO soundCashDO = new SoundCashDO();
        soundCashDO.setStoreId("storeId");
        soundCashService.create(soundCashDO);
    }

    @Test
    public void unbindByCashSn() {
        boolean res = soundCashService.unbindByCashSn("cash000");
        log.info("res:{}", res);
    }
}
