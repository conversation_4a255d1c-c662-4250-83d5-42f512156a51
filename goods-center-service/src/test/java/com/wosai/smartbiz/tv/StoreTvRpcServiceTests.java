package com.wosai.smartbiz.tv;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wosai.smartbiz.Application;
import com.wosai.smartbiz.base.utils.WebUtil;
import com.wosai.smartbiz.gds.service.StoreTvRpcService;
import com.wosai.smartbiz.gds.vo.StoreTvDeviceMqttInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.util.List;

/**
 * @author: redhat
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles("beta")
@Slf4j
public class StoreTvRpcServiceTests {

    @Autowired
    private StoreTvRpcService storeTvRpcService;

    @Test
    public void testCheckTvOnline() {
        String checkOnlineUrl = "http://rmqx.iwosai.com:8090/api/v3/connections";
        String tvDeviceClientIdPrefix = "mk-tv-";
        String deviceSn = "EMULATOR30X2X6X0";
        String checkUrl = new StringBuilder(checkOnlineUrl).append("/").append(tvDeviceClientIdPrefix).append(deviceSn).toString();
        String response = null;
        try {
            response = WebUtil.doGet(checkUrl, null);
        } catch (IOException e) {
            e.printStackTrace();
        }
        JSONObject jsonObject = JSON.parseObject(response);
        log.info("response:{}", response);
        List<StoreTvDeviceMqttInfoVO> list =  jsonObject.getJSONArray("data").toJavaList(StoreTvDeviceMqttInfoVO.class);
        log.info("list:{}", JSON.toJSONString(list));

        log.info("connected:{}", list.get(0).getConnected());
    }
}
