package com.wosai.smartbiz;

import com.wosai.smartbiz.gds.service.NotifyRpcService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @create 2021/3/1
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles("beta")
public class NotifyRpcServiceTests {

    @Autowired
    private NotifyRpcService notifyRpcService;

    @Test
    public void testProductChange(){
        //notifyRpcService.notifyProductChange(null, "bd40dd6d-e021-443c-8b2c-ddf8db770857");

        notifyRpcService.notifyCategoryChange(null, "bd40dd6d-e021-443c-8b2c-ddf8db770857");
    }

}
