package com.wosai.smartbiz.jjz;

import com.alibaba.fastjson.JSON;
import com.wosai.smartbiz.Application;
import com.wosai.smartbiz.uc.jsonrpc.MerchantContractRemoteService;
import com.wosai.smartbiz.uc.jsonrpc.QrCodeRemoteService;
import com.wosai.smartbiz.uc.query.RemoteQrcodeBindQuery;
import com.wosai.smartbiz.uc.vo.RemoteQrCodeVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles("beta")
public class QrCodeServiceTest {

    @Autowired
    private QrCodeRemoteService qrCodeRemoteService;
    @Autowired
    private MerchantContractRemoteService merchantContractRemoteService;

    @Test
    public void testBindQrcode() {
        try {
            String qrCodeUrl = "http://99zhe.test.shouqianba.com/d/iqYpH";
            String sqbMerchantId = "b292d6b8-b355-4e5d-ba55-72c408512078";
            String sqbStoreId = "75f2bb2a73c2-046b-ba04-0291-33048a4a";

            RemoteQrcodeBindQuery bindQuery = new RemoteQrcodeBindQuery();
            bindQuery.setQrCoderUrl(qrCodeUrl);
            bindQuery.setMerchantId(sqbMerchantId);
            bindQuery.setStoreId(sqbStoreId);
            bindQuery.setBusinessId("12345");
            bindQuery.setBusinessName("1号桌");



            RemoteQrCodeVO remoteQrCodeVO = qrCodeRemoteService.bind(bindQuery, null);
            Assert.notNull(remoteQrCodeVO);
            System.out.println("remoteQrCodeVO:" + JSON.toJSONString(remoteQrCodeVO));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    @Test
    public void testUnBindQrcode() {
        try {
//            String qrCode = "jjzt20092309293000100594";
//            String sqbMerchantId = "c1063b47-f70c-4d4d-9997-0e1fd5d137b7";
//
//            boolean unBindResult = qrCodeRemoteService.unbind(qrCode, sqbMerchantId);
//            Assert.isTrue(unBindResult);
//            System.out.println("unBindResult:" + unBindResult);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }


    public void testGenQRCodeImage() {
        try {
//            String qrCode = "";
//            String sqbMerchantId = "";
//
//            boolean unBindResult = qrCodeRemoteService.unbind(qrCode, sqbMerchantId);
//            Assert.isTrue(unBindResult);
//            System.out.println("unBindResult:" + unBindResult);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}
