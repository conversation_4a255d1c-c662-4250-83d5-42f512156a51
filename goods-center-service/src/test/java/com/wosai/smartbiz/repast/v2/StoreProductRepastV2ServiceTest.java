package com.wosai.smartbiz.repast.v2;

import com.alibaba.fastjson.JSON;
import com.wosai.market.dto.product.ProductDetail;
import com.wosai.smartbiz.Application;
import com.wosai.smartbiz.base.enums.YesNoEnum;
import com.wosai.smartbiz.converter.gds.repast.ProductV2ConvertUtils;
import com.wosai.smartbiz.goods.query.repast.StoreProductCursorQuery;
import com.wosai.smartbiz.gds.service.ProductSyncV2Service;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2020/12/29
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles("beta")
public class StoreProductRepastV2ServiceTest {

    @Autowired
    private ProductSyncV2Service productSyncV2Service;

    private final static String merchantId = "eb3bda9a-372a-41b8-a2f6-69a91c5926b4";

    private final static String storeId = "bd40dd6d-e021-443c-8b2c-ddf8db770857";

    @Test
    public void testCursor() {

        StoreProductCursorQuery query = StoreProductCursorQuery.builder()
                .merchantId(merchantId)
                .storeId(storeId)
                .deleteMark(YesNoEnum.N)
                .minWaterMark(579L)
                .maxWaterMark(589L)
                .pageSize(500)
                .build();

        List<ProductDetail> detailList = productSyncV2Service.syncProductToCashier(query);
        System.out.println(JSON.toJSONString(ProductV2ConvertUtils.buildStoreProductVO(detailList)));
    }

}
