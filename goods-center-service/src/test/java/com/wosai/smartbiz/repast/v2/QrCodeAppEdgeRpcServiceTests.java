package com.wosai.smartbiz.repast.v2;

import com.alibaba.fastjson.JSON;
import com.wosai.smartbiz.Application;
import com.wosai.smartbiz.dto.req.qrcode.QrcodeBindRequest;
import com.wosai.smartbiz.dto.req.qrcode.QrcodeQueryRequest;
import com.wosai.smartbiz.dto.req.qrcode.QrcodeUnbindRequest;
import com.wosai.smartbiz.gds.jsonrpc.QrCodeAppEdgeRpcService;
import com.wosai.smartbiz.uc.vo.QrCodeVO;
import com.wosai.smartbiz.user.user.enums.QrCodeBusinessType;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/3/24
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles("beta")
public class QrCodeAppEdgeRpcServiceTests {

    @Autowired
    private QrCodeAppEdgeRpcService qrCodeAppEdgeRpcService;

    @Test
    public void testBind() {

        QrcodeBindRequest request = new QrcodeBindRequest();
        request.setMerchant_id("eb3bda9a-372a-41b8-a2f6-69a91c5926b4");
        request.setBiz_store_id("bd40dd6d-e021-443c-8b2c-ddf8db770857");
        request.setQrcodeUrl("http://99zhe.test.shouqianba.com/c/BfaBGJ");
        request.setBusinessType(QrCodeBusinessType.STORE_SCAN_ORDER);
        request.setBusinessId("bd40dd6d-e021-443c-8b2c-ddf8db770857");

        QrCodeVO result = qrCodeAppEdgeRpcService.bind(request);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testUnbind() {
        QrcodeUnbindRequest unbindRequest = new QrcodeUnbindRequest();
        unbindRequest.setMerchant_id("eb3bda9a-372a-41b8-a2f6-69a91c5926b4");
        unbindRequest.setBiz_store_id("bd40dd6d-e021-443c-8b2c-ddf8db770857");
        unbindRequest.setBindRelationId(673L);
        qrCodeAppEdgeRpcService.unbind(unbindRequest);
    }

    @Test
    public void testList() {

        QrcodeQueryRequest queryRequest = new QrcodeQueryRequest();
        queryRequest.setMerchant_id("eb3bda9a-372a-41b8-a2f6-69a91c5926b4");
        queryRequest.setBiz_store_id("bd40dd6d-e021-443c-8b2c-ddf8db770857");
        queryRequest.setBusinessType(QrCodeBusinessType.STORE_SCAN_ORDER);
        queryRequest.setBusinessId("bd40dd6d-e021-443c-8b2c-ddf8db770857");

        List<QrCodeVO> result = qrCodeAppEdgeRpcService.list(queryRequest);
        System.out.println(JSON.toJSONString(result));
    }

}
