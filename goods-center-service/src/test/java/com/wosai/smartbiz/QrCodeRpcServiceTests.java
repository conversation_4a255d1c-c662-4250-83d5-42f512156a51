package com.wosai.smartbiz;

import com.alibaba.fastjson.JSON;
import com.wosai.market.merchant.api.QRCodeRemoteService;
import com.wosai.market.merchant.dto.request.qrcode.QRCodeBindRequest;
import com.wosai.market.merchant.dto.request.qrcode.QRCodeFindRequest;
import com.wosai.market.merchant.dto.request.qrcode.QRCodeImageRequest;
import com.wosai.market.merchant.dto.request.qrcode.QRCodeQueryCustomerRequest;
import com.wosai.smartbiz.gds.dto.qrcode.BindQrcodeInfoToTableDTO;
import com.wosai.smartbiz.gds.dto.qrcode.QrCodeClientSnTuple;
import com.wosai.smartbiz.gds.request.FindQrCodeBusinessRequest;
import com.wosai.smartbiz.gds.request.QrcodeInfoRequest;
import com.wosai.smartbiz.gds.service.QrCodeRpcService;
import com.wosai.smartbiz.gds.vo.QrCodeBusinessVO;
import com.wosai.upay.common.bean.ListResult;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2021/2/25
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles("beta")
public class QrCodeRpcServiceTests {

    @Autowired
    private QrCodeRpcService qrCodeRpcService;

    @Autowired
    private QRCodeRemoteService qrCodeRemoteService;

    @Test
    public void testBindQrcodeInfoToTable() {

        BindQrcodeInfoToTableDTO dto = new BindQrcodeInfoToTableDTO()
                .setMerchantId("eb3bda9a-372a-41b8-a2f6-69a91c5926b4")
                .setStoreId("bd40dd6d-e021-443c-8b2c-ddf8db770857")
                .setQrcode("jjzt19112204438000773307")
                .setQrPrintCode("0443800077")
                .setQrcodeImageUrl(null)
                .setTableName("嘟啦啦")
                .setUserId("acf12573-0cca-11ea-9391-00163e02669a");

        qrCodeRpcService.bindQrcodeInfoToTable(dto);
    }

    @Test
    public void testGet() {
        QRCodeQueryCustomerRequest customerRequest = new QRCodeQueryCustomerRequest();
        customerRequest.setUrl("https://shouqianba-customer.oss-cn-hangzhou.aliyuncs.com/jjz_test/processedPhoto4/0929300016");
        Map result = qrCodeRemoteService.customerQueryByUrl(customerRequest);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testGenerate() {
        QRCodeImageRequest request = new QRCodeImageRequest();
        request.setClientSn("xxxx");
        System.out.println(JSON.toJSONString(qrCodeRemoteService.genQRCodeImage(request)));
    }

    @Test
    public void testGetInfo() {
        QrcodeInfoRequest request = new QrcodeInfoRequest();
        request.setUrl("http://99zhe.test.shouqianba.com/b/CLVxrn");
        Map result = qrCodeRpcService.getInfoByUrl(request);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void test2() {
        Map result = qrCodeRemoteService.queryByUrlString("http://99zhe.test.shouqianba.com/t/iqyqF");
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void bindByRpc() {
        QRCodeBindRequest bindRequest = new QRCodeBindRequest();
        bindRequest.setMerchant_id("eb3bda9a-372a-41b8-a2f6-69a91c5926b4");
        bindRequest.setQrcode("jjzd19122505125000413723");
        bindRequest.setId("047f1513-26e0-11ea-9391-00163e02669a");
        bindRequest.setRole("super_admin");
        bindRequest.setTargetStoreId("bd40dd6d-e021-443c-8b2c-ddf8db770857");
        bindRequest.setNotBindTable(true);
        qrCodeRemoteService.bind(bindRequest);
    }

    @Test
    public void testQuery() {
        QRCodeFindRequest findRequest = new QRCodeFindRequest();
        findRequest.setMerchant_id("eb3bda9a-372a-41b8-a2f6-69a91c5926b4");
        findRequest.setStoreId("bd40dd6d-e021-443c-8b2c-ddf8db770857");
        findRequest.setSize(10);
        findRequest.setPage(1);
        //List<RemoteQrCodeType> types = Lists.newArrayList(RemoteQrCodeType.STORE, RemoteQrCodeType.SELF_HELP_STORE);
        //findRequest.setTypes(types.stream().map(RemoteQrCodeType::getTerminalType).collect(Collectors.toList()));

        ListResult originResult = qrCodeRemoteService.findQRCodeByStoreId(findRequest);
        System.out.println(JSON.toJSONString(originResult));
    }

    @Test
    public void testGetBusiness() {

        final String storeId = "bd40dd6d-e021-443c-8b2c-ddf8db770857";

        FindQrCodeBusinessRequest request = new FindQrCodeBusinessRequest()
                .setTuples(Lists.newArrayList(
                        new QrCodeClientSnTuple()
                        .setStoreId(storeId)
                        .setClientSn("1400800002")
                        .setTerminalType(45),
                        new QrCodeClientSnTuple()
                        .setStoreId(storeId)
                        .setClientSn("1400700001")
                        .setTerminalType(4801),
                        new QrCodeClientSnTuple()
                        .setStoreId(storeId)
                        .setClientSn("1400800010")
                        .setTerminalType(45),
                        new QrCodeClientSnTuple()
                        .setStoreId(storeId)
                        .setClientSn("1380400010")
                        .setTerminalType(4801),
                        new QrCodeClientSnTuple()
                        .setStoreId(storeId)
                        .setClientSn("1380400008")
                        .setTerminalType(4801),
                        new QrCodeClientSnTuple()
                        .setStoreId(storeId)
                        .setClientSn("1380400007")
                        .setTerminalType(4801)
                ));

        List<QrCodeBusinessVO> result = qrCodeRpcService.getBusiness(request);
        System.out.println(JSON.toJSONString(result));
    }

}
