package com.wosai.smartbiz.service.gds;

import com.wosai.smartbiz.Application;
import com.wosai.smartbiz.gds.dto.licence.LicenceRenewRequest;
import com.wosai.smartbiz.gds.dto.licence.RenewUrlDetail;
import com.wosai.smartbiz.gds.dto.licence.StoreLicenceDetail;
import com.wosai.smartbiz.gds.enums.LicenceProductTypeEnum;
import com.wosai.smartbiz.gds.enums.LicenceVersionTypeEnum;
import com.wosai.smartbiz.gds.service.StoreLicenceRenewService;
import com.wosai.trade.service.servicefee.request.PeriodApplyOneRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles("beta")
class StoreLicenceRenewServiceTest {
    @Autowired
    private StoreLicenceRenewService storeLicenceRenewService;

    @Test
    void syncCashierActivationInfo() {
//        PeriodApplyOneRequest periodApplyOneRequest = new PeriodApplyOneRequest();
//        periodApplyOneRequest.setMerchantSn("21690003677915");
//        periodApplyOneRequest.setStoreSn("21590000001302760");
//        periodApplyOneRequest.setOperatorId("6d744c9e-a18c-4d05-ac2d-0daeddcf88ca");
//        periodApplyOneRequest.setOperatorName("魏运宏");
//        periodApplyOneRequest.setRemark("激活1");
//        periodApplyOneRequest.setPlatform("CRM");
//        periodApplyOneRequest.setScenesCode("");
//        periodApplyOneRequest.setChargeAmount(1L);
//        periodApplyOneRequest.setOrderSn("123");
//        periodApplyOneRequest.setServiceFeeId(1097L);
//        storeLicenceRenewService.syncCashierActivationInfo(periodApplyOneRequest, "", "");
//        System.out.println("11");
    }

    @Test
    void getLicenceInfo() {
        LicenceRenewRequest licenceRequest = new LicenceRenewRequest();
        licenceRequest.setStoreId("e23d0a3c-97e0-42a5-ad07-7409379de00a");
        licenceRequest.setProductType(LicenceProductTypeEnum.RESTAURANT);
//        licenceRequest.setVersionType(LicenceVersionTypeEnum.ONE_Y);

        StoreLicenceDetail licenceInfo = storeLicenceRenewService.getLicenceInfo(licenceRequest);
        System.out.println(licenceInfo);
    }

    @Test
    void getRenewCashierLicenceUrl() {
        LicenceRenewRequest licenceRequest = new LicenceRenewRequest();
        licenceRequest.setStoreId("e23d0a3c-97e0-42a5-ad07-7409379de00a");
        licenceRequest.setProductType(LicenceProductTypeEnum.RESTAURANT);
//        licenceRequest.setVersionType(LicenceVersionTypeEnum.ONE_Y);
        licenceRequest.setRequestSource("HomePage");

        RenewUrlDetail licenceInfo = storeLicenceRenewService.getRenewCashierLicenceUrl(licenceRequest);
        System.out.println(licenceInfo);
    }
}