package com.wosai.smartbiz.waterline;

import com.alibaba.fastjson.JSON;
import com.wosai.market.merchant.api.QRCodeRemoteService;
import com.wosai.market.merchant.dto.request.qrcode.QRCodeBindRequest;
import com.wosai.market.merchant.dto.request.qrcode.QRCodeFindRequest;
import com.wosai.market.merchant.dto.request.qrcode.QRCodeImageRequest;
import com.wosai.market.merchant.dto.request.qrcode.QRCodeQueryCustomerRequest;
import com.wosai.smartbiz.Application;
import com.wosai.smartbiz.base.enums.BusinessTypeEnum;
import com.wosai.smartbiz.base.enums.OwnerTypeEnum;
import com.wosai.smartbiz.gds.dto.WaterLineConfigDTO;
import com.wosai.smartbiz.gds.dto.WaterLineDataDTO;
import com.wosai.smartbiz.gds.dto.qrcode.BindQrcodeInfoToTableDTO;
import com.wosai.smartbiz.gds.dto.qrcode.QrCodeClientSnTuple;
import com.wosai.smartbiz.gds.request.*;
import com.wosai.smartbiz.gds.service.QrCodeRpcService;
import com.wosai.smartbiz.gds.service.WaterLineService;
import com.wosai.smartbiz.gds.vo.QrCodeBusinessVO;
import com.wosai.upay.common.bean.ListResult;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/6/17
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles("beta")
public class WaterLineServiceTests {

    @Autowired
    private WaterLineService waterLineService;

    @Test
    public void testUpdateWaterLine() {
        List<String> businessIdList = Lists.newArrayList("5","6");
        String storeId = "598c4133-077c-462c-9714-9bdd863247b5";
        OwnerTypeEnum ownerType = OwnerTypeEnum.STORE;
        WaterLineUpdateRequest request = new WaterLineUpdateRequest();
        request.setOwnerId(storeId);
        request.setOwnerType(ownerType);
        request.setBusinessType(BusinessTypeEnum.R_STOCK);
        request.setBusinessIdList(businessIdList);

        WaterLineDataDTO waterLineDataDTO = waterLineService.updateWaterLine(request);
        Assert.assertNotNull(waterLineDataDTO);
    }


    @Test
    public void testFindLastWaterLine() {
        List<String> businessIdList = Lists.newArrayList("5","6");
        String storeId = "598c4133-077c-462c-9714-9bdd863247b5";
        OwnerTypeEnum ownerType = OwnerTypeEnum.STORE;
        WaterLineUpdateRequest request = new WaterLineUpdateRequest();
        request.setOwnerId(storeId);
        request.setOwnerType(ownerType);
        request.setBusinessType(BusinessTypeEnum.R_STOCK);
        request.setBusinessIdList(businessIdList);

        WaterLineDataDTO waterLineDataDTO = waterLineService.updateWaterLine(request);
        WaterLineConfigRequest configRequest = new WaterLineConfigRequest();
        configRequest.setOwnerId(storeId);
        configRequest.setOwnerType(ownerType);
        List<WaterLineConfigDTO> configList = waterLineService.findLastWaterLine(configRequest);
        Assert.assertNotNull(configList);

        List<WaterLineConfigDTO> filterConfigList = configList.stream().filter(config -> config.getBusinessType() == BusinessTypeEnum.R_STOCK).collect(Collectors.toList());
        Assert.assertNotNull(filterConfigList);
        Assert.assertEquals(waterLineDataDTO.getWaterLine(), filterConfigList.get(0).getWaterLine());
    }
}
