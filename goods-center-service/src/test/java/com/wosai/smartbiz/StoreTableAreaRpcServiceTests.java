package com.wosai.smartbiz;

import com.wosai.smartbiz.gds.dto.table.StoreTableCreateAndBindQrcodeDTO;
import com.wosai.smartbiz.gds.dto.table.TableQrcodeDTO;
import com.wosai.smartbiz.gds.service.StoreTableService;
import com.wosai.smartbiz.user.user.enums.RemoteQrCodeStatus;
import com.wosai.smartbiz.user.user.enums.RemoteQrCodeType;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @create 2021/2/7
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles(value = "beta")
public class StoreTableAreaRpcServiceTests {

    @Autowired
    private StoreTableService storeTableService;

    private final static String merchantId = "eb3bda9a-372a-41b8-a2f6-69a91c5926b4";

    private final static String storeId = "bd40dd6d-e021-443c-8b2c-ddf8db770857";

    private final static String userId = "1c29449b-7241-45fa-bdf4-6d8e64a2625b";

    @Test
    public void testCreateTableAndBindQrcode() {

        Long areaId = 136L;

        StoreTableCreateAndBindQrcodeDTO dto = new StoreTableCreateAndBindQrcodeDTO()
                .setMerchantId(merchantId)
                .setStoreId(storeId)
                .setUserId(userId)
                .setAreaId(areaId)
                .setTableNo("No.1")
                .setSeatCount(4)
                .setQrcodeList(Lists.newArrayList(
                        new TableQrcodeDTO()
                                .setQrCodeType(RemoteQrCodeType.TABLE.name())
                                .setStatus(RemoteQrCodeStatus.NOT_USED.name())
                                .setQrCode("ssssss")
                                .setQrPrintCode("sssssssss")
                                .setQrcodeImageUrl(null)
                ));

        storeTableService.createAndBindQrcode(dto);
    }

    @Test
    public void testCopyTableAndQrcodeToNewStore() {
        storeTableService.copyTableAndQrcodeToNewStore("eb3bda9a-372a-41b8-a2f6-69a91c5926b4",
                "f1474aed-5352-4170-88f7-a05224bffc8c",
                "eb3bda9a-372a-41b8-a2f6-69a91c5926b4",
                "6c381908-3ff9-4eae-920e-97d8cb16c8f9");
    }


}
