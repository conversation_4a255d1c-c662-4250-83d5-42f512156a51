package com.wosai.smartbiz.licence;

import com.alibaba.fastjson.JSON;
import com.wosai.market.merchant.dto.PageResponse;
import com.wosai.smartbiz.Application;
import com.wosai.smartbiz.gds.dto.licence.AvailableQuantitySummaryDTO;
import com.wosai.smartbiz.gds.dto.licence.LicenceDistributionSummaryDTO;
import com.wosai.smartbiz.gds.dto.licence.OrganizationSummaryDTO;
import com.wosai.smartbiz.gds.enums.LicenceStatusEnum;
import com.wosai.smartbiz.gds.request.LicenceDistributionRequest;
import com.wosai.smartbiz.gds.request.LicenceGiftRequest;
import com.wosai.smartbiz.gds.request.LicenceRequest;
import com.wosai.smartbiz.gds.service.CashierLicenceRpcService;
import com.wosai.smartbiz.gds.service.CashierLicenceService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/2/25
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@ActiveProfiles("local")
public class CashierLicenceRpcServiceTests {

    @Autowired
    private CashierLicenceRpcService cashierLicenceRpcService;
    @Autowired
    private CashierLicenceService cashierLicenceService;

    @Test
    public void testOrganizationSummaryList() {
        LicenceRequest request = new LicenceRequest();
        request.setPage(1);
        request.setPageSize(10);

        PageResponse<OrganizationSummaryDTO> pageResponse = cashierLicenceRpcService.organizationSummaryList(request);
        log.info("testOrganizationSummaryList:{}", JSON.toJSONString(pageResponse));
    }

    @Test
    public void testAvailableQuantityDetail() {
        String organizationId = "631009499b1febc468b85485";
        LicenceRequest request = new LicenceRequest();
        request.setOrganizationId(organizationId);

        AvailableQuantitySummaryDTO summaryDTO = cashierLicenceRpcService.availableQuantityDetail(request);
        log.info("testAvailableQuantityDetail:{}", JSON.toJSONString(summaryDTO));
    }

    @Test
    public void testDistributionList() {
        String organizationId = "631009499b1febc468b85485";
        LicenceRequest request = new LicenceRequest();
        request.setOrganizationId(organizationId);

        PageResponse<LicenceDistributionSummaryDTO> pageResponse = cashierLicenceRpcService.distributionList(request);
        log.info("testDistributionList:{}", JSON.toJSONString(pageResponse));
    }


    @Test
    public void testDistributionDetail() {
        Long distributionId = 77L;
        LicenceDistributionRequest request = new LicenceDistributionRequest()
                .setDistributionId(distributionId);

        LicenceDistributionSummaryDTO distributionSummaryDTO = cashierLicenceRpcService.distributionDetail(request);
        log.info("testDistributionDetail:{}", JSON.toJSONString(distributionSummaryDTO));
    }

    @Test
    public void testGiftLicence() {
        LicenceGiftRequest request = new LicenceGiftRequest();

        List<LicenceGiftRequest.LicenceGiftData> list = new ArrayList<>();
        LicenceGiftRequest.LicenceGiftData giftData1 = new LicenceGiftRequest.LicenceGiftData();
        giftData1.setOrganizationCode("00025");
        giftData1.setCellphone("13958099751");
        giftData1.setQuantity(2);
        giftData1.setRemark("测试赠送");

        LicenceGiftRequest.LicenceGiftData giftData2 = new LicenceGiftRequest.LicenceGiftData();
        giftData2.setOrganizationCode("00025");
        giftData2.setCellphone("13958099751");
        giftData2.setQuantity(3);
        giftData2.setRemark("测试赠送");

        list.add(giftData1);
        list.add(giftData2);

        request.setList(list);

        boolean result = cashierLicenceRpcService.giftLicence(request);
        log.info("testListGiftLicence:{}", result);
    }



    @Test
    public void testUpdateBatchInfoStatus() {
        String batchNo = "230104162206388";
        LicenceRequest request = new LicenceRequest();
        request.setBatchNo(batchNo);
        request.setStatus(LicenceStatusEnum.DISABLED);

        boolean result = cashierLicenceRpcService.updateBatchInfoStatus(request);
        Assert.assertTrue(result);
        log.info("testUpdateBatchInfoStatus:{}", result);
    }
}
