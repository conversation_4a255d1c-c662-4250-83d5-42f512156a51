package com.wosai.smartbiz;

import org.jose4j.jwa.AlgorithmConstraints;
import org.jose4j.jws.AlgorithmIdentifiers;
import org.jose4j.jws.JsonWebSignature;
import org.jose4j.jwt.JwtClaims;
import org.jose4j.jwt.MalformedClaimException;
import org.jose4j.jwt.consumer.ErrorCodes;
import org.jose4j.jwt.consumer.InvalidJwtException;
import org.jose4j.jwt.consumer.JwtConsumer;
import org.jose4j.jwt.consumer.JwtConsumerBuilder;
import org.jose4j.keys.HmacKey;
import org.jose4j.lang.JoseException;
import org.junit.Test;

import java.security.Key;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * @author: redhat
 */
public class Jose4JTest {

    @Test
    public void testHMAC() throws JoseException {
        // 注意密钥长短（最少32个字符）
        HmacKey hmacKey = new HmacKey("12345678123456781234567812345678".getBytes());
        JsonWebSignature jsonWebSignature = jsonWebSignature(hmacKey, AlgorithmIdentifiers.HMAC_SHA256);
        String jwt = jsonWebSignature.getCompactSerialization();
        JwtConsumer jwtConsumer = jwtConsumer(hmacKey, AlgorithmIdentifiers.HMAC_SHA256);
        try {
            // 校验 JWT 并将其处理成 JwtClaims 对象
            JwtClaims jwtClaims = jwtConsumer.processToClaims(jwt);
            System.out.println("JWT validation succeeded! JwtClaims: " + jwtClaims);
        } catch (InvalidJwtException e) {
            handleException(e);
        }
    }

    @Test
    public void testRSA() throws JoseException {
        JsonWebSignature jsonWebSignature = jsonWebSignature(getPrivateKey(), null, AlgorithmIdentifiers.RSA_USING_SHA256);

        String jwt = jsonWebSignature.getCompactSerialization();

        JwtConsumer jwtConsumer = jwtConsumer(getPublicKey(), AlgorithmIdentifiers.RSA_USING_SHA256);
        try {
            // 校验 JWT 并将其处理成 JwtClaims 对象
            JwtClaims jwtClaims = jwtConsumer.processToClaims(jwt);
            System.out.println("JWT validation succeeded! JwtClaims: " + jwtClaims);
        } catch (InvalidJwtException e) {
            handleException(e);
        }
    }

    private JsonWebSignature jsonWebSignature(Key key, String algorithm) {
        return jsonWebSignature(key, null, algorithm);
    }

    /**
     * 一个 JWT 是一个携带 JwtClaims 作为负载的 JsonWebSignature 或 JsonWebEncryption 对象
     * 本例中的 JWT 是一个 JsonWebSignature 对象
     */
    private JsonWebSignature jsonWebSignature(Key key, String kid, String algorithm) {
        JsonWebSignature jsonWebSignature = new JsonWebSignature();
        // 为 JsonWebSignature 对象添加负载：JwtClaims 对象的 Json 内容
        jsonWebSignature.setPayload(jwtClaims().toJson());
        // JWT 使用 RSA 私钥签名
        jsonWebSignature.setKey(key);
        // 可选操作
        if (null != key) {
            jsonWebSignature.setKeyIdHeaderValue(kid);
        }
        // 在 JWT / JWS 上设置签名算法
        jsonWebSignature.setAlgorithmHeaderValue(algorithm);
        return jsonWebSignature;
    }

    /**
     * 创建 Claims，包装了 JWT 的内容
     */
    private JwtClaims jwtClaims() {
        JwtClaims claims = new JwtClaims();
        // 设置 Token 的签发者
        claims.setIssuer("Issuer");
        // 设置过期时间
        // claims.setExpirationTime();
        // 设置过期时间为 10 分钟后
        claims.setExpirationTimeMinutesInTheFuture(10);
        claims.setSubject("Subject");
        // 设置 Token 将被发送给哪些对象
        claims.setAudience("Audience X", "Audience Y", "Audience Z");
        // claims.setNotBefore();
        // 设置生效时间为 2 分钟前
        claims.setNotBeforeMinutesInThePast(2);
        // claims.setIssuedAt();
        // 设置 Token 发布/创建 时间为当前时间
        claims.setIssuedAtToNow();
        // claims.setJwtId();
        // 为 JWT 设置一个自动生成的唯一 ID
        claims.setGeneratedJwtId();
        // 额外添加的生命属性
        claims.setClaim("email", "<EMAIL>");
        claims.setClaim("accessToken", "123456789");
        return claims;
    }

    /**
     * 使用 JwtConsumerBuilder 构建一个合适的 JwtConsumer 对象，用于校验和处理 JWT。
     * 如果 JWT 已被加密，只需提供一个解密密钥或解密密钥解析器给 JwtConsumerBuilder。
     */
    private JwtConsumer jwtConsumer(Key key, String algorithm) {
        return new JwtConsumerBuilder()
                // 在验证时间时留出一些余量以解决时钟偏差问题
                .setAllowedClockSkewInSeconds(30)
                // 设置解密密钥
                // .setDecryptionKey()
                // 设置解密密钥解析器
                // .setDecryptionKeyResolver()
                // .setDisableRequireSignature()
                // 必须设置过期时间
                .setRequireExpirationTime()
                // 必须设置 Subject
                .setRequireSubject()
                // 必须设置 Token 签发者
                .setExpectedIssuer("Issuer")
                // 必须设置 Token 签发给谁
                .setExpectedAudience("Audience X")
                // 设置用于验证签名的公钥
                .setVerificationKey(key)
                // 设置允许的预期签名算法
                .setJwsAlgorithmConstraints(
                        AlgorithmConstraints.ConstraintType.WHITELIST, algorithm)
                .build();
    }

    /**
     * 处理校验 JWT 并将其处理成 JwtClaims 对象过程中出现的异常
     */
    private void handleException(InvalidJwtException e) {
        System.out.println("Invalid JWT:" + e);
        try {
            JwtClaims jwtClaims = e.getJwtContext().getJwtClaims();
            // 异常是否因 JWT 过期触发
            if (e.hasExpired()) {
                System.out.println("Expired at " + jwtClaims.getExpirationTime());
            }
            // 异常是否因 Audience 无效触发
            if (e.hasErrorCode(ErrorCodes.AUDIENCE_INVALID)) {
                System.out.println("Invalid audience: " + jwtClaims.getAudience());
            }
            // 异常是否因缺少 Audience 触发
            if (e.hasErrorCode(ErrorCodes.AUDIENCE_MISSING)) {
                System.out.println("Audience missing！");
            }
            // 异常是否因缺少加密触发
            if (e.hasErrorCode(ErrorCodes.ENCRYPTION_MISSING)) {
                System.out.println("Encryption missing！");
            }
            // 异常是否因缺少过期时间触发
            if (e.hasErrorCode(ErrorCodes.EXPIRATION_MISSING)) {
                System.out.println("Expiration missing!");
            }
            // 异常是否因过期时间太长触发
            if (e.hasErrorCode(ErrorCodes.EXPIRATION_TOO_FAR_IN_FUTURE)) {
                System.out.println("Expiration too far in future: " + jwtClaims.getExpirationTime());
            }
            // 异常是否因缺乏完整性触发
            if (e.hasErrorCode(ErrorCodes.INTEGRITY_MISSING)) {
                System.out.println("Integrity missing!");
            }
            // 异常是否因发布时间无效触发
            if (e.hasErrorCode(ErrorCodes.ISSUED_AT_INVALID_FUTURE)) {
                System.out.println("Issued at invalid future: " + jwtClaims.getIssuedAt());
            }
            // 异常是否因发布时间无效触发
            if (e.hasErrorCode(ErrorCodes.ISSUED_AT_INVALID_PAST)) {
                System.out.println("Issued at invalid past: " + jwtClaims.getIssuedAt());
            }
            // 异常是否因缺少发布时间触发
            if (e.hasErrorCode(ErrorCodes.ISSUED_AT_MISSING)) {
                System.out.println("Issued at missing!");
            }
            // 异常是否因签发者无效触发
            if (e.hasErrorCode(ErrorCodes.ISSUER_INVALID)) {
                System.out.println("Issuer invalid: " + jwtClaims.getIssuer());
            }
            // 异常是否因缺少签发者触发
            if (e.hasErrorCode(ErrorCodes.ISSUER_MISSING)) {
                System.out.println("Issuer missing!");
            }
            // 异常是否因 JSON 无效触发
            if (e.hasErrorCode(ErrorCodes.JSON_INVALID)) {
                System.out.println("Json invalid: " + jwtClaims.toString());
            }
            // 异常是否因缺少 JWT ID 触发
            if (e.hasErrorCode(ErrorCodes.JWT_ID_MISSING)) {
                System.out.println("JWT ID missing!");
            }
            // 异常是否因 JwtClaims 格式错误触发
            if (e.hasErrorCode(ErrorCodes.MALFORMED_CLAIM)) {
                System.out.println("Malformed claim!");
            }
            // 异常是否因缺少生效时间触发
            if (e.hasErrorCode(ErrorCodes.NOT_BEFORE_MISSING)) {
                System.out.println("Not before missing!");
            }
            // 异常是否因 Token 尚未生效触发
            if (e.hasErrorCode(ErrorCodes.NOT_YET_VALID)) {
                System.out.println("Not yet valid: " + jwtClaims.getNotBefore());
            }
            // 异常是否因 Token 的 Signature 部分无效触发
            if (e.hasErrorCode(ErrorCodes.SIGNATURE_INVALID)) {
                System.out.println("Signature invalid: " + jwtClaims.toString());
            }
            // 异常是否因 Token 的 Signature 部分缺失触发
            if (e.hasErrorCode(ErrorCodes.SIGNATURE_MISSING)) {
                System.out.println("Signature missing!");
            }
            // 异常是否因 Subject 无效触发
            if (e.hasErrorCode(ErrorCodes.SUBJECT_INVALID)) {
                System.out.println("Subject invalid: " + jwtClaims.getSubject());
            }
            // 异常是否因 Subject 缺失触发
            if (e.hasErrorCode(ErrorCodes.SUBJECT_MISSING)) {
                System.out.println("Subject missing!");
            }
            // 异常是否因 Type 无效触发
            if (e.hasErrorCode(ErrorCodes.TYPE_INVALID)) {
                System.out.println("Type invalid: " + jwtClaims.getRawJson());
            }
            // 异常是否因 Type 缺失触发
            if (e.hasErrorCode(ErrorCodes.TYPE_MISSING)) {
                System.out.println("Type missing!");
            }
        } catch (MalformedClaimException e1) {
            System.out.println("Malformed claim: " + e);
        }
    }

    /**
     * 解码PublicKey
     * @return
     */
    private PublicKey getPublicKey() {
        try {
            String key = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAx4/5dVVer4S95dkX15g7ohJM3bdoxFnb6ozCI2dMm2is7bT7v9SvzGIZXoSU7OxxVcfHBzJWzdtEe56ARF9g0dEyuwILISzkklCkLdwgXvFcZm7xbM1YApTMaCjEs4zfDCXVl0Dmo+PjL41sGUT9yiImKlirHXu/w2RvkPwIR+VOceUPY2CFw974EROw2EPKQ4+DCVE6ynSzmVX9zqWrceywgTv+S9mNd9jV2rKkFFEWgp4sJxfaaYCJKdfeB/cxZVY5Iw1igQzjLuXgej4AZJwPlnYeXZePr3KfgHx+M6NQ6bwNgQ9X841IWr4c6eAezRGgN+C7yMWgXCeim4miuQIDAQAB";
            byte[] byteKey = Base64.getDecoder().decode(key);
            X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(byteKey);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            return keyFactory.generatePublic(x509EncodedKeySpec);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
    /**
     * 解码PrivateKey
     * @return
     */
    private PrivateKey getPrivateKey() {
        try {
            String key = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDHj/l1VV6vhL3l2RfXmDuiEkzdt2jEWdvqjMIjZ0ybaKzttPu/1K/MYhlehJTs7HFVx8cHMlbN20R7noBEX2DR0TK7AgshLOSSUKQt3CBe8VxmbvFszVgClMxoKMSzjN8MJdWXQOaj4+MvjWwZRP3KIiYqWKsde7/DZG+Q/AhH5U5x5Q9jYIXD3vgRE7DYQ8pDj4MJUTrKdLOZVf3Opatx7LCBO/5L2Y132NXasqQUURaCniwnF9ppgIkp194H9zFlVjkjDWKBDOMu5eB6PgBknA+Wdh5dl4+vcp+AfH4zo1DpvA2BD1fzjUhavhzp4B7NEaA34LvIxaBcJ6KbiaK5AgMBAAECggEBALhl696kdKqAYKwbDnHTo/6dJFOW3wLX+WJgkB8Am4PalDfNjPXzxEF5v/FbVvUZgZv+zoRy2ky7OCt2CRWkgCVARb0RJVGRVt9wDlSl229/XJ72ErUzhQhVkA8K5L74Az4JU7TAbp2vvEQ7TFCJNGzrYxKr/elvaIFWs9RnXgt3TfVmcYufu60ilB4YtF7oCUywXymlY1mz9XbQAV2WyWLUm+3nCRWfC60jdUulKSnDjy+rPoxOo/j5lZ5akDiR7hMJW3iA09fx/9NbgCS1efPyFsmOA8p+nJ471JnKEbRzj6dej/1lRrVt13h83+KIHujzI5PTpK5uXn69YWvD0+UCgYEA/Amq/nuyKodEc5k93L7SzYbUzvqOewRrUXcN62ek8+3dlRKdXYZl+V4Dv0/AXzElXWMbxb4SaWX3/v19KK7uV1OMo8psMfA7Nir7XokSOW1Une9jogd2bHpysg7rzLSHw4zXnDpgpXfgsTsHNJAZeb7V73GWGca2wOL8vpXDsZ8CgYEAyrMePcqe39Jp9Lufa2vt6B31CcXV22l+K11JMP935pKqLsU5BrVlKn2clR6q33r1Ingvqgvx/5S9HRoPvsy7+B4+azH24d+J3ZdSFfS3Fs/Ldpd9ov57CH6eTeo9BamLq+OttndsaBPWgXG7xIAcNcs1Cd8y1aQ2nYEBdheQvKcCgYBBCvmu/RsosInwzz5Qxoo+cipfU/gLNUPA3VmhQ7klHmDNxOUJqtKMPT/4UkuSBWvgIuCa8odr3d3XV3HCWwq1TKGB745mgNm3C/adGgCzA00QCSSMPyLHsf4Bh6pAxOI4jBNkhQhGC57X/iTI4ttAOQia55RWCJtHQgz53kqHfwKBgQC4Ckm68O2vauypOHFWp3gq+Igzh1zVCsNzvA9oXYwP3bqHHKraXHiuS76P5nhe0NUhpt4gFxhyZioO8kq5gkpozYz+5y6xhrU2bN4q8TYLkD+8PW/lPgmXfN9e/v7I1VfriZ1/vo6/MMPiY9GIaIk0jnxpqi4NrDuqZiddgaf4oQKBgDz/2yP57IFalxXXUj074QwkL0XJ3mywTHezFqkO0IeOFmn/ELHiVW+/cDmmvHqi3nSfTUcAqUV1lOWQxCodN06lDCxl9FsZnp8gbZAFhEXrhINhR/wh5fua46BYkKf1mYog/lxzq6c6f12Fs2T4AjYXrALyQhBIEbnNE7LuyBo1";
            byte[] byteKey = Base64.getDecoder().decode(key);
            PKCS8EncodedKeySpec x509EncodedKeySpec = new PKCS8EncodedKeySpec(byteKey);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            return keyFactory.generatePrivate(x509EncodedKeySpec);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
