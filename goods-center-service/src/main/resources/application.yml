hs:
  server:
    env: prod
  gateway:
    notify:
      url: http://gateway-node

spring:
  application:
    name : goods-center-service
  datasource:
    dba:
      jdbc-url: tk-goods-center-sbiz_dj_db-2875?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai
      driver-class-name: com.mysql.cj.jdbc.Driver
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 10000
      idle-timeout: 60000
    dbb:
      jdbc-url: tk-goods-center-sbiz_dj_db-2875?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai
      driver-class-name: com.mysql.cj.jdbc.Driver
      maximum-pool-size: 20
      minimum-idle: 1
      connection-timeout: 10000
      idle-timeout: 60000
  kafka:
    listener:
      ft-order-topic: databus.event.shouqianba.order.allin
      customer-relation-topic: databus_events_CRM_customer-relation
      organization-change-topic: databus_events_CRM_service-org
      crm-user-topic: databus_events_CRM_service-user
      oms-ITSYS-ding-audit-sync-topic: databus_ITSYS_ding-audit-sync
      account-transfer-topic: databus.event.account.transfer.allin
      upay-core-change-uc-user-topic: events.upay-core.change-uc-user
      iot-TAP-device-bind-topic: events_TAP_device-bind
    consumer:
      group-id: goods-center-service-dev
      max-poll-records: 500
      key-deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer2
      value-deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer2
      bootstrap-servers: **************:9092,**************:9092,**************:9092
#      aliyun-bootstrap-servers: **************:9092,**************:9092,**************:9092
      aliyun-bootstrap-servers: aliyun-beta-kafka-01.iwosai.com:9092,aliyun-beta-kafka-02.iwosai.com:9092,aliyun-beta-kafka-03.iwosai.com:9092
      auto-commit-interval: 100
      enable-auto-commit: true
      properties:
        spring.deserializer.key.delegate.class: org.apache.kafka.common.serialization.StringDeserializer
        spring.deserializer.value.delegate.class: io.confluent.kafka.serializers.KafkaAvroDeserializer
    properties:
      max:
        partition:
          fetch:
            bytes: 2097152
      specific:
        avro:
          reader: true
      schema:
        registry:
          url: http://**************:8081,http://**************:8081,http://**************:8081
      aliyun-schema:
        registry:
#          url: http://**************:8081,http://**************:8081,http://**************:8081
          url: http://aliyun-beta-schema-01.iwosai.com:8081,http://aliyun-beta-schema-02.iwosai.com:8081,http://aliyun-beta-schema-03.iwosai.com:8081

  #high level rest client 配置
  elasticsearch:
    url: *************:9200

  redis:
    host: r-8vbkddg0ez3eak2rzq.redis.zhangbei.rds.aliyuncs.com
    port: 6379
    database: 3
    password: roFXzHwXPY3RnI%5
    timeout: 1000s  # 数据库连接超时时间，2.0 中该参数的类型为Duration，这里在配置的时候需要指明单位
    lettuce:
      pool:
        max-active: 8 # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1 # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 8 # 连接池中的最大空闲连接
        min-idle: 0 # 连接池中的最小空闲连接
      shutdown-timeout: 100 # 关闭超时时间

#port
server:
  port : 8080

dependency:
  kafkaConsumerEnable: true

# kakfa demo topic
demo:
  kafka:
    topic:
      name: avro-archetype

# apollo
app:
  id: smartbiz-goods-center # 使用的 Apollo 的项目（应用）编号,等同于 classPath:/META_INF/app.properties
  name: goods-center # next上项目名称
  group: smart
apollo:
  bootstrap:
    enabled: true
    # will inject 'application' and 'TEST1.apollo' namespaces in bootstrap phase
    namespaces: application,marketing.common-config

wechat:
  mini-app-id: wxd2f16468474f61b8

vendor:
  vendor-sn: 91801117
  vendor-app-appid: 2020102600003264
  vendor-secret: 71ab2c00f3c28d753549b322f63530b3
  retail-vendor-sn: 91800102
  retail-vendor-app-appid: 2023070500005695
  retail-vendor-secret: 7517b97ab432f4348aad467b243ef356

rmqx:
  host: tcp://rmqx.iwosai.com:1883
  server-client-id-prefix: mk-server-
  tv-device-client-id-prefix: mk-tv-
  cashier-device-client-id-prefix: mk-cashier-
  user: unmurphy
  pwd: 123456
  superuser: true
  qos: 2
  mk-tv-config-topic: mk-tv-config
  mk-tv-order-topic: mk-tv-order
  mk-cashier-message-topic: mk-cashier-message
  mk-tv-check-online-url: http://rmqx.iwosai.com:8070/api/v3/connections
  will-data: offline

crm:
  customer-relation:
    store-origin-config-code: ZHJYMDZZ
    store-ufood-user-config-code: ZHJYSTOREKEEPER
    cashier-keeper-relation-code: MDSYWHR
    store-upay-user-config-code: test-bd-store
    store-retail-user-config-code: MDSYWHR
  smart-store:
    business-id: 010b567c-faf4-4740-8424-c0e5b8dde447
  store-cashier-active:
    jump-url: https://jjz-crm-fe.iwosai.com/cashierSoftwares/activate?store_id=%s&store_name=%s&token=:token
  store-cashier-unbind:
    jump-url: https://jjz-crm-fe.iwosai.com/cashierSoftwares/unbind?store_id=%s&store_name=%s&active_time=%s&brand=%s&product_type=%s&product_name=%s&token=:token
  external-product-bind:
    jump-url: https://jjz-crm-fe.iwosai.com/thirdpartyPayBind?storeId=%s&storeName=%s&token=:token
  store-ai-code-bind:
    jump-url: https://jjz-crm-fe.iwosai.com/aiCodeList?storeId=%s&storeName=%s&token=:token

smart:
  goods:
    retail:
      biz-code: SMART
      biz-scene: retail
      template-id: 40

external:
  retail:
    product-probation-default-days: 30
    huiguanjia-app-id: 202406189056979413
    kewuyou-app-id: 202310075818045718
    zhibaiwei-app-id: 202308152555843166

jsonrpc:
  rpc-connection-timeout-mills: 1000
  rpc-read-timeout-mills: 3000
  uc-token-service-url: http://uc-token-service.beta.iwosai.com
  uc-user-service-url: http://uc-user-service.beta.iwosai.com
  merchant-user-service-url: http://merchant-user-service.beta.iwosai.com
  app-backend-service-url: http://app-backend-service.beta.iwosai.com
  merchant-service-url: http://merchant-center.beta.iwosai.com
  market-merchant-service-url: http://merchant.beta.iwosai.com
  mcc-url: http://mcc.beta.iwosai.com
  uitem-core-service-url: http://uitem-core.beta.iwosai.com
  core-business-url: http://core-business.beta.iwosai.com
  galaxy-service-url: http://galaxy.beta.iwosai.com
  customer-user-service-url: http://customer-user.beta.iwosai.com
  sales-terminal-order-service-url: http://sales-terminal-order.beta.iwosai.com
  crow-server-service-url: http://192.168.101.149:18081
  market-awesome-goods-url: http://awesome-goods.beta.iwosai.com
  customer-relation-service-url: http://crm-customer-relation.beta.iwosai.com
  sales-system-service-url: http://sales-system-service.beta.iwosai.com
  sp-workflow-service-url: http://sp-workflow-service.beta.iwosai.com
  boss-circle: http://boss-circle-user.beta.iwosai.com
  tethys: http://tethys.beta.iwosai.com
  upay-activity-url: http://upay-activity.beta.iwosai.com
  data-events-url: http://data-events.beta.iwosai.com
  awesome-message-service-url: http://awesome-message.beta.iwosai.com
  marketing-saas-external-platform: http://marketing-saas-external-platform.beta.iwosai.com
  sales-system-profit-service-url: http://sales-system-profit.beta.iwosai.com
  smart-goods-url: http://smart-goods.beta.iwosai.com
  smart-standard-goods-url: http://smart-standard-goods.beta.iwosai.com
  invoice-url: http://invoice-app-backend.beta.iwosai.com
  oms-service-url: http://oms-service.beta.iwosai.com
  third-mapping-manage-url: http://third-mapping-manage.beta.iwosai.com
  gated-server-url: http://app-gated-service.beta.iwosai.com
  trade-manage-service: http://trade-manage-service.beta.iwosai.com
  marketing-saas-merchant-url: http://marketing-saas-merchant.beta.iwosai.com
  mkss-customer-url: http://mkss-customer.beta.iwosai.com
  aop-gateway-url: http://aop-gateway.beta.iwosai.com
  licence-manager: http://licence-manager.beta.iwosai.com
  voicebox-push-url: http://app-push-service.beta.iwosai.com
  upay-qrcode: http://upay-qrcode.beta.iwosai.com
  shouqianba-iot-service-url: http://shouqianba-iot-service.beta.iwosai.com
  outer-service-adapter-url: http://outer-service-adapter.beta.iwosai.com
  marketing-saas-prepaid-card-url: http://marketing-saas-prepaid-card.beta.iwosai.com
  renewal-center-url: http://renewal-center.beta.iwosai.com
  order-center-url: http://order-center
  bank-info-service-url: http://bank-info-service
  business-logstash-url: http://business-logstash.beta.iwosai.com
  merchant-user-url: http://merchant-user-service.beta.iwosai.com

tags:
  store_entity_id: 446143d3-697e-49bf-a12c-441c7e2447f0
  merchant_entity_id: dd0cd0fb-0659-4cc5-b7c2-f72637fde27c
  duplicateRegister: 76be16ce-c9cd-4d12-a1de-03c13cc6f86f

ai:
  alipay:
    isv-id: ****************
  osai:
    isv-id: jpBR
    username: ***********
    password: afasydfyasy8y3
    token-get-url: https://open.osai.cc/open/token/get
    device-sn-unbind-url: https://open.osai.cc/open/api/sbbh_jb/%s?token=%s
    active-code-unbind-url: https://open.osai.cc/open/api/sqm_jb/%s?token=%s
  youyou:
    code: SQB
    key: 6398a8492ec36d9ac8624e14c7d7ef7e
    unbind-url: http://sp.yoyo.link/api/v2/cdKey/unbound
  sunmi:
    app-id: edb2b49b47d94f99987d5d7cda506f68
    app-key: a8e684b116344255b560a1a51337ad9f
    unbind-url: https://openapi.sunmi.com/v2/open/ability-manage/ability/unbindByAuthCode

white:
  ai-business-type: 70659c7b-8e73-4374-bd6c-dc8c56244111
  device-active-store-business-type: 39546e89-7a85-4fb7-8590-f7b92bf4361b

saas:
    appId:
      restaurant: VZA5L9RJWAXZ
      retail: FYLW4SZVU164

aop:
  client-code:
    cashier: TERMINALCASHREGISTER
  service-notification:
    devCode: L9ZOYSQZXUXV
    firstTemplateCode: RGDQI9Q5OWXD
    everydayTemplateCode: JBEL2AYF8GUS



sunmi:
  api-midplat-url: https://api-midplat.sunmi.com
  sqb-sbs-sn-check-path: /pub-esb/sqb/sbs/sn/check
  app-id: 34f4535cc1a548f06
  app-secret-key: fc63121447191b9798cc2be311690594b79be765

yimin:
  api-url: https://api.neostra.com
  query-device-path: /device/openapi/v1/check
  kit-account: CRJD260073