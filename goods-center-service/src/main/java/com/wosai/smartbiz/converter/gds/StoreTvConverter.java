package com.wosai.smartbiz.converter.gds;

import com.google.common.collect.Lists;
import com.wosai.smartbiz.gds.vo.StoreTvConfigMediaVO;
import com.wosai.smartbiz.gds.vo.StoreTvConfigVO;
import com.wosai.smartbiz.gds.vo.StoreTvDeviceVO;
import com.wosai.smartbiz.goods.domain.TvConfigDO;
import com.wosai.smartbiz.goods.domain.TvConfigMediaDO;
import com.wosai.smartbiz.goods.domain.TvDeviceDO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022/1/11
 */
public class StoreTvConverter {

    /**
     * 转化数据 DO =》VO
     * @param tvDeviceDO
     * @return
     */
    public static StoreTvDeviceVO toDeviceVO(TvDeviceDO tvDeviceDO) {
        return toDeviceVO(tvDeviceDO, false);
    }

    /**
     * 转化数据 DO =》VO
     * @param tvDeviceDO
     * @return
     */
    public static StoreTvDeviceVO toDeviceVOWithSecretKey(TvDeviceDO tvDeviceDO) {
        return toDeviceVO(tvDeviceDO, true);
    }

    /**
     * 转化数据 DO =》VO
     * @param tvDeviceDO
     * @return
     */
    public static StoreTvDeviceVO toDeviceVO(TvDeviceDO tvDeviceDO, boolean needSecretKey) {
        if(null == tvDeviceDO){
            return null;
        }
        StoreTvDeviceVO storeTvDeviceVO = new StoreTvDeviceVO()
                .setBind(null != tvDeviceDO.getBindTime())
                .setDeviceId(tvDeviceDO.getId())
                .setName(tvDeviceDO.getName())
                .setSn(tvDeviceDO.getSn())
                .setOnline(tvDeviceDO.getOnline())
                .setBindTime(tvDeviceDO.getBindTime())
                .setLastLoginTime(tvDeviceDO.getLastLoginTime())
                .setDeviceClientId(tvDeviceDO.getDeviceClientId())
                .setDeviceModel(tvDeviceDO.getDeviceModel())
                .setMerchantId(tvDeviceDO.getMerchantId())
                .setMerchantSn(tvDeviceDO.getMerchantSn())
                .setStoreId(tvDeviceDO.getStoreId())
                .setStoreSn(tvDeviceDO.getStoreSn());
        if(needSecretKey){
            storeTvDeviceVO.setPublicKey(tvDeviceDO.getPublicKey());
            storeTvDeviceVO.setPrivateKey(tvDeviceDO.getPrivateKey());
        }
        return storeTvDeviceVO;
    }

    /**
     * 转化数据 DO =》VO
     * @param tvDeviceDOList
     * @return
     */
    public static List<StoreTvDeviceVO> toDeviceVOList(List<TvDeviceDO> tvDeviceDOList) {
        if(CollectionUtils.isEmpty(tvDeviceDOList)){
            return Lists.newArrayList();
        }
        return tvDeviceDOList.stream()
                .map(StoreTvConverter::toDeviceVO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }



    /**
     * 转化数据 DO =》VO
     * @param tvConfigDO
     * @return
     */
    public static StoreTvConfigVO toConfigVO(TvConfigDO tvConfigDO) {
        if(null == tvConfigDO){
            return null;
        }
        return new StoreTvConfigVO()
                .setConfigId(tvConfigDO.getId())
                .setDeviceId(tvConfigDO.getDeviceId())
                .setUse(tvConfigDO.getUse())
                .setMode(tvConfigDO.getMode())
                .setRotationDuration(tvConfigDO.getRotationDuration())
                .setRotationPosition(tvConfigDO.getRotationPosition())
                .setRotationContent(tvConfigDO.getRotationContent())
                .setShowQrCode(tvConfigDO.getShowQrCode())
                .setShowNickname(tvConfigDO.getShowNickname());
    }

    /**
     * 转化数据 DO =》VO
     * @param tvConfigDOList
     * @return
     */
    public static List<StoreTvConfigVO> toConfigVOList(List<TvConfigDO> tvConfigDOList) {
        if(CollectionUtils.isEmpty(tvConfigDOList)){
            return Lists.newArrayList();
        }
        return tvConfigDOList.stream()
                .map(StoreTvConverter::toConfigVO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    /**
     * 转化数据 DO =》VO
     * @param tvConfigMediaDO
     * @return
     */
    public static StoreTvConfigMediaVO toConfigMediaVO(TvConfigMediaDO tvConfigMediaDO) {
        if(null == tvConfigMediaDO){
            return null;
        }
        return new StoreTvConfigMediaVO()
                .setConfigId(tvConfigMediaDO.getConfigId())
                .setType(tvConfigMediaDO.getType())
                .setUrl(tvConfigMediaDO.getUrl())
                .setCoverURL(tvConfigMediaDO.getCoverURL());
    }

    /**
     * 转化数据 DO =》VO
     * @param tvConfigMediaDOList
     * @return
     */
    public static List<StoreTvConfigMediaVO> toConfigMediaVOList(List<TvConfigMediaDO> tvConfigMediaDOList) {
        if(CollectionUtils.isEmpty(tvConfigMediaDOList)){
            return Lists.newArrayList();
        }
        return tvConfigMediaDOList.stream()
                .map(StoreTvConverter::toConfigMediaVO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
