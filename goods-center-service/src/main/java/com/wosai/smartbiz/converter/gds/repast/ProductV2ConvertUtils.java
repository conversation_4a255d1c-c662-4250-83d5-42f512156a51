package com.wosai.smartbiz.converter.gds.repast;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.wosai.market.dto.*;
import com.wosai.market.dto.category.SingleCategoryDetail;
import com.wosai.market.dto.product.ProductDetail;
import com.wosai.market.enums.SaleSceneEnum;
import com.wosai.market.enums.SaleTimeTypeEnum;
import com.wosai.smart.goods.common.utils.PinyinUtils;
import com.wosai.smartbiz.base.enums.YesNoEnum;
import com.wosai.smartbiz.enums.CashierProductTypeEnum;
import com.wosai.smartbiz.enums.MaterialStockStatusEnum;
import com.wosai.smartbiz.enums.ProductSaleTerminalEnum;
import com.wosai.smartbiz.enums.ProductStockTypeEnum;
import com.wosai.smartbiz.gds.dto.CashierRetailProductSnapshot;
import com.wosai.smartbiz.gds.enums.SkuType;
import com.wosai.smartbiz.goods.constants.GdsConstants;
import com.wosai.smartbiz.goods.enums.ProductStatusEnum;
import com.wosai.smartbiz.goods.util.PinyinUtil;
import com.wosai.smartbiz.vo.gds.gds.SkuImagesVO;
import com.wosai.smartbiz.vo.gds.gds.SkuPropertiesVO;
import com.wosai.smartbiz.vo.gds.gds.v2.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2020/12/30
 */
public class ProductV2ConvertUtils {

    public static List<StoreProductVO> buildStoreProductVO(List<ProductDetail> list){
        if(CollectionUtils.isEmpty(list)){
            return Collections.emptyList();
        }
        return list.stream()
                .map(ProductV2ConvertUtils::buildStoreProductVO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static StoreProductVO buildStoreProductVO(ProductDetail detail){
        if(Objects.isNull(detail)){
            return null;
        }

        ProductSpu spu = detail.getSpu();
        if(Objects.isNull(spu)){
            return null;
        }
        List<ProductSku> skuList = Optional.ofNullable(detail.getSkuList()).orElse(Collections.emptyList());

        // 获取分类排序
        int categorySort = Optional.ofNullable(detail.getCategory())
                .map(SingleCategoryDetail::getCategory)
                .map(CategoryDTO::getSort)
                .orElse(0);

        StoreProductVO productVO = StoreProductVO.builder()
                .id(spu.getId())
                .spuId(spu.getSpuId())
                .spuTitle(spu.getSpuTitle())
                .spuType(CashierProductTypeEnum.toCashier(spu.getType()))
                .productPinyin(PinyinUtil.getUpCaseHeadLetter(spu.getSpuTitle()))
                .productStatus(ProductStatusEnum.NORMAL)
                .spuBarcode(spu.getSpuBarcode())
                .briefCode(spu.getBriefCode())
                .plu(spu.getPlu())
                .mainImageUrl(spu.getMainImageUrl())
                .description(spu.getDescription())
                .isBarcode(YesNoEnum.N)
                .isCombine(YesNoEnum.N)
                .sort(spu.getSort())
                .skuType(skuList.size() > 1 ? SkuType.MULTI : SkuType.SINGLE)
                .unitType(spu.getUnitType())
                .saleUnit(StringUtils.defaultIfBlank(spu.getSaleUnit(), GdsConstants.ProductConstants.PRODUCT_DEFAULT_SALE_UNIT))
                .saleStatus(spu.getSaleStatus())
                .categoryId(spu.getCategoryId())
                .categorySort(categorySort)
                .gmtCreate(new Date(spu.getCtime()))
                .keepPriceEqual(spu.getKeepPriceEqual())
                .minSaleNum(spu.getMinSaleNum())
                .saleTimes(spu.getSaleTimes())
                .saleTerminals(ProductSaleTerminalEnum.toCashier(spu.getSaleTerminalTypes()))
                .saleScenes(com.wosai.smartbiz.goods.enums.repast.SaleSceneEnum.toLocal(spu.getSaleScene()))
                .skuList(buildProductSkuVO(skuList, spu.getSaleScene()))
                .recipes(buildSkuPropertiesVO(detail.getAttributeList()))
                .images(buildImageVO(detail.getMediaList()))
                .materialList(buildMaterialVO(detail.getMaterialList()))
                .materialGroups(buildMaterialGroupVO(detail.getMaterialGroups()))
                .stock(buildStockVO(detail.getStock()))
                .mustOrderProducts(buildMustOrderProductVO(detail.getMustOrderProducts()))
                .optionalProductGroups(buildOptionalGroupVO(detail.getOptionalOrderProductGroups()))
                .saleTime(toSaleTimeVo(detail))
                .extendProps(spu.getExtendProps())
                .build();

        // 构建快照
        buildSnapshot(productVO);

        return productVO;
    }

    public static List<ProductSkuVO> buildProductSkuVO(List<ProductSku> list, SaleSceneEnum saleScene){
        if(CollectionUtils.isEmpty(list)){
            return Collections.emptyList();
        }
        return list.stream()
                .map(p -> buildProductSkuVO(p, saleScene))
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(ProductSkuVO::getSort, Comparator.nullsLast(Integer::compareTo)))
                .collect(Collectors.toList());
    }

    public static ProductSkuVO buildProductSkuVO(ProductSku sku, SaleSceneEnum saleScene) {
        if(Objects.isNull(sku)) {
            return null;
        }

        Long salePrice = sku.getSalePrice();
        switch (saleScene) {
            case ALL:
            case SCAN:
                if(Objects.nonNull(sku.getArrivalPrice())) {
                    salePrice = sku.getArrivalPrice();
                }
                break;
            case TAKEOUT:
                if(Objects.nonNull(sku.getTakeoutPrice())) {
                    salePrice = sku.getTakeoutPrice();
                }
                break;
            default:
                break;
        }

        return ProductSkuVO.builder()
                .skuId(sku.getSkuId())
                .skuTitle(sku.getSpecName())
                .salePrice(salePrice)
                .costPrice(sku.getCostPrice())
                .vipPrice(sku.getVipPrice())
                .sort(sku.getSort())
                .arrivalPrice(sku.getArrivalPrice())
                .takeoutPrice(sku.getTakeoutPrice())
                .packFee(sku.getPackFee())
                .arrivalPackFee(sku.getArrivalPackFee())
                .takeoutPackFee(sku.getTakeoutPackFee())
                .conversionRatio(sku.getConversionRatio())
                .conversionRatioUnit(sku.getConversionRatioUnit())
                .build();
    }

    public static List<SkuPropertiesVO> buildSkuPropertiesVO(List<Attribute> list){
        if(CollectionUtils.isEmpty(list)){
            return Collections.emptyList();
        }
        return list.stream()
                .map(ProductV2ConvertUtils::buildSkuPropertiesVO)
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(SkuPropertiesVO::getSort, Comparator.nullsLast(Integer::compareTo)))
                .collect(Collectors.toList());
    }

    public static SkuPropertiesVO buildSkuPropertiesVO(Attribute attribute){
        if(Objects.isNull(attribute)){
            return null;
        }
        List<Attribute> subAttrList = ListUtils.defaultIfNull(attribute.getSubAttrList(), Lists.newArrayList());

        return new SkuPropertiesVO()
                .setAttributeId(attribute.getAttributeId())
                .setName(attribute.getName())
                .setSort(attribute.getSort())
                .setIsMust(Optional.ofNullable(attribute.getIsMust()).orElse(YesNoEnum.N))
                .setIsMulti(Optional.ofNullable(attribute.getIsMulti()).orElse(YesNoEnum.N))
                // 兼容老版本，返回只有名字的列表
                .setValues(subAttrList.stream()
                        .map(Attribute::getName)
                        .collect(Collectors.toList())
                )
                .setSubAttrList(subAttrList.stream()
                        .map(p -> new SkuPropertiesVO()
                                .setAttributeId(p.getAttributeId())
                                .setName(p.getName())
                                .setSort(p.getSort())
                                .setIsMust(Optional.ofNullable(p.getIsMust()).orElse(YesNoEnum.N)))
                        .sorted(Comparator.comparing(SkuPropertiesVO::getSort, Comparator.nullsLast(Integer::compareTo)))
                        .collect(Collectors.toList())
                );
    }

    public static List<SkuImagesVO> buildImageVO(List<ProductMedia> list){
        if(CollectionUtils.isEmpty(list)){
            return Collections.emptyList();
        }
        return list.stream()
                .map(ProductV2ConvertUtils::buildImageVO)
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(SkuImagesVO::getSort, Comparator.nullsLast(Integer::compareTo)))
                .collect(Collectors.toList());
    }

    public static SkuImagesVO buildImageVO(ProductMedia media){
        if(Objects.isNull(media)){
            return null;
        }
        SkuImagesVO vo = new SkuImagesVO();
        vo.setImageType(media.getType().name());
        vo.setImageUrl(media.getUrl());
        vo.setSort(media.getSort());
        return vo;
    }

    public static List<ProductMaterialGroupVO> buildMaterialGroupVO(List<MaterialGroup> groups) {
        if (CollectionUtils.isEmpty(groups)) {
            return Lists.newArrayList();
        }
        return groups.stream()
                .map(p -> ProductMaterialGroupVO.builder()
                        .groupId(p.getGroupId())
                        .groupName(p.getName())
                        .sort(p.getSort())
                        .materials(buildMaterialVO(p.getMaterials()))
                        .build())
                .collect(Collectors.toList());
    }

    public static List<ProductMaterialVO> buildMaterialVO(List<Material> list){
        if(CollectionUtils.isEmpty(list)){
            return Lists.newArrayList();
        }
        return list.stream()
                .map(ProductV2ConvertUtils::buildMaterialVO)
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(ProductMaterialVO::getSort, Comparator.nullsLast(Integer::compareTo)))
                .collect(Collectors.toList());
    }

    public static ProductMaterialVO buildMaterialVO(Material material){
        if(Objects.isNull(material)){
            return null;
        }
        return ProductMaterialVO.builder()
                .materialId(material.getMaterialId())
                .materialName(material.getName())
                .materialNamePinyin(PinyinUtils.getUpCaseHeadLetter(material.getName()))
                .price(material.getPrice())
                .sort(material.getSort())
                .status(MaterialStockStatusEnum.toStatus(material.getStatus()))
                .deleted(false)
                .build();
    }

    public static ProductStockVO buildStockVO(Stock stock){
        if(Objects.isNull(stock)){
            return ProductStockVO.builder()
                    .type(ProductStockTypeEnum.UNLIMITED_STOCK)
                    .autoReset(false)
                    .build();
        }
        return ProductStockVO.builder()
                .type(Objects.isNull(stock.getQuantity()) ? ProductStockTypeEnum.UNLIMITED_STOCK : ProductStockTypeEnum.CONFIGURED_STOCK)
                .stockId(stock.getStockId())
                .quantity(Objects.isNull(stock.getQuantity()) ? null : stock.getQuantity().setScale(3, RoundingMode.HALF_DOWN).stripTrailingZeros().toPlainString())
                .autoReset(YesNoEnum.Y == stock.getAutoReset())
                .autoResetQuantity(Objects.isNull(stock.getDefaultQuantity()) ? null : stock.getDefaultQuantity().setScale(3, RoundingMode.HALF_DOWN).stripTrailingZeros().toPlainString())
                .build();
    }

    /**
     * 构建必选商品
     * @param mustOrderProducts
     * @return
     */
    public static List<GroupProductVO> buildMustOrderProductVO(List<MustOrderProduct> mustOrderProducts) {
        if(CollectionUtils.isEmpty(mustOrderProducts)) {
            return Lists.newArrayList();
        }
        return mustOrderProducts.stream()
                .map(ProductV2ConvertUtils::buildMustOrderProductVO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 构建必选商品
     * @param mustOrderProduct
     * @return
     */
    public static GroupProductVO buildMustOrderProductVO(MustOrderProduct mustOrderProduct) {
        if(Objects.isNull(mustOrderProduct)) {
            return null;
        }
        return GroupProductVO.builder()
                .spuId(mustOrderProduct.getSpuId())
                .skuId(mustOrderProduct.getSkuId())
                .saleNum(mustOrderProduct.getSaleNum())
                .build();
    }

    /**
     * 构建可选商品组
     * @param groups
     * @return
     */
    public static List<ProductGroupVO> buildOptionalGroupVO(List<OptionalOrderProductGroup> groups) {
        if(CollectionUtils.isEmpty(groups)) {
            return Lists.newArrayList();
        }
        return groups.stream()
                .map(ProductV2ConvertUtils::buildOptionalGroupVO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 构建可选商品组
     * @param group
     * @return
     */
    public static ProductGroupVO buildOptionalGroupVO(OptionalOrderProductGroup group) {
        if(Objects.isNull(group)) {
            return null;
        }

        return ProductGroupVO.builder()
                .groupId(group.getGroupId())
                .groupName(group.getGroupName())
                .mustOrderNum(Optional.ofNullable(group.getMustOrderNum()).orElse(1))
                .supportDuplicate(YesNoEnum.Y == group.getSupportDuplicate())
                .products(buildOptionalProductVO(group.getProducts()))
                .build();
    }

    /**
     * 构建可选商品
     * @param products
     * @return
     */
    public static List<GroupProductVO> buildOptionalProductVO(List<OptionalOrderProduct> products) {
        if(CollectionUtils.isEmpty(products)) {
            return Lists.newArrayList();
        }
        return products.stream()
                .map(ProductV2ConvertUtils::buildOptionalProductVO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 构建可选商品
     * @param product
     * @return
     */
    public static GroupProductVO buildOptionalProductVO(OptionalOrderProduct product) {
        if(Objects.isNull(product)) {
            return null;
        }
        return GroupProductVO.builder()
                .spuId(product.getSpuId())
                .skuId(product.getSkuId())
                .addPrice(product.getAddPrice())
                .build();
    }

    public static ProductSaleTimeVO toSaleTimeVo(ProductDetail detail) {
        Objects.requireNonNull(detail, "detail can not be null");
        ProductSaleTimeVO vo = new ProductSaleTimeVO().setType(SaleTimeTypeEnum.ALL_TIME.getCode());
        SaleTime st = detail.getSaleTime();
        if (Objects.nonNull(st)) {
            vo.setType(st.getType().getCode());
            vo.setStartDate(st.getStartDate());
            vo.setEndDate(st.getEndDate());
            vo.setCycle(toSaleCycle(st.getCycle()));
            vo.setTimes(st.getTimes());
        }
        return vo;
    }

    public static List<Integer> toSaleCycle(List<DayOfWeek> dayOfWeeks) {
        if (CollectionUtils.isEmpty(dayOfWeeks)) {
            return Lists.newArrayList();
        }
        return dayOfWeeks.stream()
                .map(DayOfWeek::getValue)
                .collect(Collectors.toList());
    }

    public static BigDecimal toQuantityDecimal(String quantityStr) {
        try {
            BigDecimal number = new BigDecimal(quantityStr);
            return number.setScale(3, RoundingMode.HALF_DOWN).stripTrailingZeros();
        } catch (NumberFormatException ex) {
            throw new NumberFormatException("非法的库存值");
        }
    }

    public static void buildSnapshot(StoreProductVO productVO) {
        if (Objects.isNull(productVO) || CollectionUtils.isEmpty(productVO.getSkuList())) {
            return;
        }
        productVO.getSkuList().forEach(sku -> {
            CashierRetailProductSnapshot snapshot = new CashierRetailProductSnapshot();
            snapshot.setSpuId(productVO.getSpuId());
            snapshot.setTitle(productVO.getSpuTitle());
            snapshot.setSkuId(sku.getSkuId());
            snapshot.setSkuTitle(sku.getSkuTitle());
            snapshot.setSalePrice(sku.getSalePrice());
            snapshot.setPurchasePrice(null);
            snapshot.setCostPrice(sku.getCostPrice());
            snapshot.setSaleUnit(productVO.getSaleUnit());
            snapshot.setConversionRatio(sku.getConversionRatio());
            if (Objects.nonNull(sku.getConversionRatio())) {
                snapshot.setConversionRatio(BigDecimal.valueOf(sku.getConversionRatio()).multiply(new BigDecimal(1000)).setScale(3, RoundingMode.HALF_DOWN).doubleValue());
                snapshot.setConversionRatioUnit("g");
            }
            sku.setSnapshot(JSON.toJSONString(snapshot));
        });
    }

}
