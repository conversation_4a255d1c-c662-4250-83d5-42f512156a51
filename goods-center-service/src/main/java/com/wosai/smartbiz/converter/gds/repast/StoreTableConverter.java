package com.wosai.smartbiz.converter.gds.repast;

import com.google.common.collect.Lists;
import com.wosai.smartbiz.dto.business.table.StoreTableDTO;
import com.wosai.smartbiz.dto.vo.table.StoreTableVO;
import com.wosai.smartbiz.dto.vo.table.TableSortParam;
import com.wosai.smartbiz.goods.domain.repast.table.StoreTableDO;
import com.wosai.smartbiz.uc.domain.QrCodeDO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021/2/18
 */
public class StoreTableConverter {

    /**
     * 转化数据 DTO =》VO
     * @param tableDTOList
     * @return
     */
    public static List<StoreTableVO> toVOList(List<StoreTableDTO> tableDTOList) {
        if(CollectionUtils.isEmpty(tableDTOList)){
            return Lists.newArrayList();
        }
        return tableDTOList.stream()
                .map(StoreTableConverter::toVO)
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(StoreTableVO::getSort))
                .collect(Collectors.toList());
    }

    /**
     * 转化数据 DTO =》VO
     * @param tableDTO
     * @return
     */
    public static StoreTableVO toVO(StoreTableDTO tableDTO) {
        if(Objects.isNull(tableDTO)){
            return null;
        }
        StoreTableDO tableDO = tableDTO.getTableDO();
        if(Objects.isNull(tableDO)){
            return null;
        }
        List<QrCodeDO> qrCodeDOList = Optional.ofNullable(tableDTO.getQrCodeDOList()).orElse(Lists.newArrayList());
        return new StoreTableVO()
                .setAreaId(tableDO.getAreaId())
                .setTableId(tableDO.getId())
                .setTableNo(tableDO.getTableNo())
                .setSeatCount(tableDO.getSeatCount())
                .setSort(tableDO.getSort())
                .setStatus(tableDO.getStatus())
                .setBindQrCodeCount(qrCodeDOList.size())
                .setBindQrCode(qrCodeDOList.size() > 0);
    }

    /**
     * 排序列表 =》DO
     * @param sortParamList
     * @return
     */
    public static List<StoreTableDO> toDOList(List<TableSortParam> sortParamList){
        if(CollectionUtils.isEmpty(sortParamList)){
            return Collections.emptyList();
        }
        return sortParamList.stream()
                .map(sortParam -> {
                    StoreTableDO table = new StoreTableDO();
                    table.setId(sortParam.getTableId());
                    table.setSort(sortParam.getSort());
                    return table;
                }).collect(Collectors.toList());
    }

}
