package com.wosai.smartbiz.converter.gds;

import com.google.common.collect.Lists;
import com.wosai.smartbiz.dto.req.printer.StorePrinterConfigRpcSaveReq;
import com.wosai.smartbiz.user.user.dto.StorePrinterConfigDTO;
import com.wosai.smartbiz.user.user.dto.StorePrinterConfigSaveDTO;
import com.wosai.smartbiz.user.user.enums.print.PrintRangeEnum;
import com.wosai.smartbiz.user.user.enums.print.PrinterConfigTagEnum;
import com.wosai.smartbiz.user.user.vo.printer.AppEdgeStorePrinterConfigVO;
import com.wosai.smartbiz.user.user.vo.printer.PrintAreaVO;
import com.wosai.smartbiz.user.user.vo.printer.v2.PrintConfigVO;
import com.wosai.smartbiz.user.user.vo.printer.v2.StorePrinterConfigVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @create 2021/3/26
 */
public class StorePrinterConfigConverter {

    /**
     * 前端req =》dto
     * @param req
     * @return
     */
    public static StorePrinterConfigSaveDTO toDTO(StorePrinterConfigRpcSaveReq req) {

        if(Objects.isNull(req)){
            return null;
        }

        return StorePrinterConfigSaveDTO.builder()
                .configId(req.getConfigId())
                .merchantId(req.getMerchantId())
                .storeId(req.getStoreId())
                .userId(req.getSqbUserId())
                // 基本信息
                .printerName(req.getPrinterName())
                .printerType(req.getPrinterType())
                .printerIp(req.getPrinterIp())
                .paperSize(req.getPaperSize())
                // 后厨票据配置
                .printBackKitchenReceipt(Optional.ofNullable(req.getPrintBack())
                        .orElse(Boolean.FALSE))
                .backKitchenReceiptPrintNum(Optional.ofNullable(req.getBackPrintNum())
                        .orElse(1))
                .printModes(ListUtils.defaultIfNull(req.getPrintModes(), Lists.newArrayList()))
                .packageOneForOne(BooleanUtils.toBooleanDefaultIfNull(req.getPackageOneForOne(), true))
                .printSpuRange(Optional.ofNullable(req.getPrintSpuRange())
                        .orElse(PrintRangeEnum.ALL))
                .printCategoryList(ListUtils.defaultIfNull(req.getPrintCategoryList(), Lists.newArrayList()))
                .printSpuList(ListUtils.defaultIfNull(req.getPrintSpuList(), Lists.newArrayList()))
                // 前台票据配置
                .printCheckoutReceipt(req.getPrintFront())
                .checkoutReceiptPrintNum(Optional.ofNullable(req.getFrontPrintNum())
                        .orElse(1))
                .printPointMenuReceipt(Optional.ofNullable(req.getPrintPointMenuReceipt())
                        .orElse(Boolean.FALSE))
                .pointMenuReceiptPrintNum(Optional.ofNullable(req.getPointMenuReceiptPrintNum())
                        .orElse(1))
                .printAreaRange(Optional.ofNullable(req.getPrintAreaRange())
                        .orElse(PrintRangeEnum.ALL))
                .printAreaList(ListUtils.defaultIfNull(req.getPrintAreaIdList(), Lists.newArrayList()).stream()
                        .map(p -> PrintAreaVO.builder().areaId(p).build()).collect(Collectors.toList()))
                // 三方外卖配置
                .printTakeawayReceipt(Optional.ofNullable(req.getPrintTakeawayReceipt())
                        .orElse(Boolean.FALSE))
                .takeawayReceiptPrintNum(Optional.ofNullable(req.getTakeawayReceiptPrintNum())
                        .orElse(1))
                .build();
    }

    /**
     * DTO => VO
     * @param dto
     * @return
     */
    public static AppEdgeStorePrinterConfigVO toAppVO(StorePrinterConfigDTO dto) {

        if(Objects.isNull(dto)){
            return null;
        }

        return new AppEdgeStorePrinterConfigVO()
                // 基本信息
                .setConfigId(dto.getConfigId())
                .setPrinterName(dto.getPrinterName())
                .setPrinterType(dto.getPrinterType())
                .setPrinterIp(dto.getPrinterIp())
                .setPaperSize(dto.getPaperSize())
                // 后厨票据
                .setPrintBack(dto.getPrintBackKitchenReceipt())
                .setBackPrintNum(Optional.ofNullable(dto.getBackKitchenReceiptPrintNum())
                        .orElse(1))
                .setPrintModes(dto.getPrintModes())
                .setPackageOneForOne(dto.getPackageOneForOne())
                .setPrintCategoryRange(dto.getPrintCategoryRange())
                .setPrintCategoryIdList(dto.getPrintCategoryIdList())
                .setPrintSpuRange(dto.getPrintSpuRange())
                .setPrintSpuIdList(dto.getPrintSpuIdList())
                // 前台票据
                .setPrintFront(dto.getPrintCheckoutReceipt())
                .setFrontPrintNum(Optional.ofNullable(dto.getCheckoutReceiptPrintNum())
                        .orElse(1))
                .setPrintPointMenuReceipt(dto.getPrintPointMenuReceipt())
                .setPointMenuReceiptPrintNum(Optional.ofNullable(dto.getPointMenuReceiptPrintNum())
                        .orElse(1))
                .setPrintAreaRange(Optional.ofNullable(dto.getPrintAreaRange())
                        .orElse(PrintRangeEnum.ALL))
                .setPrintAreaIdList(dto.getPrintAreaIdList())
                // 三方外卖
                .setPrintTakeawayReceipt(dto.getPrintTakeawayReceipt())
                .setTakeawayReceiptPrintNum(Optional.ofNullable(dto.getTakeawayReceiptPrintNum())
                        .orElse(1));
    }

    /**
     * DTO =》VO
     * @param dto
     * @return
     */
    public static StorePrinterConfigVO toCashierVO(StorePrinterConfigDTO dto){
        if(Objects.isNull(dto)){
            return null;
        }

        List<PrintConfigVO> printConfigList = Stream.of(PrinterConfigTagEnum.values())
                .map(p -> {
                    PrintConfigVO configVO = new PrintConfigVO()
                            .setPrintScene(p);
                    switch (p) {
                        case PRINT_BACK_KITCHEN_RECEIPT:
                            configVO.setPrint(BooleanUtils.toBooleanDefaultIfNull(dto.getPrintBackKitchenReceipt(), false))
                                    .setPrintNum(Optional.ofNullable(dto.getBackKitchenReceiptPrintNum()).orElse(1))
                                    .setPrintModes(StringUtils.join(dto.getPrintModes(), ","))
                                    .setPackageOneForOne(BooleanUtils.toBooleanDefaultIfNull(dto.getPackageOneForOne(), true));

                            // 打印分类
                            configVO.setPrintCategoryIds(PrintRangeEnum.ALL == dto.getPrintCategoryRange() ? PrintRangeEnum.ALL.name() : "");
                            if(CollectionUtils.isNotEmpty(dto.getPrintCategoryIdList())){
                                configVO.setPrintCategoryIds(StringUtils.join(dto.getPrintCategoryIdList(), ","));
                            }

                            // 打印商品
                            configVO.setPrintSpuIds(PrintRangeEnum.ALL == dto.getPrintSpuRange() ? PrintRangeEnum.ALL.name() : "");
                            if(CollectionUtils.isNotEmpty(dto.getPrintSpuIdList())){
                                configVO.setPrintSpuIds(StringUtils.join(dto.getPrintSpuIdList(), ","));
                            }
                            break;
                        case PRINT_CHECKOUT_RECEIPT:
                            configVO.setPrint(BooleanUtils.toBooleanDefaultIfNull(dto.getPrintCheckoutReceipt(), false))
                                    .setPrintNum(Optional.ofNullable(dto.getCheckoutReceiptPrintNum()).orElse(1));
                            break;
                        case PRINT_POINT_MENU_RECEIPT:
                            configVO.setPrint(BooleanUtils.toBooleanDefaultIfNull(dto.getPrintPointMenuReceipt(), false))
                                    .setPrintNum(Optional.ofNullable(dto.getPointMenuReceiptPrintNum()).orElse(1));

                            // 打印区域
                            configVO.setPrintAreaIds(PrintRangeEnum.ALL == dto.getPrintAreaRange() ? PrintRangeEnum.ALL.name() : "");
                            if(CollectionUtils.isNotEmpty(dto.getPrintAreaIdList())){
                                configVO.setPrintAreaIds(StringUtils.join(dto.getPrintAreaIdList(), ","));
                            }
                            break;
                        case PRINT_TAKEAWAY_RECEIPT:
                            configVO.setPrint(BooleanUtils.toBooleanDefaultIfNull(dto.getPrintTakeawayReceipt(), false))
                                    .setPrintNum(Optional.ofNullable(dto.getTakeawayReceiptPrintNum()).orElse(1));
                            break;
                        default:
                            break;
                    }

                    return configVO;
                }).collect(Collectors.toList());

        return new StorePrinterConfigVO()
                .setConfigId(dto.getConfigId())
                .setMerchantId(dto.getMerchantId())
                .setStoreId(dto.getStoreId())
                .setPrinterName(dto.getPrinterName())
                .setPrinterType(dto.getPrinterType())
                .setPrinterIp(dto.getPrinterIp())
                .setPaperSize(dto.getPaperSize())
                .setPrintConfigs(printConfigList.stream().collect(Collectors.groupingBy(p -> p.getPrintScene().getScene())));
    }

}
