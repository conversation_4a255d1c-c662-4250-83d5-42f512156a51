package com.wosai.smartbiz.converter.gds.repast;

import com.wosai.market.dto.CategoryDTO;
import com.wosai.market.dto.category.CategoryDetail;
import com.wosai.smartbiz.enums.CashierCategoryTypeEnum;
import com.wosai.smartbiz.vo.gds.gds.v2.ProductCategoryVO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2020/12/30
 */
public class CategoryV2ConvertUtils {

    public static List<ProductCategoryVO> buildProductCategoryVOList(List<CategoryDetail> list){
        if(CollectionUtils.isEmpty(list)){
            return Collections.emptyList();
        }
        return list.stream()
                .map(CategoryV2ConvertUtils::buildProductCategoryVO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static ProductCategoryVO buildProductCategoryVO(CategoryDetail detail){
        if(Objects.isNull(detail)){
            return null;
        }
        ProductCategoryVO parent = buildProductCategoryVO(detail.getCategory());
        if(Objects.nonNull(parent)) {
            parent.setChildCategoryList(buildProductCategoryVO(detail.getSubCategoryList()));
        }
        return parent;
    }

    public static List<ProductCategoryVO> buildProductCategoryVO(List<CategoryDTO> list){
        if(CollectionUtils.isEmpty(list)){
            return Collections.emptyList();
        }
        return list.stream()
                .map(CategoryV2ConvertUtils::buildProductCategoryVO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    public static ProductCategoryVO buildProductCategoryVO(CategoryDTO dto){
        if(Objects.isNull(dto)){
            return null;
        }
        ProductCategoryVO vo = new ProductCategoryVO();
        vo.setCategoryId(dto.getCategoryId());
        vo.setCategoryType(CashierCategoryTypeEnum.toCashier(dto.getType()));
        vo.setCategoryName(dto.getCategoryName());
        vo.setDescription(dto.getDescription());
        vo.setSort(dto.getSort());
        return vo;
    }

}
