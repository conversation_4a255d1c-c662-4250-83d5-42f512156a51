package com.wosai.smartbiz.converter.gds.repast;

import com.google.common.collect.Lists;
import com.wosai.smartbiz.base.enums.YesNoEnum;
import com.wosai.smartbiz.dto.business.area.StoreTableAreaDTO;
import com.wosai.smartbiz.dto.business.table.StoreTableDTO;
import com.wosai.smartbiz.dto.req.table.StoreTableSaveReq;
import com.wosai.smartbiz.dto.vo.table.StoreAreaTableGroupVO;
import com.wosai.smartbiz.dto.vo.table.StoreTableAreaRpcVO;
import com.wosai.smartbiz.dto.vo.table.StoreTableAreaVO;
import com.wosai.smartbiz.gds.enums.TableStatusEnum;
import com.wosai.smartbiz.goods.domain.repast.table.StoreTableAreaDO;
import com.wosai.smartbiz.goods.domain.repast.table.StoreTableDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: KangYiJun
 * @date: 2020-09-01
 */
public class StoreTableAreaConverter {

    public static StoreTableDO convert(StoreTableSaveReq req){
        return new StoreTableDO()
                .setMerchantId(req.getMerchantId())
                .setStoreId(req.getStoreId())
                .setTableNo(req.getTableNo())
                .setSeatCount(req.getSeatCount())
                .setAreaId(req.getAreaId())
                .setId(req.getTableId())
                .setStatus(TableStatusEnum.FREE)
                .setBindQrCode(Objects.isNull(req.getTableId()) ? YesNoEnum.N : null)
                .setCreateBy(req.getMerchantUserId())
                .setModifiedBy(req.getMerchantUserId());
    }


    /**
     * 转化数 DTO =》VO
     * @param areaDTOList
     * @return
     */
    public static List<StoreTableAreaRpcVO> toRpcVOList(List<StoreTableAreaDTO> areaDTOList){
        if(CollectionUtils.isEmpty(areaDTOList)){
            return Lists.newArrayList();
        }
        return areaDTOList.stream()
                .map(StoreTableAreaConverter::toRpcVO)
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(StoreTableAreaRpcVO::getSort))
                .collect(Collectors.toList());
    }

    /**
     * 转化数据 DTO =》VO
     * @param areaDTO
     * @return
     */
    public static StoreTableAreaRpcVO toRpcVO(StoreTableAreaDTO areaDTO){
        if(Objects.isNull(areaDTO)){
            return null;
        }
        StoreTableAreaDO areaDO = areaDTO.getAreaDO();
        if(Objects.isNull(areaDO)){
            return null;
        }
        return StoreTableAreaRpcVO.builder()
                .areaId(areaDO.getId())
                .areaName(areaDO.getAreaName())
                .sort(areaDO.getSort())
                .tableCount(ListUtils.defaultIfNull(areaDTO.getTableDTOList(), Lists.newArrayList()).size())
                .build();
    }

    /**
     * 转化数据 DTO =》VO
     * @param areaDTOList
     * @return
     */
    public static List<StoreAreaTableGroupVO> toGroupVOList(List<StoreTableAreaDTO> areaDTOList){
        if(CollectionUtils.isEmpty(areaDTOList)){
            return Collections.emptyList();
        }
        return areaDTOList.stream()
                .filter(p -> Objects.nonNull(p.getAreaDO()))
                .map(areaDTO -> {
                    StoreTableAreaDO areaDO = areaDTO.getAreaDO();
                    return new StoreAreaTableGroupVO()
                            .setAreaId(areaDO.getId())
                            .setAreaName(areaDO.getAreaName())
                            .setSort(areaDO.getSort())
                            .setTableList(StoreTableConverter.toVOList(areaDTO.getTableDTOList()))
                            .setNotCleanCount(calculateNotCleanTableCount(areaDTO.getTableDTOList()));
                })
                .sorted(Comparator.comparing(StoreAreaTableGroupVO::getSort))
                .collect(Collectors.toList());
    }

    /**
     * 计算未清台的桌台数量
     * @param tableDTOList
     * @return
     */
    public static Integer calculateNotCleanTableCount(List<StoreTableDTO> tableDTOList) {
        if(CollectionUtils.isEmpty(tableDTOList)){
            return 0;
        }
        List<StoreTableDO> notCleanTableDOList = tableDTOList.stream()
                .map(StoreTableDTO::getTableDO)
                .filter(Objects::nonNull)
                .filter(p -> !TableStatusEnum.getCleanStatus().contains(p.getStatus()))
                .collect(Collectors.toList());
        return notCleanTableDOList.size();
    }

    /**
     * 转化数据 DTO =》VO
     * @param areaDTOList
     * @return
     */
    public static List<StoreTableAreaVO> toVOList(List<StoreTableAreaDTO> areaDTOList){
        if(CollectionUtils.isEmpty(areaDTOList)){
            return Lists.newArrayList();
        }
        return areaDTOList.stream()
                .filter(p -> Objects.nonNull(p.getAreaDO()))
                .map(areaDTO -> {
                    StoreTableAreaDO areaDO = areaDTO.getAreaDO();
                    return new StoreTableAreaVO()
                            .setAreaId(areaDO.getId())
                            .setAreaName(areaDO.getAreaName())
                            .setSort(areaDO.getSort());
                })
                .sorted(Comparator.comparing(StoreTableAreaVO::getSort))
                .collect(Collectors.toList());
    }

}
