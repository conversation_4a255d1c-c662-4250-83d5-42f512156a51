package com.wosai.smartbiz.strategy.config;


import com.wosai.smartbiz.gds.enums.config.CashierAutoSettleValueEnum;
import org.apache.commons.lang3.EnumUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 收银端支付后是否自动结账配置策略类
 * <AUTHOR>
 * @date 2020-12-03
 */
public class CashierAutoSettleConfigStrategy extends OrderConfigStrategy{

    private  static final Logger LOGGER = LoggerFactory.getLogger(CashierAutoSettleConfigStrategy.class);

    @Override
    public boolean isValueValid( String  value) {

        return EnumUtils.isValidEnum(CashierAutoSettleValueEnum.class, value);
    }


    @Override
    public String getDefaultValue() {
        return CashierAutoSettleValueEnum.Y.getCode();
    }

    @Override
    public Boolean getEnable() {
        return true;
    }
}
