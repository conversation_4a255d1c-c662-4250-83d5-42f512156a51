package com.wosai.smartbiz.strategy.product;

import com.wosai.market.merchant.dto.response.StoreStatusResponse;
import com.wosai.smartbiz.enums.StoreTypeEnum;

/**
 * 克隆商品策略
 */
public interface CloneProductStrategy {

    /**
     * 获取门店类型
     * @return
     */
    StoreTypeEnum getStoreType();

    /**
     * 克隆商品
     * @param sourceStore
     * @param targetStore
     * @param operatorId
     */
    Boolean cloneProductCheck(StoreStatusResponse sourceStore, StoreStatusResponse targetStore, String operatorId,String cloneMode);

}
