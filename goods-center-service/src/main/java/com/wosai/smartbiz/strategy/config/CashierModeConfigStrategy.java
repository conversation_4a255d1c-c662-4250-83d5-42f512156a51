package com.wosai.smartbiz.strategy.config;

import com.wosai.smartbiz.gds.enums.config.CashierModeValueEnum;
import org.apache.commons.lang3.EnumUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 是否是收银机模式
 * <AUTHOR>
 * @date 2021-4-23
 */
public class CashierModeConfigStrategy extends OrderConfigStrategy {

    private  static final Logger LOGGER = LoggerFactory.getLogger(CashierModeConfigStrategy.class);

    @Override
    public boolean isValueValid(String value) {

        return EnumUtils.isValidEnum(CashierModeValueEnum.class, value);
    }

    @Override
    public String getDefaultValue() {

        return CashierModeValueEnum.Y.getCode();
    }

    @Override
    public Boolean getEnable() {
        return true;
    }
}
