package com.wosai.smartbiz.strategy.config;

import com.wosai.smartbiz.gds.enums.config.MealTypeValueEnum;
import org.apache.commons.lang3.EnumUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * 就餐类型策略防范
 * <AUTHOR>
 * @date 2020-12-03
 */
public class MealTypeConfigStrategy extends OrderConfigStrategy{

    private  static final Logger LOGGER = LoggerFactory.getLogger(MealTypeConfigStrategy.class);

    @Override
    public boolean isValueValid( String  value) {

        return EnumUtils.isValidEnum(MealTypeValueEnum.class, value);
    }


    @Override
    public String getDefaultValue() {
        return MealTypeValueEnum.single.getCode();
    }

    @Override
    public Boolean getEnable() {
        return true;
    }

}
