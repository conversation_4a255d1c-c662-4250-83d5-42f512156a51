package com.wosai.smartbiz.strategy.config;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

/**
 * Y/N配置策略类
 * <AUTHOR>
 * @date 2020-12-03
 */
public class ZeroOneConfigStrategy extends OrderConfigStrategy{

    private  static final Logger LOGGER = LoggerFactory.getLogger(ZeroOneConfigStrategy.class);

    @Override
    public boolean isValueValid( String  value) {
        if (Objects.equals("1",value) || Objects.equals("0",value) ){
            return true;
        }
        return false;
    }


    @Override
    public String getDefaultValue() {
        return "0";
    }

    @Override
    public Boolean getEnable() {
        return true;
    }
}
