package com.wosai.smartbiz.strategy.config;

import com.wosai.smartbiz.gds.enums.config.SupportPackageValueEnum;
import org.apache.commons.lang3.EnumUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 是否支持打包配置策略类
 * <AUTHOR>
 * @date 2020-12-03
 */
public class SupportPackageConfigStrategy extends OrderConfigStrategy{

    private  static final Logger LOGGER = LoggerFactory.getLogger(SupportPackageConfigStrategy.class);


    @Override
    public boolean isValueValid(String value) {

        return EnumUtils.isValidEnum(SupportPackageValueEnum.class, value);
    }

    @Override
    public String getDefaultValue() {
        return SupportPackageValueEnum.N.getCode();
    }

    @Override
    public Boolean getEnable() {
        return true;
    }
}
