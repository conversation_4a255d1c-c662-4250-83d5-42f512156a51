package com.wosai.smartbiz.strategy.config;

import com.wosai.smartbiz.gds.enums.config.ScanAutoSettleValueEnum;
import org.apache.commons.lang3.EnumUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 扫码点餐支付后是否自动结账配置类
 * <AUTHOR>
 * @date 2020-12-03
 */
public class ScanAutoSettleConfigStrategy extends OrderConfigStrategy{


    private  static final Logger LOGGER = LoggerFactory.getLogger(ScanAutoSettleConfigStrategy.class);

    @Override
    public boolean isValueValid(String value) {

        return EnumUtils.isValidEnum(ScanAutoSettleValueEnum.class, value);
    }

    @Override
    public String getDefaultValue() {

        return ScanAutoSettleValueEnum.Y.getCode();
    }

    @Override
    public Boolean getEnable() {
        return true;
    }


}
