package com.wosai.smartbiz.strategy.config;

import com.wosai.smartbiz.gds.enums.config.ManualInputTakeNumEnum;
import org.apache.commons.lang3.EnumUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 是否手动输入取餐号策略类
 * <AUTHOR>
 * @date 2020-12-03
 */
public class ManualInputNoConfigStrategy extends OrderConfigStrategy implements CommonConfigStrategy{

    private  static final Logger LOGGER = LoggerFactory.getLogger(ManualInputNoConfigStrategy.class);

    @Override
    public boolean isValueValid( String  value) {


        return EnumUtils.isValidEnum(ManualInputTakeNumEnum.class, value);
    }


    @Override
    public String getDefaultValue() {
        return ManualInputTakeNumEnum.N.getCode();
    }

    @Override
    public Boolean getEnable() {
        return true;
    }
}
