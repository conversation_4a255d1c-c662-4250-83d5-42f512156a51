package com.wosai.smartbiz.strategy.config;

import com.wosai.smartbiz.enums.config.CommonConfigNameEnum;
import com.wosai.smartbiz.gds.enums.config.ConfigAppIdEnum;
import com.wosai.smartbiz.gds.enums.config.ConfigOwnerTypeEnum;

import java.util.ArrayList;
import java.util.List;

/**
 * 点餐配置策略类
 * <AUTHOR>
 * @date 2020-12-03
 */
public class OrderConfigStrategy implements CommonConfigStrategy{


    @Override
    public boolean isValueValid(String value) {
        return false;
    }

    @Override
    public ConfigAppIdEnum getAppId() {
        return ConfigAppIdEnum.UFOOD;
    }

    @Override
    public ConfigOwnerTypeEnum getOwnerType() {
        return ConfigOwnerTypeEnum.STORE_ID;
    }

    @Override
    public <T> T getDefaultValue() {
        return null;
    }

    public List<CommonConfigNameEnum> getConfigNameList(){
        List<CommonConfigNameEnum> list = new ArrayList<>();
        list.add(CommonConfigNameEnum.MEAL_TYPE);
        list.add(CommonConfigNameEnum.MANUAL_INPUT_TAKEOUT_NO);
        list.add(CommonConfigNameEnum.SUPPORT_PACKAGE);
        list.add(CommonConfigNameEnum.SCAN_ACTION);
        list.add(CommonConfigNameEnum.SCAN_AUTO_SETTLE_AFTER_PAY);
        list.add(CommonConfigNameEnum.CASHIER_AUTO_SETTLE_AFTER_PAY);
        list.add(CommonConfigNameEnum.AUTO_CLEAN_AFTER_SETTLE);
        list.add(CommonConfigNameEnum.SUBSCRIBE_ORDER);
        list.add(CommonConfigNameEnum.REFUND_WITHOUT_PWD);
        return list;
    }

    @Override
    public Boolean getEnable() {
        return null;
    }
}
