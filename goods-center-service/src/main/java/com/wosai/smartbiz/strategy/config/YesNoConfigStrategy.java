package com.wosai.smartbiz.strategy.config;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

/**
 * Y/N配置策略类
 * <AUTHOR>
 * @date 2020-12-03
 */
public class YesNoConfigStrategy extends OrderConfigStrategy{

    private  static final Logger LOGGER = LoggerFactory.getLogger(YesNoConfigStrategy.class);

    @Override
    public boolean isValueValid( String  value) {
        if (Objects.equals("Y",value) || Objects.equals("N",value) ){
            return true;
        }
        return false;
    }


    @Override
    public String getDefaultValue() {
        return "Y";
    }

    @Override
    public Boolean getEnable() {
        return true;
    }
}
