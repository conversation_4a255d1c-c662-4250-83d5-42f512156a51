package com.wosai.smartbiz.strategy.config;

import com.wosai.smartbiz.gds.enums.config.StoreTakeoutActivateStatValueEnum;
import org.apache.commons.lang3.EnumUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 是否开通了自营外卖
 * <AUTHOR>
 * @date 2021-4-23
 */
public class StoreTakeoutActivateStatConfigStrategy extends OrderConfigStrategy {

    private  static final Logger LOGGER = LoggerFactory.getLogger(StoreTakeoutActivateStatConfigStrategy.class);

    @Override
    public boolean isValueValid(String value) {

        return EnumUtils.isValidEnum(StoreTakeoutActivateStatValueEnum.class, value);
    }

    @Override
    public String getDefaultValue() {

        return StoreTakeoutActivateStatValueEnum.FALSE.getCode();
    }

    @Override
    public Boolean getEnable() {
        return true;
    }
}
