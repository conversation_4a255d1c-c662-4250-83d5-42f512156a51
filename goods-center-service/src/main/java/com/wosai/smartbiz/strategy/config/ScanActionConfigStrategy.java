package com.wosai.smartbiz.strategy.config;


import com.wosai.smartbiz.gds.enums.config.ScanActionValueEnum;
import org.apache.commons.lang3.EnumUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * 扫码后下一步动作策略类
 * <AUTHOR>
 * @date 2020-12-03
 */
public class ScanActionConfigStrategy extends OrderConfigStrategy {

    private  static final Logger LOGGER = LoggerFactory.getLogger(ScanActionConfigStrategy.class);


    @Override
    public boolean isValueValid(String value) {


        return EnumUtils.isValidEnum(ScanActionValueEnum.class, value);
    }


    @Override
    public String getDefaultValue() {
        return ScanActionValueEnum.SCAN_AND_PAY.getCode();
    }

    @Override
    public Boolean getEnable() {
        return true;
    }
}
