package com.wosai.smartbiz.strategy.product;

import com.wosai.market.merchant.api.StoreRemoteService;
import com.wosai.market.merchant.dto.response.StoreStatusResponse;
import com.wosai.market.merchant.dto.store.BizActivateStatusDTO;
import com.wosai.smart.goods.common.constant.ProductTaskStatusEnum;
import com.wosai.smart.goods.common.model.BizResult;
import com.wosai.smart.goods.enums.SpuStatusEnum;
import com.wosai.smart.goods.product.IProductRpcService;
import com.wosai.smart.goods.product.req.ProductReqWrapper;
import com.wosai.smart.goods.search.IProductSearchRpcService;
import com.wosai.smart.goods.task.IProductTaskRpcService;
import com.wosai.smart.goods.task.req.ProductCoverCloneTaskQueryReq;
import com.wosai.smart.goods.task.res.ProductTaskDTO;
import com.wosai.smartbiz.base.exceptions.BusinessException;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.enums.CloneTypeEnum;
import com.wosai.smartbiz.enums.StoreTypeEnum;
import com.wosai.smartbiz.gds.service.ProductMenuService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Service(value = "retailCloneProductStrategy")
@Slf4j
public class RetailCloneProductStrategy implements CloneProductStrategy {

    private static final String AUDIT_CHECK_UPAY_RELATION = "您不是门店 %s 的收银系统维护人，请修改后重新提交";

    @Autowired
    private ProductMenuService productMenuService;

    @Value("${crm.customer-relation.store-retail-user-config-code}")
    private String storeRetailUserConfigCode;

    @Autowired
    private IProductSearchRpcService productSearchRpcService;

    @Autowired
    private IProductTaskRpcService productTaskRpcService;

    @Autowired
    private IProductRpcService productRpcService;

    @Autowired
    private StoreRemoteService storeRemoteService;

    @Value("${smart.goods.retail.biz-code}")
    private String retailBizCode;

    @Value("${smart.goods.retail.biz-scene}")
    private String retailBizScene;


    @Override
    public StoreTypeEnum getStoreType() {
        return StoreTypeEnum.RETAIL;
    }

    @Override
    public Boolean cloneProductCheck(StoreStatusResponse sourceStore, StoreStatusResponse targetStore, String operatorId, String cloneMode) {
        if (Objects.equals(cloneMode, CloneTypeEnum.CLONE.getCode())) {
            throw new ParamException("零售门店不支持增量克隆");
        }
        // 判断门店业务类型是否符合
        BizActivateStatusDTO sourceActivateStatusDTO = storeRemoteService.getStoreActivateStatus(sourceStore.getStoreId());

        BizActivateStatusDTO targetStoreActivateStatusDTO = storeRemoteService.getStoreActivateStatus(targetStore.getStoreId());

        // 检查源门店是否同时激活了零售外卖和收银业务
        boolean sourceHasBoth = sourceActivateStatusDTO.isRetailTakeoutActivated() && sourceActivateStatusDTO.isRetailCashierActivated();
        // 目标门店激活了零售外卖或收银业务的条件
        boolean targetHasEither = targetStoreActivateStatusDTO.isRetailCashierActivated() || targetStoreActivateStatusDTO.isRetailTakeoutActivated();
        // 双方都激活了零售外卖的条件
        boolean bothHaveRetailTakeout = sourceActivateStatusDTO.isRetailTakeoutActivated() && targetStoreActivateStatusDTO.isRetailTakeoutActivated();
        boolean bothHaveRetailCashier = sourceActivateStatusDTO.isRetailCashierActivated() && targetStoreActivateStatusDTO.isRetailCashierActivated();

        // 根据源门店的激活情况分两部分判断
        if (sourceHasBoth) {
            // 如果源门店同时激活两种业务，目标门店需至少激活其中一种
            if (!targetHasEither) {
                throw new BusinessException("原门店与目标门店的商品售卖渠道不同，无法克隆");
            }
        } else {
            // 否则，检查双方是否都激活了相同的外卖业务（零售或普通外卖）
            if (!(bothHaveRetailTakeout || bothHaveRetailCashier)) {
                throw new BusinessException("原门店与目标门店的商品售卖渠道不同，无法克隆");
            }
        }
        // 查询是否有重复的任务
        ProductCoverCloneTaskQueryReq productCoverCloneTaskQueryReq = new ProductCoverCloneTaskQueryReq();
        List<Integer> status = Arrays.asList(ProductTaskStatusEnum.INIT.getCode(), ProductTaskStatusEnum.WAIT.getCode());
        productCoverCloneTaskQueryReq.setStatus(status);
        productCoverCloneTaskQueryReq.setBizCode(retailBizCode);
        productCoverCloneTaskQueryReq.setBizScene(retailBizScene);
        productCoverCloneTaskQueryReq.setStoreId(targetStore.getStoreId());
        productCoverCloneTaskQueryReq.setSourceStoreId(sourceStore.getStoreId());
        BizResult<ProductTaskDTO> result = productTaskRpcService.queryBySourceTargetStoreAndStatus(productCoverCloneTaskQueryReq);
        if (!result.isSuccess()) {
            throw new BusinessException("查询任务失败，请稍后重试");

        }
        if (result.getResult() != null) {
            throw new BusinessException("存在相同的克隆任务");
        }
        // 校验商品数量
        ProductReqWrapper<Integer> reqWrapper = new ProductReqWrapper<>();
        reqWrapper.setStoreId(sourceStore.getStoreId());
        reqWrapper.setBizParams(SpuStatusEnum.DEFAULT.getCode());
        BizResult<Long> countResult = productRpcService.countByStoreIdAndStatus(reqWrapper);
        if (!countResult.isSuccess()) {
            throw new BusinessException(String.format("查询,%s商品失败", sourceStore.getStoreSn()));

        }
        // 零时商品减掉
        if (countResult.getResult() != null && countResult.getResult() - 2 <= 0) {
            throw new BusinessException(String.format("%s商品数量为0", sourceStore.getStoreSn()));
        }
        return true;
    }

}
