package com.wosai.smartbiz.strategy.config;

import com.wosai.smartbiz.gds.enums.config.AutoCleanValueEnum;
import org.apache.commons.lang3.EnumUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 是否自动清台配置策略类
 * <AUTHOR>
 * @date 2020-12-03
 */
public class AutoCleanConfigStrategy extends OrderConfigStrategy {

    private  static final Logger LOGGER = LoggerFactory.getLogger(AutoCleanConfigStrategy.class);

    @Override
    public boolean isValueValid( String  value) {
        return EnumUtils.isValidEnum(AutoCleanValueEnum.class, value);
    }


    @Override
    public String getDefaultValue() {
        return AutoCleanValueEnum.Y.getCode();
    }

    @Override
    public Boolean getEnable() {
        return true;
    }
}
