package com.wosai.smartbiz.strategy.product;

import com.wosai.market.merchant.dto.response.StoreStatusResponse;
import com.wosai.market.service.store.StoreProductService;
import com.wosai.smartbiz.base.exceptions.BusinessException;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.enums.StoreTypeEnum;
import com.wosai.smartbiz.gds.service.ProductMenuService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service(value = "cateringCloneProductStrategy")
@Slf4j
public class CateringCloneProductStrategy implements CloneProductStrategy{



    private static final int LIMIT_ITEM_COUNT = 2000;

    private static final String AUDIT_CHECK_SOURCE_STORE_ITEM_TOO_MUCH = "门店超过2000个商品不能克隆";

    private static final String AUDIT_CHECK_SOURCE_STORE_ITEM_EMPTY = "来源门店 %s 没有商品，无法进行菜单克隆";

    private static final String AUDIT_CHECK_UPAY_RELATION = "您不是门店 %s 的点单维护人，请修改后重新提交";



    @Autowired
    private StoreProductService productService;

    @Autowired
    private ProductMenuService productMenuService;


    @Value("${crm.customer-relation.store-ufood-user-config-code}")
    private String storeUfoodUserConfigCode;


    @Override
    public StoreTypeEnum getStoreType() {
        return StoreTypeEnum.CATERING;
    }

    @Override
    public Boolean cloneProductCheck(StoreStatusResponse sourceStore, StoreStatusResponse targetStore, String operatorId,String cloneMode) {
        // 校验来源门店下是否存在商品
        int sourceStoreItemCount = Optional.ofNullable(productService.listSimpleSpuByStoreId(sourceStore.getStoreId())).map(Result::getData).map(List::size).orElse(0);
        if (sourceStoreItemCount <= 0) {
            throw new BusinessException(String.format(AUDIT_CHECK_SOURCE_STORE_ITEM_EMPTY, sourceStore.getStoreSn()));
        }
        // 校验来源门店下商品数量
        if (sourceStoreItemCount > LIMIT_ITEM_COUNT) {
            throw new BusinessException(AUDIT_CHECK_SOURCE_STORE_ITEM_TOO_MUCH);
        }
        return true;
    }


}
