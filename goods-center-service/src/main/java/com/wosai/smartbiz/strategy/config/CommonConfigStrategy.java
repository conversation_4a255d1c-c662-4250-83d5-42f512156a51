package com.wosai.smartbiz.strategy.config;

import com.wosai.smartbiz.gds.enums.config.ConfigAppIdEnum;
import com.wosai.smartbiz.gds.enums.config.ConfigOwnerTypeEnum;


public interface CommonConfigStrategy {

    /**
     * 比对配置值是否与配置项一致
     * @param value
     * @return
     */
    boolean isValueValid(String value);

    /**
     * 获取appId
     * @return
     */
    ConfigAppIdEnum getAppId();

    /**
     * 获取ownerType
     * @return
     */
    ConfigOwnerTypeEnum getOwnerType();

    /**
     * 获取默认值
     * @return
     */
    <T> T getDefaultValue();


    /**
     * 是否有效
     * @return
     */
    Boolean getEnable();

}
