package com.wosai.smartbiz.strategy.config;

import com.wosai.smartbiz.gds.enums.config.SupportPreOrderValueEnum;
import org.apache.commons.lang3.EnumUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 是否支持预约点餐配置策略类
 * <AUTHOR>
 * @date 2020-12-03
 */
public class SupportPreOrderConfigStrategy extends OrderConfigStrategy {

    private  static final Logger LOGGER = LoggerFactory.getLogger(ScanActionConfigStrategy.class);

    @Override
    public boolean isValueValid(String value) {

        return EnumUtils.isValidEnum(SupportPreOrderValueEnum.class, value);
    }

    @Override
    public String getDefaultValue() {

        return SupportPreOrderValueEnum.Y.getCode();
    }

    @Override
    public Boolean getEnable() {
        return true;
    }
}
