package com.wosai.smartbiz.utils;

import com.alibaba.fastjson.JSON;
import com.wosai.market.mcc.api.dto.request.CreateConfigRequest;
import com.wosai.market.mcc.api.enums.AppId;
import com.wosai.market.mcc.api.enums.OwnerType;
import com.wosai.smartbiz.base.enums.YesNoEnum;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.goods.constants.MccConfigConstants;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2021/1/19
 */
public class MccConfigValueValidHelper {

    /**
     * 校验备注列表是否有效
     * @param value
     * @return
     */
    public static boolean validateRemarkList(String value){
        List<String> textList = JSON.parseArray(value, String.class);
        if(CollectionUtils.isEmpty(textList)){
            return true;
        }
        // 长度判断
        boolean tooLong = textList.stream()
                .anyMatch(t -> t.length() > 30);
        if(tooLong){
            throw new ParamException("单条备注长度不能超过30字");
        }
        // 判重处理
        Set<String> textSet = new HashSet<>(textList);
        if(textSet.size() < textList.size()){
            throw new ParamException("备注信息不能重复");
        }
        return true;
    }

    /**
     * 校验开关的配置值
     * @param value
     * @return
     */
    public static boolean validateSwitch(String value){
        // 兼容原来的YN策略
        if(EnumUtils.isValidEnum(YesNoEnum.class, value)){
            return true;
        }
        // 是否是开关对应的值
        return StringUtils.equals(MccConfigConstants.SWITCH_CLOSE, value)
                || StringUtils.equals(MccConfigConstants.SWITCH_OPEN, value);
    }

    /**
     * 校验是否的配置值
     * @param value
     * @return
     */
    public static boolean validateYesOrNo(String value){
        return StringUtils.equals(MccConfigConstants.NO, value)
                || StringUtils.equals(MccConfigConstants.YES, value);
    }

    /**
     * 构造配置请求对象
     * @param appId
     * @param ownerType
     * @param ownerId
     * @param name
     * @param value
     * @return
     */
    public static CreateConfigRequest buildSaveRequest(AppId appId, OwnerType ownerType, String ownerId, String name, String value){
        CreateConfigRequest request = new CreateConfigRequest();
        request.setAppId(appId.getAppId());
        request.setOwnerType(ownerType.getOwnerType());
        request.setOwnerId(ownerId);
        request.setName(name);
        request.setValue(value);
        request.setEnabled(Boolean.TRUE);
        return request;
    }

}
