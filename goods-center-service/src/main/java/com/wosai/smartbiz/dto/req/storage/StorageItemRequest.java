package com.wosai.smartbiz.dto.req.storage;

import com.wosai.smartbiz.dto.res.storage.StorageItemDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class StorageItemRequest {

    /**
     * 商户ID
     */
    @NotBlank(message = "商户ID不能为空")
    private String merchantId;

    /**
     * 门店ID
     */
    @NotBlank(message = "门店ID不能为空")
    private String storeId;

    /**
     * 寄存商品列表
     */
    @NotEmpty(message = "寄存商品列表不能为空")
    private List<StorageItemDTO> itemList;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    private String phoneNumber;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空")
    private String name;

    /**
     * 寄存天数    7 、15、30、60、90、长期(-1)
     */
    @NotNull(message = "寄存天数不能为空")
    private Integer storageDays;

    /**
     * 服务员id
     */
    private String waiterId;

    /**
     * 服务员姓名
     */
    private String waiterName;

    /**
     * 操作人id
     */
    private String operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 备注信息
     */
    private String remark;

}
