package com.wosai.smartbiz;

import com.wosai.database.instrumentation.springboot.v2.EnableDataSourceTranslate;
import com.wosai.web.rpc.EnableJsonRpc;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableRetry
@EnableJsonRpc
@EnableScheduling
@EnableDataSourceTranslate
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
