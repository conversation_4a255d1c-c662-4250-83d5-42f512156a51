package com.wosai.smartbiz.retail.jsonrpc.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.saas.customer.model.MerchantAvailableStoreSetDto;
import com.shouqianba.saas.customer.service.MemberAvailableStoreService;
import com.wosai.marketing.merchant.representation.MerchantMemberOpenRepresentation;
import com.wosai.marketing.merchant.service.MerchantMemberOpenService;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.base.apisix.cashier.CashierBaseParam;
import com.wosai.smartbiz.gds.enums.MemberActivityStatusEnum;
import com.wosai.smartbiz.retail.dto.StoreMemberActivityStatusRes;
import com.wosai.smartbiz.retail.jsonrpc.CashierMarketingRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @create 2024/3/11
 */
@Slf4j
@Service
@AutoJsonRpcServiceImpl
public class CashierMarketingRpcServiceImpl implements CashierMarketingRpcService {

    /**
     * 商户会员折扣活动开启状态
     */
    public static final int MEMBER_DISCOUNT_OPEN_STATUS = 1;

    /**
     * 门店参与商户会员折扣活动状态
     */
    public static final int STORE_MEMBER_DISCOUNT_AVAILABLE_STATUS = 1;

    @Autowired
    private MerchantMemberOpenService merchantMemberOpenService;

    @Autowired
    private MemberAvailableStoreService memberAvailableStoreService;

    @Override
    public StoreMemberActivityStatusRes queryStoreMemberActivityStatus(CashierApiRequest<CashierBaseParam> request) {
        StoreMemberActivityStatusRes res = new StoreMemberActivityStatusRes();
        res.setOpenStatus(MemberActivityStatusEnum.NOT_OPEN.getCode());
        // 先判断商户是否开启会员折扣活动
        String merchantId = request.getUser().getMerchantId();
        boolean openMemberDiscount = false;
        try {
            MerchantMemberOpenRepresentation memberOpenRep = merchantMemberOpenService.queryMerchantMemberOpenStatus(merchantId);
            openMemberDiscount = Optional.ofNullable(memberOpenRep).map(p -> Objects.equals(p.getStatus(), MEMBER_DISCOUNT_OPEN_STATUS)).orElse(false);
        } catch (Exception ex) {
            log.error("query merchant open member activity error, merchantId:{}", merchantId, ex);
        }
        if (!openMemberDiscount) {
            return res;
        }
        // 再判断当前门店是否参与了该活动
        String storeId = request.getBody().getStoreId();
        try {
            List<MerchantAvailableStoreSetDto> stores = memberAvailableStoreService.getAvailableStoreByMerchantId(merchantId);
            if (CollectionUtils.isNotEmpty(stores)) {
                openMemberDiscount = stores.stream().anyMatch(s -> Objects.equals(s.getStoreId(), storeId) && Objects.equals(s.getAvailableStatus(), STORE_MEMBER_DISCOUNT_AVAILABLE_STATUS));
                if (openMemberDiscount) {
                    res.setOpenStatus(MemberActivityStatusEnum.OPEN.getCode());
                }
            }
        } catch (Exception ex) {
            log.error("get member activity available store by merchantId error", ex);
        }
        return res;
    }

}
