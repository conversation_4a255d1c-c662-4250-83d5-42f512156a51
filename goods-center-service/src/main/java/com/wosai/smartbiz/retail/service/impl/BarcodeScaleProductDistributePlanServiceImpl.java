package com.wosai.smartbiz.retail.service.impl;

import com.wosai.smartbiz.apollo.pojo.BarcodeScaleDistributeBarcodeFormatConfig;
import com.wosai.smartbiz.base.exceptions.BusinessException;
import com.wosai.smartbiz.goods.direct.BarcodeScaleProductDistributePlanDAO;
import com.wosai.smartbiz.goods.domain.BarcodeScaleProductDistributePlanDO;
import com.wosai.smartbiz.retail.dto.BarcodeScaleProductDistributePlanDTO;
import com.wosai.smartbiz.retail.service.BarcodeScaleProductDistributePlanService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @<PERSON>
 * @create 2024/5/16
 */
@Service
public class BarcodeScaleProductDistributePlanServiceImpl implements BarcodeScaleProductDistributePlanService {

    private static final int MAX_PLAN_COUNT = 50;

    @Autowired
    private BarcodeScaleDistributeBarcodeFormatConfig barcodeFormatConfig;

    @Autowired
    private BarcodeScaleProductDistributePlanDAO barcodeScaleProductDistributePlanDAO;

    @Override
    public Long create(BarcodeScaleProductDistributePlanDTO planDTO) {
        String storeId = planDTO.getStoreId();

        // 判断数量是否达到上限
        int existCount = barcodeScaleProductDistributePlanDAO.countByStoreId(storeId);
        if (existCount >= MAX_PLAN_COUNT) {
            throw new BusinessException(String.format("最多添加%s个下发方案", MAX_PLAN_COUNT));
        }

        // 判断条码规范是否合法
        if (StringUtils.isNotBlank(planDTO.getBarcodeFormat())) {
            if (!barcodeFormatConfig.getContentMap().containsKey(planDTO.getBarcodeFormat())) {
                throw new BusinessException("条码规范不合法");
            }
        }

        // 判断名称是否已被占用
        // 备注：产品要求去除该校验
//        List<BarcodeScaleProductDistributePlanDO> dbRecords = barcodeScaleProductDistributePlanDAO.queryByStoreIdAndTitle(storeId, planDTO.getTitle());
//        if (CollectionUtils.isNotEmpty(dbRecords)) {
//            throw new BusinessException("方案名称已存在");
//        }

        // 插入数据
        BarcodeScaleProductDistributePlanDO planDO = new BarcodeScaleProductDistributePlanDO();
        planDO.setStoreId(storeId);
        planDO.setBizScene(planDTO.getBizScene());
        planDO.setTitle(planDTO.getTitle());
        planDO.setBarcodeFormat(planDTO.getBarcodeFormat());
        planDO.setProducts(planDTO.getProducts());
        boolean insertSuc = barcodeScaleProductDistributePlanDAO.insert(planDO) > 0;
        if (!insertSuc) {
            throw new BusinessException("保存失败，请稍后重试");
        }

        return planDO.getId();
    }

    @Override
    public Long update(BarcodeScaleProductDistributePlanDTO planDTO) {
        Long planId = planDTO.getPlanId();
        String storeId = planDTO.getStoreId();

        // 判断方案是否属于该门店
        BarcodeScaleProductDistributePlanDO dbPlanDO = barcodeScaleProductDistributePlanDAO.queryById(planId);
        if (Objects.isNull(dbPlanDO) || !StringUtils.equals(dbPlanDO.getStoreId(), storeId)) {
            throw new BusinessException("方案不存在");
        }

        // 判断条码规范是否合法
        if (StringUtils.isNotBlank(planDTO.getBarcodeFormat())) {
            if (!barcodeFormatConfig.getContentMap().containsKey(planDTO.getBarcodeFormat())) {
                throw new BusinessException("条码规范不合法");
            }
        }

        // 判断名称是否已被占用
        // 备注：产品要求去除该校验
//        List<BarcodeScaleProductDistributePlanDO> dbRecords = barcodeScaleProductDistributePlanDAO.queryByStoreIdAndTitle(storeId, planDTO.getTitle());
//        if (CollectionUtils.isNotEmpty(dbRecords)) {
//            boolean titleExist = dbRecords.stream().anyMatch(dr -> !Objects.equals(dr.getId(), planId));
//            if (titleExist) {
//                throw new BusinessException("方案名称已存在");
//            }
//        }

        // 执行更新
        BarcodeScaleProductDistributePlanDO planDO = new BarcodeScaleProductDistributePlanDO();
        planDO.setId(planId);
        planDO.setTitle(planDTO.getTitle());
        planDO.setBarcodeFormat(planDTO.getBarcodeFormat());
        planDO.setProducts(planDTO.getProducts());
        boolean updateSuc = barcodeScaleProductDistributePlanDAO.update(planDO) > 0;
        if (!updateSuc) {
            throw new BusinessException("保存失败，请稍后重试");
        }

        return planId;
    }

    @Override
    public List<BarcodeScaleProductDistributePlanDTO> queryByStoreId(String storeId) {
        if (StringUtils.isBlank(storeId)) {
            return Collections.emptyList();
        }
        List<BarcodeScaleProductDistributePlanDO> dbRecords = barcodeScaleProductDistributePlanDAO.queryByStoreId(storeId);
        if (CollectionUtils.isEmpty(dbRecords)) {
            return Collections.emptyList();
        }
        return dbRecords.stream()
                .map(r -> {
                    BarcodeScaleProductDistributePlanDTO dto = new BarcodeScaleProductDistributePlanDTO();
                    dto.setPlanId(r.getId());
                    dto.setBizScene(r.getBizScene());
                    dto.setStoreId(r.getStoreId());
                    dto.setTitle(r.getTitle());
                    dto.setBarcodeFormat(r.getBarcodeFormat());
                    dto.setProducts(r.getProducts());
                    dto.setCtime(r.getCtime());
                    dto.setMtime(r.getMtime());
                    return dto;
                }).collect(Collectors.toList());
    }
}
