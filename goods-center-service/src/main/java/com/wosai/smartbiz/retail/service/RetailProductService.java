package com.wosai.smartbiz.retail.service;

import com.wosai.smart.goods.dto.CursorResult;
import com.wosai.smartbiz.base.res.CursorQueryResult;
import com.wosai.smartbiz.gds.dto.CashierRetailProduct;
import com.wosai.smartbiz.gds.dto.CashierRetailProductStock;
import com.wosai.smartbiz.retail.dto.*;

import java.util.List;
import java.util.Map;

public interface RetailProductService {

    /**
     * 同步门店下存量商品
     * @param param
     * @return
     */
    CursorResult<CashierRetailProduct> syncExist(CashierRetailProductSyncParam param);

    /**
     * 同步门店下删除商品
     * @param param
     * @return
     */
    CursorResult<CashierRetailProduct> syncDeleted(CashierRetailProductSyncParam param);

    /**
     * 同步门店下所有商品
     * @param param
     * @return
     */
    CursorResult<CashierRetailProduct> syncAll(CashierRetailProductSyncParam param);

    /**
     * 获取门店下的临时商品
     */
    List<CashierRetailProduct> queryTempProducts(String storeId);

    /**
     * 获取门店下指定ID商品信息
     */
    Map<Long, CashierRetailProduct> queryExistProductBySpuIds(String storeId, List<Long> spuIds);

    /**
     * 查询指定条码商品
     * @param storeId
     * @param barcode
     * @return
     */
    CashierRetailProduct queryByBarcode(String storeId, String barcode);

    /**
     * 创建商品
     * @param storeId
     * @param filingData
     * @return
     */
    ProductCreateResult create(String storeId, ProductFilingData filingData);

    /**
     * 更新商品，仅更新部分字段
     * @param storeId
     * @param info
     * @return
     */
    ProductUpdateResult updateSelective(String storeId, CashierRetailProductPartialUpdateParam.Info info,CashierRetailProductPartialUpdateParam.ClientInfo clientInfo,String merchantUserId);

    /**
     * 查询指定商品
     * @param storeId
     * @param spuId
     * @return
     */
    CashierRetailProduct queryBySpuId(String storeId, Long spuId);

    /**
     * 查询门店下商品库存
     * @param param
     * @return
     */
    CursorResult<CashierRetailProductStock> pageQueryProductStock(CashierRetailProductStockQueryParam param);


    /**
     * 根据游标查询商品库存
     * @param param
     * @return
     */
    CursorQueryResult<CashierRetailProductStock> pageQueryProductStockCursor(CashierRetailProductStockCursorQueryParam param);


}
