package com.wosai.smartbiz.retail.service.impl;

import com.wosai.market.mcc.api.dto.request.WhiteListV2Request;
import com.wosai.market.mcc.api.dto.response.SpecificListResponse;
import com.wosai.market.mcc.api.service.WhiteListV2RemoteService;
import com.wosai.smartbiz.base.enums.SceneEnum;
import com.wosai.smartbiz.gds.enums.WhiteTypeEnum;
import com.wosai.smartbiz.gds.vo.WhiteConfigVO;
import com.wosai.smartbiz.retail.service.WhiteService;
import com.wosai.smartbiz.uc.manager.StoreV2Service;
import com.wosai.smartbiz.user.user.vo.StoreV2VO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @author: huqiaomu
 * @createDate: 2024/1/24
 */
@Service
@Slf4j
public class WhiteServiceImpl implements WhiteService {

    @Autowired
    private WhiteListV2RemoteService mccWhiteListV2RemoteService;

    @Autowired
    private StoreV2Service storeV2Service;

    @Value("${white.ai-business-type}")
    private String aiWhiteBusinessType;

    @Override
    public List<WhiteConfigVO> whiteList(SceneEnum scene, String storeId) {
        StoreV2VO storeInfo = storeV2Service.getSimpleStoreByStoreId(storeId);
        WhiteListV2Request aiWhiteRequest = new WhiteListV2Request();
        aiWhiteRequest.setBusinessId(storeInfo.getStoreCode());
        if (Objects.equals(scene, SceneEnum.RETAIL)) {
            aiWhiteRequest.setBusinessType(aiWhiteBusinessType);
        }
        List<WhiteListV2Request> whiteRequestList = new ArrayList<>(Collections.singleton(aiWhiteRequest));

        List<SpecificListResponse> specificListResponses = mccWhiteListV2RemoteService.batchFindWhiteListConfig(whiteRequestList);
        List<WhiteConfigVO> whiteConfigVOList = new ArrayList<>();
        specificListResponses.forEach(t -> {
            WhiteConfigVO whiteConfigVO = new WhiteConfigVO();
            if (Objects.equals(t.getBusinessType(), aiWhiteBusinessType)) {
                whiteConfigVO.setWhiteType(WhiteTypeEnum.AI);
                whiteConfigVO.setIsWhite(true);
                whiteConfigVOList.add(whiteConfigVO);
            }
        });
        return whiteConfigVOList;
    }
}
