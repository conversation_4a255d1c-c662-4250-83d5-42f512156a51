package com.wosai.smartbiz.retail.jsonrpc;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.smart.goods.dto.CursorResult;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.base.apisix.cashier.CashierBaseParam;
import com.wosai.smartbiz.gds.dto.CashierProductWaterLine;
import com.wosai.smartbiz.gds.dto.CashierRetailProduct;
import com.wosai.smartbiz.gds.dto.CashierRetailProductStock;
import com.wosai.smartbiz.retail.dto.*;
import com.wosai.web.api.ListResult;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @create 2023/5/15
 */
@JsonRpcService(value = "/rpc/retail/cashier/product")
@Validated
public interface CashierRetailProductRpcService {

    /**
     * 同步商品
     */
    @Deprecated
    CursorResult<CashierRetailProduct> sync(@Valid CashierApiRequest<CashierRetailProductSyncParam> request);

    /**
     * 同步已删除的商品
     */
    @Deprecated
    CursorResult<CashierRetailProduct> syncDeleted(@Valid CashierApiRequest<CashierRetailProductSyncParam> request);

    /**
     * 同步临时商品
     */
    @Deprecated
    ListResult<CashierRetailProduct> syncTemp(@Valid CashierApiRequest<CashierBaseParam> request);

    /**
     * 同步全部商品
     * 商品类型包括：普通商品、临时商品
     * 商品状态包括：已删除、未删除
     */
    CursorResult<CashierRetailProduct> syncAll(@Valid CashierApiRequest<CashierRetailProductSyncParam> request);

    /**
     * 最新水位线
     */
    CashierProductWaterLine latestWaterLine(@Valid CashierApiRequest<CashierBaseParam> request);

    /**
     * 判断条码对应的商品是否需要建档
     */
    ProductCheckCreateResult checkCreateByBarcode(@Valid CashierApiRequest<CashierRetailProductCheckCreateByBarcodeParam> request);

    /**
     * 创建商品
     */
    ProductCreateResult create(@Valid CashierApiRequest<CashierRetailProductCreateParam> request);

    /**
     * 编辑商品
     */
    @Deprecated
    ProductUpdateResult update(@Valid CashierApiRequest<CashierRetailProductUpdateParam> request);

    /**
     * 部分更新商品
     */
    ProductUpdateResult partialUpdate(@Valid CashierApiRequest<CashierRetailProductPartialUpdateParam> request);

    /**
     * 分页查询门店下商品库存
     */
    CursorResult<CashierRetailProductStock> pageQueryProductStock(@Valid CashierApiRequest<CashierRetailProductStockQueryParam> request);



}
