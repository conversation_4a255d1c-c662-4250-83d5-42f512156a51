package com.wosai.smartbiz.retail.jsonrpc;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.base.apisix.cashier.CashierBaseParam;
import com.wosai.smartbiz.retail.dto.BarcodeScaleProductDistributePlanCreateReq;
import com.wosai.smartbiz.retail.dto.BarcodeScaleProductDistributePlanRes;
import com.wosai.smartbiz.retail.dto.BarcodeScaleProductDistributePlanSaveRes;
import com.wosai.smartbiz.retail.dto.BarcodeScaleProductDistributePlanUpdateReq;
import com.wosai.web.api.ListResult;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;

/**
 * 条码秤商品下发方案RPC接口
 */
@JsonRpcService(value = "/rpc/barcodescale/product-distribute-plan")
@Validated
public interface BarcodeScaleProductDistributePlanRpcService {

    /**
     * 新建
     */
    BarcodeScaleProductDistributePlanSaveRes create(@Valid CashierApiRequest<BarcodeScaleProductDistributePlanCreateReq> request);

    /**
     * 更新
     */
    BarcodeScaleProductDistributePlanSaveRes update(@Valid CashierApiRequest<BarcodeScaleProductDistributePlanUpdateReq> request);

    /**
     * 查询
     */
    ListResult<BarcodeScaleProductDistributePlanRes> query(@Valid CashierApiRequest<CashierBaseParam> request);

}
