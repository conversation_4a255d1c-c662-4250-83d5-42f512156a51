package com.wosai.smartbiz.retail.jsonrpc;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.base.apisix.cashier.CashierBaseParam;
import com.wosai.smartbiz.gds.vo.WhiteConfigVO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;

/**
 * @author: huqiaomu
 * @createDate: 2024/1/23
 */
@JsonRpcService(value = "/rpc/retail/cashier/white")
@Validated
public interface CashierRetailWhiteRpcService {

    /**
     * 批量查询白名单
     * @param request
     * @return
     */
    List<WhiteConfigVO> retailWhiteList(@Valid CashierApiRequest<CashierBaseParam> request);
}
