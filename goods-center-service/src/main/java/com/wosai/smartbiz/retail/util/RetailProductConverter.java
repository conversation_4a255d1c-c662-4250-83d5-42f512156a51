package com.wosai.smartbiz.retail.util;

import com.alibaba.fastjson.JSON;
import com.wosai.market.merchant.dto.store.BizActivateStatusDTO;
import com.wosai.smart.goods.common.constant.ProductConstant;
import com.wosai.smart.goods.common.constant.SaleChannelFlag;
import com.wosai.smart.goods.common.constant.UnitConstant;
import com.wosai.smart.goods.common.utils.SellWeightUtils;
import com.wosai.smart.goods.constant.CommonConstant;
import com.wosai.smart.goods.constant.TemplateCodeConstant;
import com.wosai.smart.goods.constant.TemplateDefaultPropConstant;
import com.wosai.smart.goods.enums.*;
import com.wosai.smart.goods.product.dto.PriceDTO;
import com.wosai.smart.goods.product.dto.ProductDTO;
import com.wosai.smart.goods.product.dto.SkuDTO;
import com.wosai.smart.goods.product.dto.SpuDTO;
import com.wosai.smart.goods.product.req.ProductSaveReq;
import com.wosai.smartbiz.gds.dto.CashierRetailCategory;
import com.wosai.smartbiz.gds.dto.CashierRetailProduct;
import com.wosai.smartbiz.gds.dto.CashierRetailProductSnapshot;
import com.wosai.smartbiz.retail.dto.ProductFilingData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: redhat
 */
public class RetailProductConverter {

    /**
     * 保质期
     */
    public static final String EXPIRATION_DATE = "expiration_date";

    /**
     * 产地
     */
    public static final String ORIGIN_AREA = "origin_area";

    /**
     * 规格描述
     */
    public static final String SPEC_DESC = "spec_desc";

    /**
     * 规格描述
     */
    public static final String SHELF_LIFE_UNIT = "shelf_life_unit";

    public static ProductFilingData toProductFilingData(CashierRetailProduct storeGoods) {
        Objects.requireNonNull(storeGoods);
        ProductFilingData filingData = new ProductFilingData();
        filingData.setSpuId(storeGoods.getSpuId());
        filingData.setSeq(storeGoods.getSeq());
        filingData.setSpuTitle(storeGoods.getSpuTitle());
        filingData.setSpuType(storeGoods.getSaleType());
        filingData.setPinyin(storeGoods.getPinyin());
        filingData.setSaleStatus(storeGoods.getSaleStatus());
        filingData.setBarcode(storeGoods.getBarcode());
        filingData.setExtendBarcodes(storeGoods.getExtendBarcodes());
        filingData.setIsStandard(storeGoods.getIsStandard());
        filingData.setSystemCode(storeGoods.getSystemCode());
        filingData.setCoverImage(storeGoods.getCoverImage());
        filingData.setSaleUnit(storeGoods.getSaleUnit());
        filingData.setSalePrice(storeGoods.getSalePrice());
        filingData.setMemberPrice(storeGoods.getMemberPrice());
        filingData.setDescription(storeGoods.getDescription());
        filingData.setProducer(storeGoods.getProducer());
        filingData.setSpec(storeGoods.getSpec());
        filingData.setCategory(storeGoods.getCategory());
        filingData.setCtime(storeGoods.getCtime());
        filingData.setMtime(storeGoods.getMtime());
        filingData.setCostPrice(storeGoods.getCostPrice());
        filingData.setPurchasePrice(storeGoods.getPurchasePrice());
        filingData.setProductType(storeGoods.getProductType());
        filingData.setSaleType(storeGoods.getSaleType());
        filingData.setSnapshot(storeGoods.getSnapshot());
        filingData.setDeleted(storeGoods.isDeleted());
        filingData.setPluCode(storeGoods.getPluCode());
        filingData.setSaleChannels(storeGoods.getSaleChannels());
        filingData.setTakeoutSalePrice(storeGoods.getTakeoutSalePrice());
        filingData.setTakeoutMemberPrice(storeGoods.getTakeoutMemberPrice());
        filingData.setTakeoutPackFee(storeGoods.getTakeoutPackFee());
        filingData.setPerTakeoutWeight(storeGoods.getPerTakeoutWeight());
        filingData.setTakeoutWeightUnit(storeGoods.getTakeoutWeightUnit());
        return filingData;
    }

    /**
     * 判断商品是否是删除状态
     */
    public static boolean productNeed2Delete(SpuDTO spuDTO) {
        Objects.requireNonNull(spuDTO);
        if (!Objects.equals(spuDTO.getStatus(), SpuStatusEnum.DEFAULT.getCode())) {
            return true;
        }
        return !StringUtils.equals(spuDTO.getTemplateCode(), TemplateCodeConstant.RETAIL);
    }

    public static CashierRetailProduct smartGoodsProductDTO2CashierRetailProduct(ProductDTO productDTO) {
        Objects.requireNonNull(productDTO);
        CashierRetailProduct retailProduct = new CashierRetailProduct();

        // 基础信息
        SpuDTO spuDTO = productDTO.getSpu();
        retailProduct.setSpuId(spuDTO.getSpuId());
        retailProduct.setSeq(spuDTO.getSeq());
        retailProduct.setSpuTitle(spuDTO.getTitle());
        retailProduct.setPinyin(spuDTO.getPinyin());
        retailProduct.setSaleStatus(spuDTO.getSaleStatus());
        retailProduct.setCoverImage(spuDTO.getCoverImage());
        retailProduct.setSaleUnit(spuDTO.getUnit());
        retailProduct.setDescription(spuDTO.getDescription());
        retailProduct.setCtime(spuDTO.getCtime());
        retailProduct.setMtime(spuDTO.getMtime());
        retailProduct.setProductType(spuDTO.getProductType());
        retailProduct.setSaleType(spuDTO.getSaleType());
        retailProduct.setDeleted(productNeed2Delete(spuDTO));
        retailProduct.setSaleChannels(Optional.ofNullable(spuDTO.getSaleChannelFlag()).map(SaleChannelFlag::getValue).orElse(2L));

        // 条码&简码
        retailProduct.setBarcode(spuDTO.getBarcode());
        retailProduct.setExtendBarcodes(spuDTO.getExtendBarcodes());
        retailProduct.setIsStandard(StringUtils.isNotEmpty(spuDTO.getBarcode()));
        retailProduct.setSystemCode(spuDTO.getSysCode());
        retailProduct.setPluCode(spuDTO.getPluCode());

        // 归属分类
        if (Objects.nonNull(spuDTO.getCategoryId())) {
            CashierRetailProduct.Category category = new CashierRetailProduct.Category();
            category.setId(spuDTO.getCategoryId());
            retailProduct.setCategory(category);
        }

        // 保质期
        Map<String, Object> templateProps = spuDTO.getTemplateProps();
        if (templateProps != null && !templateProps.isEmpty()) {
            retailProduct.setExpirationDate(MapUtils.getLong(templateProps, EXPIRATION_DATE));
            retailProduct.setProducer(MapUtils.getString(templateProps, ORIGIN_AREA));
            retailProduct.setSpec(MapUtils.getString(templateProps, SPEC_DESC));
            retailProduct.setExpirationDateUnit(MapUtils.getString(templateProps, SHELF_LIFE_UNIT));
        }


        // 规格&价格，目前零售商品只有单规格
        for (SkuDTO skuDTO : productDTO.getSkus()) {
            Map<String, Long> priceMap = priceDTOList2Map(skuDTO.getPriceInfo());
            retailProduct.setSalePrice(MapUtils.getLong(priceMap, ProductConstant.PriceType.SALE_PRICE.getCode()));
            retailProduct.setCostPrice(MapUtils.getLong(priceMap, ProductConstant.PriceType.COST_PRICE.getCode()));
            retailProduct.setPurchasePrice(MapUtils.getLong(priceMap, ProductConstant.PriceType.PURCHASE_PRICE.getCode()));
            retailProduct.setMemberPrice(MapUtils.getLong(priceMap, ProductConstant.PriceType.MEMBER_PRICE.getCode()));
            retailProduct.setTakeoutSalePrice(MapUtils.getLong(priceMap, ProductConstant.PriceType.TAKEOUT_PRICE.getCode()));
            retailProduct.setTakeoutMemberPrice(MapUtils.getLong(priceMap, ProductConstant.PriceType.TAKEOUT_MEMBER_PRICE.getCode()));
            retailProduct.setTakeoutPackFee(skuDTO.getTakeoutPackFee());
            String perTakeoutWeight = null;
            if (Objects.nonNull(skuDTO.getPerTakeoutWeight())) {
                perTakeoutWeight = BigDecimal.valueOf(skuDTO.getPerTakeoutWeight()).setScale(3, RoundingMode.HALF_DOWN).stripTrailingZeros().toPlainString();
            }
            retailProduct.setPerTakeoutWeight(perTakeoutWeight);
            retailProduct.setTakeoutWeightUnit(skuDTO.getTakeoutWeightUnit());
            retailProduct.setSkuId(skuDTO.getSkuId());
            // 仅计件商品返回起售份数
            if (Objects.equals(SaleTypeEnum.NUMBER.getCode(), spuDTO.getSaleType())) {
                retailProduct.setMinSaleNum(skuDTO.getMinSaleNum());
            }

            if (Objects.equals(SaleTypeEnum.WEIGHT.getCode(), spuDTO.getSaleType())) {
                CashierRetailProduct.UnitInfo unitInfo = new CashierRetailProduct.UnitInfo();
                if (skuDTO.getConversionRatio() != null && StringUtils.isNotBlank(skuDTO.getConversionRatioUnit())) {
                    unitInfo.setConversionRatio(new BigDecimal(skuDTO.getConversionRatio()).setScale(3, RoundingMode.HALF_DOWN).doubleValue());
                    unitInfo.setConversionRatioUnit(skuDTO.getConversionRatioUnit());
                    retailProduct.setUnitInfo(unitInfo);
                }
                if (skuDTO.getConversionRatio() == null && StringUtils.isBlank(skuDTO.getConversionRatioUnit())) {
                    Integer conversionRatio = SellWeightUtils.convert(productDTO.getSpu().getUnit(), skuDTO.getConversionRatio(), skuDTO.getConversionRatioUnit());
                    if (conversionRatio != null) {
                        unitInfo.setConversionRatio(new BigDecimal(conversionRatio).divide(new BigDecimal(1000), 3, RoundingMode.HALF_DOWN).doubleValue());
                    }else{
                        unitInfo.setConversionRatio(1D);
                    }
                    unitInfo.setConversionRatioUnit(UnitConstant.DefaultUnitConstant.DEFAULT_WEIGHT_UNIT);
                    retailProduct.setUnitInfo(unitInfo);
                }
            }
        }
        // 生成快照
        retailProduct.setSnapshot(genCashierRetailProductSnapshot(retailProduct));

        return retailProduct;
    }

    public static String genCashierRetailProductSnapshot(CashierRetailProduct retailProduct) {
        Objects.requireNonNull(retailProduct);
        CashierRetailProductSnapshot snapshot = new CashierRetailProductSnapshot();
        snapshot.setSpuId(String.valueOf(retailProduct.getSpuId()));
        snapshot.setTitle(retailProduct.getSpuTitle());
        snapshot.setSalePrice(retailProduct.getSalePrice());
        snapshot.setPurchasePrice(retailProduct.getPurchasePrice());
        snapshot.setCostPrice(retailProduct.getCostPrice());
        snapshot.setSaleUnit(retailProduct.getSaleUnit());
        if (Objects.nonNull(retailProduct.getUnitInfo())) {
            if (retailProduct.getUnitInfo().getConversionRatio() != null) {
                snapshot.setConversionRatio(new BigDecimal(retailProduct.getUnitInfo().getConversionRatio()).multiply(new BigDecimal(1000)).setScale(3, RoundingMode.HALF_DOWN).doubleValue());
                snapshot.setConversionRatioUnit("g");
            }
        }
        return JSON.toJSONString(snapshot);
    }

    public static ProductSaveReq productFilingData2ProductSaveReq(ProductFilingData filingData, Long retailTemplateId, BizActivateStatusDTO storeInfo) {
        ProductSaveReq saveReq = new ProductSaveReq();

        ProductSaveReq.Spu saveSpu = new ProductSaveReq.Spu();
        saveSpu.setOuterId(UUID.randomUUID().toString());
        saveSpu.setTitle(filingData.getSpuTitle());
        saveSpu.setBarcode(filingData.getBarcode());
        saveSpu.setExtendBarcodes(filingData.getExtendBarcodes());
        saveSpu.setProductType(Optional.ofNullable(filingData.getProductType()).orElse(ProductTypeEnum.COMMON.getCode()));
        saveSpu.setSaleType(Optional.ofNullable(filingData.getSaleType()).orElse(SaleTypeEnum.NUMBER.getCode()));
        saveSpu.setUnit(Optional.ofNullable(filingData.getSaleUnit()).orElse(CommonConstant.DEFAULT_UNIT));
        saveSpu.setSaleStatus(SpuSaleStatusEnum.LISTING.getCode());
        saveSpu.setSource(SpuSourceTypeEnum.CASH_REGISTER_CREATE.getCode());
        List<Long> catIds = filingData.getCategories().stream().map(CashierRetailCategory::getCategoryId).collect(Collectors.toList());
        saveSpu.setCategoryId(catIds.get(catIds.size() - 1));
        saveSpu.setStandardGoodsId(filingData.getStandardGoodsId());
        saveSpu.setTemplateCode(TemplateCodeConstant.RETAIL);
        saveSpu.setTemplateId(retailTemplateId);
        saveSpu.setTemplateProp(Collections.emptyMap());
        SaleChannelFlag saleChannelFlag = new SaleChannelFlag(0L);
        if (storeInfo.isRetailCashierActivated()) {
            saleChannelFlag.setInStore(true);
        }
        if (storeInfo.isRetailTakeoutActivated()) {
            saleChannelFlag.setSelfTakeout(true);
        }
        saveSpu.setSaleChannelFlag(saleChannelFlag);
        saveReq.setSpu(saveSpu);

        ProductSaveReq.Sku saveSku = new ProductSaveReq.Sku();
        saveSku.setOuterId(UUID.randomUUID().toString());
        saveSku.setSeq(1);
        saveSku.setKeepPriceEqual(ProductConstant.KeepPriceEqualConstant.EQUAL);
        Map<String, Long> priceMap = new HashMap<>();
        if (saleChannelFlag.isInStore()) {
            priceMap.put(ProductConstant.PriceType.SALE_PRICE.getCode(), filingData.getSalePrice());
        }
        if (saleChannelFlag.isSelfTakeout()) {
            priceMap.put(ProductConstant.PriceType.TAKEOUT_PRICE.getCode(), filingData.getSalePrice());
        }
        Long purchasePrice = filingData.getPurchasePrice();
        // 此处兼容老版本收银机用costPrice上传进价的场景
        if (Objects.isNull(purchasePrice) && Objects.nonNull(filingData.getCostPrice())) {
            purchasePrice = filingData.getCostPrice();
        }
        if (Objects.nonNull(purchasePrice)) {
            priceMap.put(ProductConstant.PriceType.PURCHASE_PRICE.getCode(), purchasePrice);
        }
        saveSku.setPriceInfo(priceMap2DTOList(priceMap));
        saveReq.setSkus(Collections.singletonList(saveSku));

        return saveReq;
    }

    public static ProductSaveReq productDTO2ProductSaveReq(ProductDTO productDTO) {
        Objects.requireNonNull(productDTO);
        ProductSaveReq saveReq = new ProductSaveReq();

        SpuDTO spuDTO = productDTO.getSpu();
        ProductSaveReq.Spu saveSpu = new ProductSaveReq.Spu();
        saveSpu.setSpuId(spuDTO.getSpuId());
        saveSpu.setBizId(spuDTO.getBizId());
        saveSpu.setTitle(spuDTO.getTitle());
        saveSpu.setBarcode(spuDTO.getBarcode());
        saveSpu.setExtendBarcodes(spuDTO.getExtendBarcodes());
        saveSpu.setPluCode(spuDTO.getPluCode());
        saveSpu.setProductType(spuDTO.getProductType());
        saveSpu.setSaleType(spuDTO.getSaleType());
        saveSpu.setUnit(spuDTO.getUnit());
        saveSpu.setCoverImage(spuDTO.getCoverImage());
        saveSpu.setDescription(spuDTO.getDescription());
        saveSpu.setSaleStatus(spuDTO.getSaleStatus());
        saveSpu.setSource(spuDTO.getSource());
        saveSpu.setCategoryId(spuDTO.getCategoryId());
        saveSpu.setStandardGoodsId(spuDTO.getStandardGoodsId());
        saveSpu.setSupplierId(spuDTO.getSupplierId());
        saveSpu.setBrandId(spuDTO.getBrandId());
        saveSpu.setMedias(spuDTO.getMedias());
        saveSpu.setTemplateCode(spuDTO.getTemplateCode());
        saveSpu.setTemplateId(spuDTO.getTemplateId());
        saveSpu.setTemplateProp(spuDTO.getTemplateProps());
        saveSpu.setSaleChannelFlag(spuDTO.getSaleChannelFlag());
        saveReq.setSpu(saveSpu);

        List<ProductSaveReq.Sku> saveSkus = new ArrayList<>();
        productDTO.getSkus().forEach(skuDTO -> {
            ProductSaveReq.Sku saveSku = new ProductSaveReq.Sku();
            saveSku.setOuterId(UUID.randomUUID().toString());
            saveSku.setSkuId(skuDTO.getSkuId());
            saveSku.setTitle(skuDTO.getTitle());
            saveSku.setName(skuDTO.getName());
            saveSku.setSeq(skuDTO.getSeq());
            saveSku.setPriceInfo(skuDTO.getPriceInfo());
            saveSku.setKeepPriceEqual(skuDTO.getKeepPriceEqual());
            saveSku.setTakeoutPackFee(skuDTO.getTakeoutPackFee());
            saveSku.setPerTakeoutWeight(skuDTO.getPerTakeoutWeight());
            saveSku.setTakeoutWeightUnit(skuDTO.getTakeoutWeightUnit());
            saveSku.setMinSaleNum(skuDTO.getMinSaleNum());
            saveSkus.add(saveSku);
        });
        saveReq.setSkus(saveSkus);

        return saveReq;
    }

    public static Integer saleChannelFlag2ServiceType(SaleChannelFlag saleChannelFlag) {
        if (Objects.isNull(saleChannelFlag)) {
            return 0;
        }
        if (saleChannelFlag.isSelfTakeout() && !saleChannelFlag.isInStore()) {
            return 1;
        }
        if (!saleChannelFlag.isSelfTakeout() && saleChannelFlag.isInStore()) {
            return 2;
        }
        return 0;
    }

    public static Map<String, Long> priceDTOList2Map(List<PriceDTO> priceList) {
        if (CollectionUtils.isEmpty(priceList)) {
            return new HashMap<>();
        }
        return priceList.stream()
                .filter(p -> Objects.nonNull(p.getPrice()))
                .collect(Collectors.toMap(PriceDTO::getType, PriceDTO::getPrice, (k1, k2) -> k1));
    }

    public static List<PriceDTO> priceMap2DTOList(Map<String, Long> priceMap) {
        if (MapUtils.isEmpty(priceMap)) {
            return new ArrayList<>();
        }
        return priceMap.entrySet().stream()
                .map(e -> {
                    if (Objects.isNull(e.getValue())) {
                        return null;
                    }
                    PriceDTO priceDTO = new PriceDTO();
                    priceDTO.setType(e.getKey());
                    priceDTO.setPrice(e.getValue());
                    return priceDTO;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static Long collectPriceFromPriceList(List<PriceDTO> priceList, ProductConstant.PriceType priceType) {
        Objects.requireNonNull(priceList);
        return Optional.of(priceList)
                .orElseGet(ArrayList::new)
                .stream()
                .filter(p -> StringUtils.equals(priceType.getCode(), p.getType()))
                .map(PriceDTO::getPrice)
                .findFirst()
                .orElse(null);
    }

    public static Map<String, Object> productSaveReq2TemplatePropMap(ProductSaveReq saveReq) {
        Objects.requireNonNull(saveReq);
        ProductSaveReq.Spu saveSpu = saveReq.getSpu();

        Map<String, Object> propDataMap = new HashMap<>(16);
        propDataMap.put(TemplateDefaultPropConstant.TITLE, saveSpu.getTitle());
        propDataMap.put(TemplateDefaultPropConstant.BARCODE, saveSpu.getBarcode());
        if (CollectionUtils.isNotEmpty(saveSpu.getExtendBarcodes())) {
            propDataMap.put(TemplateDefaultPropConstant.EXTEND_BARCODES, saveSpu.getExtendBarcodes());
        }
        propDataMap.put(TemplateDefaultPropConstant.TYPE, SpuTypeEnum.NUMBER.getCode());
        propDataMap.put(TemplateDefaultPropConstant.SALE_STATUS, true);
        propDataMap.put(TemplateDefaultPropConstant.UNIT, Optional.ofNullable(saveSpu.getUnit()).orElse(CommonConstant.DEFAULT_UNIT));
        propDataMap.put(TemplateDefaultPropConstant.SERVICE_TYPE, RetailProductConverter.saleChannelFlag2ServiceType(saveSpu.getSaleChannelFlag()));

        saveReq.getSkus().forEach(sku -> {
            Map<String, Long> priceMap = priceDTOList2Map(sku.getPriceInfo());
            propDataMap.put(TemplateDefaultPropConstant.PRICE, MapUtils.getLong(priceMap, ProductConstant.PriceType.SALE_PRICE.getCode()));
            propDataMap.put(TemplateDefaultPropConstant.PURCHASE_PRICE, MapUtils.getLong(priceMap, ProductConstant.PriceType.PURCHASE_PRICE.getCode()));
            propDataMap.put(TemplateDefaultPropConstant.TAKEOUT_PRICE, MapUtils.getLong(priceMap, ProductConstant.PriceType.TAKEOUT_PRICE.getCode()));
        });
        return propDataMap;
    }

}
