package com.wosai.smartbiz.retail.service.impl;

import com.wosai.app.dto.MerchantUserInfo;
import com.wosai.app.service.MerchantUserService;
import com.wosai.market.merchant.api.StoreRemoteService;
import com.wosai.market.merchant.dto.store.BizActivateStatusDTO;
import com.wosai.smart.goods.common.constant.PriceChangeConstant;
import com.wosai.smart.goods.common.constant.ProductConstant;
import com.wosai.smart.goods.common.model.BizErrorCode;
import com.wosai.smart.goods.common.model.BizErrorInfo;
import com.wosai.smart.goods.common.model.BizResult;
import com.wosai.smart.goods.common.model.CursorPageResult;
import com.wosai.smart.goods.constant.TemplateCodeConstant;
import com.wosai.smart.goods.constant.TemplateDefaultPropConstant;
import com.wosai.smart.goods.dto.CursorResult;
import com.wosai.smart.goods.dto.terms.TemplateProductQueryTerms;
import com.wosai.smart.goods.enums.OwnerTypeEnum;
import com.wosai.smart.goods.enums.ProductTypeEnum;
import com.wosai.smart.goods.product.IProductRpcService;
import com.wosai.smart.goods.product.IWaterlineRpcService;
import com.wosai.smart.goods.product.dto.ProductDTO;
import com.wosai.smart.goods.product.dto.ProductWaterlineDTO;
import com.wosai.smart.goods.product.req.ProductReqWrapper;
import com.wosai.smart.goods.product.req.ProductSaveReq;
import com.wosai.smart.goods.product.req.ProductWaterlineQueryReq;
import com.wosai.smart.goods.product.res.ProductSaveRes;
import com.wosai.smart.goods.request.template.TemplatePropDataCheckReq;
import com.wosai.smart.goods.response.template.TemplatePropDataCheckRes;
import com.wosai.smart.goods.rpc.TemplateUseRpcService;
import com.wosai.smart.goods.search.IProductSearchRpcService;
import com.wosai.smart.goods.search.req.ProductSearchCursorQueryReq;
import com.wosai.smart.goods.search.req.ProductSearchPageQueryReq;
import com.wosai.smart.goods.search.res.ProductStockRes;
import com.wosai.smart.goods.stock.IStockRpcService;
import com.wosai.smart.goods.stock.dto.StockDTO;
import com.wosai.smartbiz.base.exceptions.BusinessException;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.base.mybatis.page.Page;
import com.wosai.smartbiz.base.res.CursorQueryResult;
import com.wosai.smartbiz.enums.ProductSaveFailTypeEnum;
import com.wosai.smartbiz.gds.dto.CashierRetailCategory;
import com.wosai.smartbiz.gds.dto.CashierRetailProduct;
import com.wosai.smartbiz.gds.dto.CashierRetailProductStock;
import com.wosai.smartbiz.retail.dto.*;
import com.wosai.smartbiz.retail.service.RetailProductService;
import com.wosai.smartbiz.retail.util.RetailProductConverter;
import com.wosai.web.api.ListResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: redhat
 */
@Slf4j
@Service
public class RetailProductServiceImpl implements RetailProductService {

    @Value("${smart.goods.retail.biz-code}")
    private String retailBizCode;

    @Value("${smart.goods.retail.biz-scene}")
    private String retailBizScene;

    @Value("${smart.goods.retail.template-id}")
    private Long retailTemplateId;

    @Autowired
    private IWaterlineRpcService waterlineRpcService;

    @Autowired
    private IProductRpcService productRpcService;

    @Autowired
    private IProductSearchRpcService productSearchRpcService;

    @Autowired
    private IStockRpcService stockRpcService;

    @Autowired
    private TemplateUseRpcService templateUseRpcService;

    @Autowired
    private StoreRemoteService storeRpcService;

    @Autowired
    private MerchantUserService merchantUserService;

    private final static String DEFAULT_ERROR_MSG = "商品查询异常，请稍后重试";

    @Override
    public CursorResult<CashierRetailProduct> syncExist(CashierRetailProductSyncParam param) {
        if (Objects.isNull(param)) {
            throw new ParamException("请求参数不能为空");
        }
        CursorResult<CashierRetailProduct> cursorResult = this.syncAll(param);
        // 这里做二次筛选，仅返回已存在的非普通商品
        List<CashierRetailProduct> existProducts = Optional.ofNullable(cursorResult.getRecords())
                .orElseGet(ArrayList::new)
                .stream()
                .filter(p -> !p.isDeleted() && !Objects.equals(p.getProductType(), ProductTypeEnum.TEMP.getCode()))
                .collect(Collectors.toList());
        cursorResult.setRecords(existProducts);
        return cursorResult;
    }

    @Override
    public CursorResult<CashierRetailProduct> syncDeleted(CashierRetailProductSyncParam param) {
        if (Objects.isNull(param)) {
            throw new ParamException("请求参数不能为空");
        }
        CursorResult<CashierRetailProduct> cursorResult = this.syncAll(param);
        // 这里做二次筛选，仅返回已删除的商品信息
        List<CashierRetailProduct> deletedProducts = Optional.ofNullable(cursorResult.getRecords())
                .orElseGet(ArrayList::new)
                .stream()
                .filter(CashierRetailProduct::isDeleted)
                .collect(Collectors.toList());
        cursorResult.setRecords(deletedProducts);
        return cursorResult;
    }

    @Override
    public CursorResult<CashierRetailProduct> syncAll(CashierRetailProductSyncParam param) {
        if (Objects.isNull(param)) {
            throw new ParamException("请求参数不能为空");
        }
        String storeId = param.getStoreId();

        // 查询水位线信息
        ProductWaterlineQueryReq waterlineQueryReq = new ProductWaterlineQueryReq();
        waterlineQueryReq.setStoreId(storeId);
        waterlineQueryReq.setPageSize(param.getPageSize());
        waterlineQueryReq.setLastId(param.getLastId());
        waterlineQueryReq.setMinWaterline(param.getMinWaterLine());
        waterlineQueryReq.setMaxWaterline(param.getMaxWaterLine());
        BizResult<CursorPageResult<ProductWaterlineDTO>> waterlineResult = waterlineRpcService.queryProductWaterline(waterlineQueryReq);
        if (Objects.isNull(waterlineResult) || !waterlineResult.isSuccess()) {
            throw new IllegalArgumentException("系统异常，请稍后重试");
        }
        CursorPageResult<ProductWaterlineDTO> cursorPageResult = waterlineResult.getResult();

        // 组装返回结果
        CursorResult<CashierRetailProduct> cursorResult = new CursorResult<>();
        cursorResult.setHasMore(cursorPageResult.getHasMore());
        cursorResult.setLastId(cursorPageResult.getCursorId());
        List<ProductWaterlineDTO> waterlineList = cursorPageResult.getRecords();
        if (CollectionUtils.isEmpty(waterlineList)) {
            return cursorResult;
        }

        // 水位线列表不为空，补全商品信息
        List<Long> spuIds = Optional.ofNullable(cursorPageResult.getRecords()).orElseGet(ArrayList::new)
                .stream()
                .map(ProductWaterlineDTO::getSpuId)
                .collect(Collectors.toList());
        Map<Long, CashierRetailProduct> retailProductMap = this.queryProductBySpuIds(storeId, spuIds);
        List<CashierRetailProduct> retailProducts = waterlineList.stream()
                .map(waterline -> {
                    Long spuId = waterline.getSpuId();
                    CashierRetailProduct retailProduct = retailProductMap.get(spuId);
                    if (Objects.isNull(retailProduct)) {
                        retailProduct = new CashierRetailProduct();
                        retailProduct.setSpuId(spuId);
                        retailProduct.setDeleted(true);
                    }
                    return retailProduct;
                }).collect(Collectors.toList());
        cursorResult.setRecords(retailProducts);

        return cursorResult;
    }

    @Override
    public List<CashierRetailProduct> queryTempProducts(String storeId) {
        ProductSearchPageQueryReq queryReq = new ProductSearchPageQueryReq();
        queryReq.setTmplCode(TemplateCodeConstant.RETAIL);
        queryReq.setOwnerType(OwnerTypeEnum.STORE.getCode());
        queryReq.setOwnerId(storeId);
        queryReq.setPage(1);
        queryReq.setPageSize(100);
        TemplateProductQueryTerms queryTerms = new TemplateProductQueryTerms();
        queryTerms.setProductTypes(Collections.singletonList(ProductTypeEnum.TEMP.getCode()));
        queryReq.setQueryTerms(queryTerms);
        BizResult<Page<Long>> bizResult = productSearchRpcService.pageQuerySpuIds(queryReq);
        if (Objects.isNull(bizResult) || Objects.isNull(bizResult.getResult())) {
            return Collections.emptyList();
        }
        List<Long> spuIds = bizResult.getResult().getPageData();
        if (CollectionUtils.isEmpty(spuIds)) {
            return Collections.emptyList();
        }
        Map<Long, CashierRetailProduct> retailProductMap = this.queryProductBySpuIds(storeId, spuIds);
        if (MapUtils.isEmpty(retailProductMap)) {
            return Collections.emptyList();
        }
        return retailProductMap.values().stream().filter(p -> !p.isDeleted()).collect(Collectors.toList());
    }

    @Override
    public Map<Long, CashierRetailProduct> queryExistProductBySpuIds(String storeId, List<Long> spuIds) {
        Map<Long, CashierRetailProduct> retailProductMap = this.queryProductBySpuIds(storeId, spuIds);
        retailProductMap.values().removeIf(CashierRetailProduct::isDeleted);
        return retailProductMap;
    }

    @Override
    public CashierRetailProduct queryByBarcode(String storeId, String barcode) {
        ProductReqWrapper<String> reqWrapper = new ProductReqWrapper<>();
        reqWrapper.setBizCode(retailBizCode);
        reqWrapper.setBizScene(retailBizScene);
        reqWrapper.setTemplateCode(TemplateCodeConstant.RETAIL);
        reqWrapper.setStoreId(storeId);
        reqWrapper.setBizParams(barcode);
        BizResult<ProductDTO> bizResult = productRpcService.queryByBarcode(reqWrapper);
        if (Objects.isNull(bizResult)) {
            throw new BusinessException(DEFAULT_ERROR_MSG);
        }
        ProductDTO product = bizResult.getResult();
        if (Objects.isNull(product)) {
            return null;
        }
        CashierRetailProduct retailProduct = RetailProductConverter.smartGoodsProductDTO2CashierRetailProduct(product);
        return retailProduct.isDeleted() ? null : retailProduct;
    }

    @Override
    public ProductCreateResult create(String storeId, ProductFilingData filingData) {
        ProductCreateResult result = new ProductCreateResult();

        // 查询门店开通的业务状态
        BizActivateStatusDTO storeInfo = storeRpcService.getStoreActivateStatus(storeId);

        // 提取分类、商品信息
        List<Long> catIds = filingData.getCategories().stream().map(CashierRetailCategory::getCategoryId).filter(Objects::nonNull).collect(Collectors.toList());
        ProductSaveReq productSaveReq = RetailProductConverter.productFilingData2ProductSaveReq(filingData, retailTemplateId, storeInfo);

        // 校验创建前输入信息
        ProductSaveResult validateResult = this.validateBeforeProductSave(true, productSaveReq, catIds);
        if (!validateResult.isSuccess()) {
            result.fail(validateResult.getFailType(), validateResult.getFailMsg());
            return result;
        }

        // 执行商品创建
        ProductReqWrapper<ProductSaveReq> createWrapper = new ProductReqWrapper<>();
        createWrapper.setBizCode(retailBizCode);
        createWrapper.setBizScene(retailBizScene);
        createWrapper.setTemplateCode(TemplateCodeConstant.RETAIL);
        createWrapper.setStoreId(storeId);
        createWrapper.setBizParams(productSaveReq);
        try {
            BizResult<ProductSaveRes> createResult = productRpcService.create(createWrapper);
            this.convertSaveResult(createResult, result);
        } catch (Exception ex) {
            log.error("create retail product error, storeId:{}", storeId, ex);
            result.fail(ProductSaveFailTypeEnum.FAIL);
        }

        return result;
    }

    @Override
    public ProductUpdateResult updateSelective(String storeId, CashierRetailProductPartialUpdateParam.Info info, CashierRetailProductPartialUpdateParam.ClientInfo clientInfo,String merchantUserId) {
        Long spuId = info.getSpuId();
        ProductUpdateResult updateResult = new ProductUpdateResult();
        updateResult.setSpuId(spuId);

        // 查询判断商品是否存在或允许编辑
        ProductReqWrapper<Long> queryWrapper = new ProductReqWrapper<>();
        queryWrapper.setBizCode(retailBizCode);
        queryWrapper.setBizScene(retailBizScene);
        queryWrapper.setTemplateCode(TemplateCodeConstant.RETAIL);
        queryWrapper.setStoreId(storeId);
        queryWrapper.setBizParams(spuId);
        BizResult<ProductDTO> queryResult = productRpcService.queryBySpuId(queryWrapper);
        if (Objects.isNull(queryResult) || Objects.isNull(queryResult.getResult())) {
            throw new IllegalArgumentException("商品不存在，请检查入参");
        }
        ProductDTO oldProduct = queryResult.getResult();
        boolean deleted = RetailProductConverter.productNeed2Delete(oldProduct.getSpu());
        if (deleted) {
            throw new IllegalArgumentException("商品不存在，请检查入参");
        }

        ProductSaveReq saveReq = RetailProductConverter.productDTO2ProductSaveReq(oldProduct);
        // 放入更新的字段信息，此处分类可能未更新，将旧分类数据原样放入即可
        this.fillProductSaveReq4Update(saveReq, info);
        List<Long> catIds = Collections.singletonList(saveReq.getSpu().getCategoryId());
        if (Objects.nonNull(info.getCategories())) {
            catIds = info.getCategories().stream().map(CashierRetailCategory::getCategoryId).filter(Objects::nonNull).collect(Collectors.toList());
        }

        // 校验商品字段合法性
        ProductSaveResult validateResult = this.validateBeforeProductSave(false, saveReq, catIds);
        if (!validateResult.isSuccess()) {
            updateResult.fail(validateResult.getFailType(), validateResult.getFailMsg());
            return updateResult;
        }

        // 执行更新
        ProductReqWrapper<ProductSaveReq> updateWrapper = new ProductReqWrapper<>();
        updateWrapper.setBizCode(retailBizCode);
        updateWrapper.setBizScene(retailBizScene);
        updateWrapper.setTemplateCode(TemplateCodeConstant.RETAIL);
        updateWrapper.setStoreId(storeId);
        updateWrapper.setBizParams(saveReq);
        updateWrapper.setBusinessAction(PriceChangeConstant.BusinessAction.UPDATE.getCode());
        updateWrapper.setActionType(PriceChangeConstant.ActionType.MANUAL.getCode());
        updateWrapper.setAppCode(PriceChangeConstant.AppType.Cashier.getCode());
        if (StringUtils.isNotEmpty(merchantUserId)) {
            updateWrapper.setOperatorId(merchantUserId);
            try {
                MerchantUserInfo merchantUserInfo = merchantUserService.getMerchantUserById(merchantUserId);
                if (merchantUserInfo != null) {
                    updateWrapper.setOperatorName(merchantUserInfo.getName());
                    if(merchantUserInfo.getAccountInfo()!=null){
                        updateWrapper.setOperatorAccount(merchantUserInfo.getAccountInfo().getCellphone());
                    }
                }
            }catch (Exception e){
                log.error("query merchantUserInfo error, merchantUserId:{}", merchantUserId, e);
                updateResult.fail(ProductSaveFailTypeEnum.FAIL);
            }
        }

        if (clientInfo != null) {
            Map extInfo = new HashMap<>();
            extInfo.put(PriceChangeConstant.ExtraInfoKey.equipment, clientInfo.getOperateDevice());
            extInfo.put(PriceChangeConstant.ExtraInfoKey.location, clientInfo.getAddress());
            extInfo.put(PriceChangeConstant.ExtraInfoKey.longitude, clientInfo.getLongitude());
            extInfo.put(PriceChangeConstant.ExtraInfoKey.latitude, clientInfo.getLatitude());
            updateWrapper.setExtraInfo(extInfo);
        }

        try {
            BizResult<ProductSaveRes> saveResult = productRpcService.update(updateWrapper);
            this.convertSaveResult(saveResult, updateResult);
        } catch (Exception ex) {
            log.error("update retail product error, storeId:{}", storeId, ex);
            updateResult.fail(ProductSaveFailTypeEnum.FAIL);
        }
        return updateResult;
    }

    @Override
    public CashierRetailProduct queryBySpuId(String storeId, Long spuId) {
        ProductReqWrapper<Long> reqWrapper = new ProductReqWrapper<>();
        reqWrapper.setBizCode(retailBizCode);
        reqWrapper.setBizScene(retailBizScene);
        reqWrapper.setTemplateCode(TemplateCodeConstant.RETAIL);
        reqWrapper.setStoreId(storeId);
        reqWrapper.setBizParams(spuId);
        BizResult<ProductDTO> bizResult = productRpcService.queryBySpuId(reqWrapper);
        if (Objects.isNull(bizResult)) {
            throw new BusinessException(DEFAULT_ERROR_MSG);
        }
        ProductDTO product = bizResult.getResult();
        if (Objects.isNull(product)) {
            throw new BusinessException("商品不存在");
        }
        CashierRetailProduct retailProduct = RetailProductConverter.smartGoodsProductDTO2CashierRetailProduct(product);
        if (retailProduct.isDeleted()) {
            throw new BusinessException("商品不存在");
        }
        return retailProduct;
    }

    private <T extends ProductSaveResult> void convertSaveResult(BizResult<ProductSaveRes> sourceResult, T targetResult) {
        Objects.requireNonNull(targetResult);
        if (Objects.isNull(sourceResult) || Objects.isNull(sourceResult.getResult())) {
            targetResult.fail(ProductSaveFailTypeEnum.FAIL);
            return;
        }
        ProductSaveRes saveRes = sourceResult.getResult();
        boolean saveSuc = Optional.ofNullable(saveRes.getSuccess()).orElse(false);
        if (!saveSuc) {
            targetResult.fail(ProductSaveFailTypeEnum.FAIL);
            List<BizErrorInfo> errors = Optional.ofNullable(saveRes.getFailReasons()).orElseGet(ArrayList::new);
            // 部分特殊的错误码做额外处理，例如同名、同码
            boolean barcodeExist = errors.stream().anyMatch(e -> StringUtils.equals(e.getCode(), BizErrorCode.PRODUCT_BARCODE_EXIST.getErrorCode()));
            if (barcodeExist) {
                targetResult.fail(ProductSaveFailTypeEnum.BARCODE_EXIST);
            }
            boolean titleExist = errors.stream().anyMatch(e -> StringUtils.equals(e.getCode(), BizErrorCode.PRODUCT_TITLE_EXIST.getErrorCode()));
            if (titleExist) {
                targetResult.fail(ProductSaveFailTypeEnum.TITLE_EXIST);
            }
            return;
        }
        targetResult.setSuccess(true);
        targetResult.setSpuId(saveRes.getSpuId());
        targetResult.setSkuId(saveRes.getSingleSkuId());
    }

    private Map<Long, CashierRetailProduct> queryProductBySpuIds(String storeId, List<Long> spuIds) {
        if (CollectionUtils.isEmpty(spuIds)) {
            return Collections.emptyMap();
        }
        ProductReqWrapper<List<Long>> reqWrapper = new ProductReqWrapper<>();
        reqWrapper.setBizCode(retailBizCode);
        reqWrapper.setBizScene(retailBizScene);
        reqWrapper.setTemplateCode(TemplateCodeConstant.RETAIL);
        reqWrapper.setStoreId(storeId);
        reqWrapper.setBizParams(spuIds);
        BizResult<List<ProductDTO>> bizResult = productRpcService.batchQueryBySpuIds(reqWrapper);
        if (Objects.isNull(bizResult) || !bizResult.isSuccess()) {
            throw new BusinessException(DEFAULT_ERROR_MSG);
        }
        List<ProductDTO> products = bizResult.getResult();
        if (CollectionUtils.isEmpty(products)) {
            return Collections.emptyMap();
        }
        return products.stream()
                .map(RetailProductConverter::smartGoodsProductDTO2CashierRetailProduct)
                .collect(Collectors.toMap(CashierRetailProduct::getSpuId, Function.identity(), (k1, k2) -> k1));
    }

    private void fillProductSaveReq4Update(ProductSaveReq saveReq, CashierRetailProductPartialUpdateParam.Info updateInfo) {
        Objects.requireNonNull(saveReq);
        Objects.requireNonNull(updateInfo);
        ProductSaveReq.Spu saveSpu = saveReq.getSpu();

        // 以下所有字段，仅非空信息会被更新
        if (Objects.nonNull(updateInfo.getBarcode())) {
            saveSpu.setBarcode(updateInfo.getBarcode());
        }
        if (Objects.nonNull(updateInfo.getExtendBarcodes())) {
            saveSpu.setExtendBarcodes(updateInfo.getExtendBarcodes());
        }
        if (Objects.nonNull(updateInfo.getSpuTitle())) {
            saveSpu.setTitle(updateInfo.getSpuTitle());
        }
        if (Objects.nonNull(updateInfo.getCategories())) {
            List<Long> categoryIds = updateInfo.getCategories().stream().map(CashierRetailCategory::getCategoryId).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(categoryIds)) {
                saveSpu.setCategoryId(categoryIds.get(categoryIds.size() - 1));
            }
        }
        saveReq.getSkus().forEach(sku -> {
            Map<String, Long> priceMap = RetailProductConverter.priceDTOList2Map(sku.getPriceInfo());
            boolean keepPriceEqual = Objects.equals(ProductConstant.KeepPriceEqualConstant.EQUAL, sku.getKeepPriceEqual());
            if (Objects.nonNull(updateInfo.getSalePrice())) {
                priceMap.put(ProductConstant.PriceType.SALE_PRICE.getCode(), updateInfo.getSalePrice());
                if (keepPriceEqual) {
                    priceMap.put(ProductConstant.PriceType.TAKEOUT_PRICE.getCode(), updateInfo.getSalePrice());
                }
            }
            if (Objects.nonNull(updateInfo.getPurchasePrice())) {
                // 与前端约定，若purchasePrice<0，则表示置空进价
                if (updateInfo.getPurchasePrice() < 0L) {
                    priceMap.remove(ProductConstant.PriceType.PURCHASE_PRICE.getCode());
                } else {
                    priceMap.put(ProductConstant.PriceType.PURCHASE_PRICE.getCode(), updateInfo.getPurchasePrice());
                }
            }
            sku.setPriceInfo(RetailProductConverter.priceMap2DTOList(priceMap));
        });
    }

    private ProductSaveResult validateBeforeProductSave(boolean create, ProductSaveReq saveReq, List<Long> categoryIds) {
        ProductSaveResult validateResult = new ProductCreateResult();
        validateResult.setSuccess(true);

        // 分类路径
        if (CollectionUtils.isEmpty(categoryIds)) {
            validateResult.fail(ProductSaveFailTypeEnum.CATEGORY_NOT_EXIST);
            return validateResult;
        }

        // 转成Map用模板进行校验
        Map<String, Object> templatePropMap = RetailProductConverter.productSaveReq2TemplatePropMap(saveReq);
        templatePropMap.put(TemplateDefaultPropConstant.CATEGORY_ID, categoryIds);
        TemplatePropDataCheckReq propCheckReq = new TemplatePropDataCheckReq();
        propCheckReq.setTemplateCode(TemplateCodeConstant.RETAIL);
        propCheckReq.setOuterId(UUID.randomUUID().toString());
        propCheckReq.setPropDataMap(templatePropMap);
        TemplatePropDataCheckRes propCheckRes = templateUseRpcService.checkPropData(propCheckReq);
        if (Objects.isNull(propCheckRes) || !propCheckRes.isSuccess()) {
            validateResult.fail(ProductSaveFailTypeEnum.FAIL);
            return validateResult;
        }

        // 非创建场景下，增加会员价比较
        if (!create) {
            for (ProductSaveReq.Sku sku : saveReq.getSkus()) {
                Long salePrice = RetailProductConverter.collectPriceFromPriceList(sku.getPriceInfo(), ProductConstant.PriceType.SALE_PRICE);
                Long memberPrice = RetailProductConverter.collectPriceFromPriceList(sku.getPriceInfo(), ProductConstant.PriceType.MEMBER_PRICE);
                if (Objects.nonNull(salePrice) && Objects.nonNull(memberPrice) && salePrice <= memberPrice) {
                    String memberPriceStr = BigDecimal.valueOf(memberPrice).divide(new BigDecimal(100), RoundingMode.HALF_DOWN).stripTrailingZeros().toPlainString();
                    String errMsg = String.format(ProductSaveFailTypeEnum.MEMBER_PRICE_CONTRAST_SALE_PRICE.getDesc(), memberPriceStr);
                    validateResult.fail(ProductSaveFailTypeEnum.MEMBER_PRICE_CONTRAST_SALE_PRICE.getCode(), errMsg);
                    return validateResult;
                }
            }
        }
        return validateResult;
    }

    @Override
    public CursorResult<CashierRetailProductStock> pageQueryProductStock(CashierRetailProductStockQueryParam param) {
        // 查询门店下的商品库存id
        ProductSearchPageQueryReq productSearchPageQueryReq = new ProductSearchPageQueryReq();
        productSearchPageQueryReq.setPage(param.getPage());
        productSearchPageQueryReq.setPageSize(param.getPageSize());
        productSearchPageQueryReq.setOwnerId(param.getStoreId());
        productSearchPageQueryReq.setOwnerType(OwnerTypeEnum.STORE.getCode());
        productSearchPageQueryReq.setTmplCode(TemplateCodeConstant.RETAIL);
        BizResult<Page<ProductStockRes>> bizResult = productSearchRpcService.pageQueryStockIds(productSearchPageQueryReq);
        if (Objects.isNull(bizResult) || !bizResult.isSuccess()) {
            throw new IllegalArgumentException("系统异常，请稍后重试");
        }
        Page<ProductStockRes> result = bizResult.getResult();
        CursorResult<CashierRetailProductStock> cursorResult = new CursorResult<>();
        cursorResult.setHasMore(!(result.getPageData().size() < param.getPageSize()));
        List<ProductStockRes> productStockRes = result.getPageData();
        if (CollectionUtils.isEmpty(productStockRes)) {
            return cursorResult;
        }
        // 查询对应stockId数据
        Map<Long, ProductStockRes> map = new HashMap<>();
        List<Long> stockId = new ArrayList<>();
        productStockRes.forEach(s -> {
            map.put(s.getStockId(), s);
            stockId.add(s.getStockId());
        });
        List<StockDTO> stockDTOS = stockRpcService.listByStockIds(param.getStoreId(), stockId);

        // 封装返回参数
        List<CashierRetailProductStock> cashierRetailProductStocks = new ArrayList<>();
        stockDTOS.forEach(s -> {
            ProductStockRes productStock = map.get(s.getStockId());
            if (productStock != null) {
                CashierRetailProductStock cashierRetailProductStock = new CashierRetailProductStock();
                cashierRetailProductStock.setMtime(s.getMtime());
                cashierRetailProductStock.setQuantity(s.getQuantity());
                cashierRetailProductStock.setSkuId(productStock.getSkuId());
                cashierRetailProductStock.setSpuId(productStock.getSpuId());
                cashierRetailProductStock.setType(s.getType());
                cashierRetailProductStocks.add(cashierRetailProductStock);
            }
        });
        cursorResult.setRecords(cashierRetailProductStocks);
        return cursorResult;
    }

    @Override
    public CursorQueryResult<CashierRetailProductStock> pageQueryProductStockCursor(CashierRetailProductStockCursorQueryParam param) {
        ProductSearchCursorQueryReq req = new ProductSearchCursorQueryReq();
        req.setPageSize(param.getPageSize());
        req.setOwnerId(param.getStoreId());
        req.setOwnerType(OwnerTypeEnum.STORE.getCode());
        req.setTmplCode(TemplateCodeConstant.RETAIL);
        req.setCursor(param.getCursor());
        BizResult<ListResult<ProductStockRes>> bizResult = productSearchRpcService.cursorQueryStockIds(req);
        if (!bizResult.isSuccess()) {
            throw new IllegalArgumentException("查询异常，请稍后重试");
        }
        ListResult<ProductStockRes> result = bizResult.getResult();
        CursorQueryResult<CashierRetailProductStock> cursorResult = new CursorQueryResult<>();
        cursorResult.setHasMore(!(result.getRecords().size() < param.getPageSize()));
        List<ProductStockRes> productStockRes = result.getRecords();
        if (CollectionUtils.isEmpty(productStockRes)) {
            return cursorResult;
        }
        // 查询对应stockId数据
        Map<Long, ProductStockRes> map = new HashMap<>();
        List<Long> stockId = new ArrayList<>();
        productStockRes.forEach(s -> {
            map.put(s.getStockId(), s);
            stockId.add(s.getStockId());
        });
        List<StockDTO> stockDTOS = stockRpcService.listByStockIds(param.getStoreId(), stockId);
        // 封装返回参数
        List<CashierRetailProductStock> cashierRetailProductStocks = new ArrayList<>();
        stockDTOS.forEach(s -> {
            ProductStockRes productStock = map.get(s.getStockId());
            if (productStock != null) {
                CashierRetailProductStock cashierRetailProductStock = new CashierRetailProductStock();
                cashierRetailProductStock.setMtime(s.getMtime());
                cashierRetailProductStock.setQuantity(s.getQuantity());
                cashierRetailProductStock.setSkuId(productStock.getSkuId());
                cashierRetailProductStock.setSpuId(productStock.getSpuId());
                cashierRetailProductStock.setType(s.getType());
                cashierRetailProductStocks.add(cashierRetailProductStock);
            }
        });
        cursorResult.setLastMark(productStockRes.get(productStockRes.size() - 1).getCursor());
        cursorResult.setRecords(cashierRetailProductStocks);
        return cursorResult;
    }

}
