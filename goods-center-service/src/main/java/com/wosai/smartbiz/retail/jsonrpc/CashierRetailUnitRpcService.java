package com.wosai.smartbiz.retail.jsonrpc;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.base.apisix.cashier.CashierBaseParam;
import com.wosai.smartbiz.gds.dto.CashierRetailCategory;
import com.wosai.smartbiz.gds.dto.CashierRetailUnitDTO;
import com.wosai.web.api.ListResult;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/5/15
 */
@JsonRpcService(value = "/rpc/retail/cashier/unit")
@Validated
public interface CashierRetailUnitRpcService {

    /**
     * 单位查询
     * 1.收银机初始化查询
     * 2.单位修改后mqtt消息触发
     */
    List<CashierRetailUnitDTO> queryAllUnit(@Valid CashierApiRequest<CashierBaseParam> request);

}
