package com.wosai.smartbiz.retail.jsonrpc;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.smart.goods.common.model.BizResult;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.retail.dto.ProductTagCreateParam;
import com.wosai.smartbiz.retail.dto.ProductTagQueryParam;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;

@JsonRpcService("rpc/product/tag")
@Validated
public interface RetailProductTagRpcService {
    /**
     * 创建门店常用商品
     * @param request
     * @return
     */
    void createProductTag(@Valid CashierApiRequest<ProductTagCreateParam> request);

    /**
     * 查询门店常用商品
     * @param request
     * @return
     */
    List<Long> findStoreTagProduct(@Valid CashierApiRequest<ProductTagQueryParam> request);
}
