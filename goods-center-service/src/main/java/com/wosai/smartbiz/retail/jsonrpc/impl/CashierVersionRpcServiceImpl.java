package com.wosai.smartbiz.retail.jsonrpc.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.smartbiz.Constants;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.base.apisix.cashier.CashierBaseParam;
import com.wosai.smartbiz.base.apisix.cashier.CashierClient;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.gds.service.VersionService;
import com.wosai.smartbiz.retail.jsonrpc.CashierVersionRpcService;
import com.wosai.smartbiz.retail.util.CashierUtil;
import com.wosai.smartbiz.vo.gds.VersionVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2023/3/9
 */
@Slf4j
@Component
@AutoJsonRpcServiceImpl
public class CashierVersionRpcServiceImpl implements CashierVersionRpcService {
    @Autowired
    private VersionService versionService;

    @Override
    public VersionVO latest(CashierApiRequest<CashierBaseParam> request) {
        CashierBaseParam baseParam = request.getBody();
        if(null == baseParam || StringUtils.isBlank(baseParam.getStoreId())){
            return null;
        }
        String storeId = baseParam.getStoreId();
        CashierClient client = request.getClient();
        if (client == null){
            throw new ParamException("client信息不能为空");
        }
        String currentVersion = CashierUtil.getRequestedAppVersion(client);
        return versionService.latest(Constants.AppMarkConstants.RETAIL, storeId, currentVersion);
    }
}
