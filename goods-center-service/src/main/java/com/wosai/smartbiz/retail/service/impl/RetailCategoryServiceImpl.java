package com.wosai.smartbiz.retail.service.impl;

import com.wosai.smart.goods.common.constant.BusinessCodeEnum;
import com.wosai.smart.goods.common.constant.BusinessSceneEnum;
import com.wosai.smart.goods.constant.CommonConstant;
import com.wosai.smart.goods.constant.TemplateCodeConstant;
import com.wosai.smart.goods.dto.CategoryDTO;
import com.wosai.smart.goods.dto.SpuCountDTO;
import com.wosai.smart.goods.enums.CategoryTypeEnum;
import com.wosai.smart.goods.enums.OwnerTypeEnum;
import com.wosai.smart.goods.product.IProductRpcService;
import com.wosai.smart.goods.product.dto.ProductCountDTO;
import com.wosai.smart.goods.product.req.ProductReqWrapper;
import com.wosai.smart.goods.request.CategoryBaseRequest;
import com.wosai.smart.goods.request.CategoryQueryRequest;
import com.wosai.smart.goods.request.CategoryRequest;
import com.wosai.smart.goods.rpc.CategoryService;
import com.wosai.smart.goods.search.IProductSearchRpcService;
import com.wosai.smartbiz.base.exceptions.BusinessException;
import com.wosai.smartbiz.gds.dto.CashierRetailCategory;
import com.wosai.smartbiz.retail.service.RetailCategoryService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023/8/21
 */
@Service
public class RetailCategoryServiceImpl implements RetailCategoryService {

    @Value("${smart.goods.retail.biz-code}")
    private String retailBizCode;

    @Value("${smart.goods.retail.biz-scene}")
    private String retailBizScene;

    @Value("${smart.goods.retail.template-id}")
    private Long retailTemplateId;

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private IProductSearchRpcService productSearchRpcService;

    @Autowired
    private IProductRpcService productRpcService;

    private static CashierRetailCategory toCashierRetailCategory(CategoryDTO cat) {
        Objects.requireNonNull(cat);
        CashierRetailCategory rc = new CashierRetailCategory();
        rc.setCategoryId(cat.getCategoryId());
        rc.setName(cat.getTitle());
        rc.setSeq(cat.getSeq().longValue());
        rc.setLevel(cat.getLevel());
        if (Objects.nonNull(cat.getParentId()) && !Objects.equals(cat.getParentId(), CommonConstant.DEFAULT_CATEGORY_PARENT_ID)) {
            rc.setParentCategoryId(cat.getParentId());
        }
        return rc;
    }

    @Override
    public List<CashierRetailCategory> queryByStore(String storeId) {
        CategoryBaseRequest categoryBaseRequest = new CategoryBaseRequest();
        categoryBaseRequest.setOwnerType(OwnerTypeEnum.STORE.getCode());
        categoryBaseRequest.setOwnerId(storeId);
        categoryBaseRequest.setBizCode(retailBizCode);
        categoryBaseRequest.setBizScene(retailBizScene);

        List<CategoryDTO> cats = categoryService.listCategoryByOwner(categoryBaseRequest);
        if (CollectionUtils.isEmpty(cats)) {
            return Collections.emptyList();
        }
        return cats.stream()
                .map(RetailCategoryServiceImpl::toCashierRetailCategory)
                .collect(Collectors.toList());
    }

    @Override
    public List<CashierRetailCategory> createByLevel(String storeId, List<CashierRetailCategory> categories) {
        if (CollectionUtils.isEmpty(categories)) {
            return Collections.emptyList();
        }
        Long parentId = CommonConstant.DEFAULT_CATEGORY_PARENT_ID;
        List<Long> queryCatIds = new ArrayList<>(categories.size());
        for (CashierRetailCategory category : categories) {
            Long catId = category.getCategoryId();
            if (Objects.isNull(catId)) {
                category.setParentCategoryId(parentId);
                catId = this.createAndReturn(storeId, category);
            }
            parentId = catId;
            queryCatIds.add(catId);
        }
        List<CategoryDTO> catDtos = categoryService.listCategoryByIds(OwnerTypeEnum.STORE, storeId, queryCatIds);
        if (CollectionUtils.isEmpty(catDtos) || catDtos.size() < categories.size()) {
            throw new BusinessException("分类不存在，请检查");
        }
        List<CashierRetailCategory> cashierRetailCategories = catDtos.stream().map(RetailCategoryServiceImpl::toCashierRetailCategory).collect(Collectors.toList());
        cashierRetailCategories.sort(Comparator.comparingInt(CashierRetailCategory::getLevel));
        return cashierRetailCategories;
    }

    @Override
    public Long createAndReturn(String storeId, CashierRetailCategory category) {
        CategoryRequest request = new CategoryRequest();
        request.setOwnerType(OwnerTypeEnum.STORE.getCode());
        request.setOwnerId(storeId);

        CategoryDTO createDto = new CategoryDTO();
        createDto.setBizCode(retailBizCode);
        createDto.setBizScene(retailBizScene);
        createDto.setTitle(category.getName());
        createDto.setParentId(category.getParentCategoryId());
        createDto.setType(CategoryTypeEnum.NORMAL);
        createDto.setSeq(1);
        request.setCategory(createDto);

        Long catId = categoryService.saveAndReturnId(request);
        if (Objects.isNull(catId)) {
            throw new BusinessException("分类创建失败");
        }
        return catId;
    }

    @Override
    public List<SpuCountDTO> countProduct(String storeId) {
        // 筛选出所有可挂载商品的叶子分类
        CategoryQueryRequest nodeReq = new CategoryQueryRequest();
        nodeReq.setBizCode(BusinessCodeEnum.SMART.getCode());
        nodeReq.setBizScene(BusinessSceneEnum.RETAIL.getCode());
        nodeReq.setOwnerType(OwnerTypeEnum.STORE.getCode());
        nodeReq.setOwnerId(storeId);
        nodeReq.setTemplateId(retailTemplateId);
        List<Long> nodeIds = categoryService.queryPageNodeIds(nodeReq);
        if (CollectionUtils.isEmpty(nodeIds)) {
            return Collections.emptyList();
        }
        // 查询其下挂载的商品数量
        ProductReqWrapper<List<Long>> countReq = new ProductReqWrapper<>();
        countReq.setBizCode(BusinessCodeEnum.SMART.getCode());
        countReq.setBizScene(BusinessSceneEnum.RETAIL.getCode());
        countReq.setStoreId(storeId);
        countReq.setTemplateCode(TemplateCodeConstant.RETAIL);
        countReq.setBizParams(nodeIds);
        List<ProductCountDTO> counts = productRpcService.countByCategoryIds(countReq);
        if (CollectionUtils.isEmpty(counts)) {
            return Collections.emptyList();
        }
        return counts.stream()
                .map(c -> {
                    SpuCountDTO d = new SpuCountDTO();
                    d.setCategoryId(c.getCategoryId());
                    d.setSpuCount(c.getSpuCount());
                    return d;
                }).collect(Collectors.toList());
    }
}
