package com.wosai.smartbiz.retail.service;

import com.wosai.smart.standard.api.category.dto.CategoryFlatDTO;
import com.wosai.smart.standard.api.goods.dto.RetailGoodsDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/9/18
 */
public interface RetailStandardGoodsService {

    /**
     * 指定条码查询标品
     * @param barcode
     * @return
     */
    RetailGoodsDTO queryByBarcode(String barcode);

    /**
     * 查询指定标准分类
     * @param categoryIds
     * @return
     */
    List<CategoryFlatDTO> listCategoryByIds(List<Long> categoryIds);

}
