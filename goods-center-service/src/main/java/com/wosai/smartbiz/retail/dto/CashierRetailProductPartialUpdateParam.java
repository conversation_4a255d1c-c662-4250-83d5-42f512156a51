package com.wosai.smartbiz.retail.dto;

import com.wosai.smartbiz.gds.dto.CashierRetailCategory;
import com.wosai.smartbiz.gds.dto.CashierRetailProduct;
import lombok.Data;

import javax.annotation.Nullable;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/4/29
 */
@Data
public class CashierRetailProductPartialUpdateParam implements Serializable {

    @NotBlank(message = "门店ID不能为空")
    private String storeId;

    @NotNull(message = "商品信息不能为空")
    private Info archivalProduct;

    private ClientInfo clientInfo;

    @Data
    public static class Info implements Serializable {

        /**
         * 商品ID
         */
        @NotNull(message = "商品ID不能为空")
        private Long spuId;

        /**
         * 条码
         * 传空字符""表示置空
         * 传null表示不更新
         */
        @Nullable
        private String barcode;

        /**
         * 扩展条码
         * 传[]表示置空
         * 传null表示不更新
         */
        @Nullable
        private List<String> extendBarcodes;

        /**
         * 商品标题
         * 传null表示不更新
         */
        @Nullable
        private String spuTitle;

        /**
         * 售卖价
         * 传null表示不更新
         */
        @Max(value = 9999999, message = "售卖价不能超过99999.99元")
        @Nullable
        private Long salePrice;

        /**
         * 进价
         * 传负数表示置空
         * 传null表示不更新
         */
        @Max(value = 9999999, message = "进价不能超过99999.99元")
        @Nullable
        private Long purchasePrice;

        /**
         * 分类路径
         * 传null表示不更新
         */
        @Nullable
        private List<CashierRetailCategory> categories;

        /**
         * 库存信息
         */
        private CashierRetailProduct.Stock stock;

    }

    @Data
    public static class Stock {

        /**
         * 管理库存
         */
        private Boolean manageStock;

        /**
         * 库存值
         */
        private Double stockValue;

    }

    @Data
    public static class ClientInfo implements Serializable {

        /**
         * 经度
         */
        private String longitude;

        /**
         * 纬度
         */
        private String latitude;

        /**
         * 地址
         */
        private String address;

        /**
         * 操作设备
         */
        private String operateDevice;


    }


}
