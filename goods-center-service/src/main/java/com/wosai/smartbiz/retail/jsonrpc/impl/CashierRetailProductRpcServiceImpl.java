package com.wosai.smartbiz.retail.jsonrpc.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.market.merchant.api.StoreRemoteService;
import com.wosai.smart.goods.common.constant.SaleChannelFlag;
import com.wosai.smart.goods.common.constant.StockConstant;
import com.wosai.smart.goods.common.model.BizResult;
import com.wosai.smart.goods.constant.CommonConstant;
import com.wosai.smart.goods.dto.CursorResult;
import com.wosai.smart.goods.dto.SpuCountDTO;
import com.wosai.smart.goods.enums.ProductTypeEnum;
import com.wosai.smart.goods.enums.SaleTypeEnum;
import com.wosai.smart.goods.product.IWaterlineRpcService;
import com.wosai.smart.goods.product.dto.StoreWaterlineDTO;
import com.wosai.smart.standard.api.category.dto.CategoryFlatDTO;
import com.wosai.smart.standard.api.goods.dto.RetailGoodsDTO;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.base.apisix.cashier.CashierBaseParam;
import com.wosai.smartbiz.base.apisix.cashier.CashierUser;
import com.wosai.smartbiz.base.exceptions.BusinessException;
import com.wosai.smartbiz.base.utils.CashierUtil;
import com.wosai.smartbiz.dto.CashierRetailProductFilingDTO;
import com.wosai.smartbiz.enums.ProductSaveFailTypeEnum;
import com.wosai.smartbiz.gds.dto.CashierProductWaterLine;
import com.wosai.smartbiz.gds.dto.CashierRetailCategory;
import com.wosai.smartbiz.gds.dto.CashierRetailProduct;
import com.wosai.smartbiz.gds.dto.CashierRetailProductStock;
import com.wosai.smartbiz.retail.adapter.CashierRetailProductSaleChannelAdapter;
import com.wosai.smartbiz.retail.adapter.CashierRetailProductUnitAdapter;
import com.wosai.smartbiz.retail.constant.CategoryConstant;
import com.wosai.smartbiz.retail.constant.ProductOperateEnum;
import com.wosai.smartbiz.retail.dto.*;
import com.wosai.smartbiz.retail.jsonrpc.CashierRetailProductRpcService;
import com.wosai.smartbiz.retail.service.RetailCategoryService;
import com.wosai.smartbiz.retail.service.RetailProductService;
import com.wosai.smartbiz.retail.service.RetailStandardGoodsService;
import com.wosai.smartbiz.retail.service.RetailStockService;
import com.wosai.web.api.ListResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.Valid;
import java.util.*;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @create 2023/5/15
 */
@Slf4j
@Service
@AutoJsonRpcServiceImpl
public class CashierRetailProductRpcServiceImpl implements CashierRetailProductRpcService {

    @Autowired
    private RetailProductService retailProductService;

    @Autowired
    private IWaterlineRpcService waterlineRpcService;

    @Autowired
    private RetailCategoryService retailCategoryService;

    @Autowired
    private RetailStandardGoodsService retailStandardGoodsService;

    @Autowired
    private CashierRetailProductSaleChannelAdapter productSaleChannelAdapter;

    @Autowired
    private CashierRetailProductUnitAdapter unitAdapter;

    @Autowired
    private StoreRemoteService storeRpcService;

    @Autowired
    private RetailStockService retailStockService;

    private static final Double MAX_VALUE = 99999.999D;

    @Override
    public CursorResult<CashierRetailProduct> sync(CashierApiRequest<CashierRetailProductSyncParam> request) {
        CursorResult<CashierRetailProduct> cursorResult = retailProductService.syncExist(request.getBody());
        // 判断当前版本是否需要使用适配器
        String unitAppVersion = CashierUtil.getRequestedAppVersion(request.getClient());
        if (unitAdapter.isLessThanDestAppVersion(unitAppVersion)) {
            cursorResult.getRecords().forEach(r -> unitAdapter.process(r));
        }
        return cursorResult;
    }

    @Override
    public CursorResult<CashierRetailProduct> syncDeleted(CashierApiRequest<CashierRetailProductSyncParam> request) {
        return retailProductService.syncDeleted(request.getBody());
    }

    @Override
    public ListResult<CashierRetailProduct> syncTemp(CashierApiRequest<CashierBaseParam> request) {
        List<CashierRetailProduct> products = retailProductService.queryTempProducts(request.getBody().getStoreId());
        return new ListResult<>(products);
    }

    @Override
    public CursorResult<CashierRetailProduct> syncAll(CashierApiRequest<CashierRetailProductSyncParam> request) {
        CursorResult<CashierRetailProduct> cursorResult = retailProductService.syncAll(request.getBody());
        if (CollectionUtils.isEmpty(cursorResult.getRecords())) {
            return cursorResult;
        }
        // 判断当前版本是否需要使用适配器
        String appVersion = CashierUtil.getRequestedAppVersion(request.getClient());
        if (productSaleChannelAdapter.isLessThanDestAppVersion(appVersion)) {
            cursorResult.getRecords().forEach(r -> productSaleChannelAdapter.process(r));
        }

        // 判断当前版本是否需要使用适配器
        if (unitAdapter.isLessThanDestAppVersion(appVersion)) {
            cursorResult.getRecords().forEach(r -> unitAdapter.process(r));
        }
        return cursorResult;
    }

    @Override
    public CashierProductWaterLine latestWaterLine(CashierApiRequest<CashierBaseParam> request) {
        String storeId = request.getBody().getStoreId();
        BizResult<StoreWaterlineDTO> bizResult = waterlineRpcService.queryStoreWaterline(storeId);
        if (Objects.isNull(bizResult) || !bizResult.isSuccess()) {
            throw new BusinessException("水位线查询异常");
        }
        StoreWaterlineDTO storeWaterlineDTO = bizResult.getResult();
        CashierProductWaterLine wl = new CashierProductWaterLine();
        wl.setWaterLine(storeWaterlineDTO.getWaterline());
        return wl;
    }

    @Override
    public ProductCheckCreateResult checkCreateByBarcode(CashierApiRequest<CashierRetailProductCheckCreateByBarcodeParam> request) {
        String storeId = request.getBody().getStoreId();
        String barcode = request.getBody().getBarcode();

        CashierRetailProductFilingDTO filingDto = new CashierRetailProductFilingDTO();
        ProductCheckCreateResult result = new ProductCheckCreateResult();

        // 1、检查门店下是否已存在同码商品
        CashierRetailProduct storeGoods = retailProductService.queryByBarcode(storeId, barcode);
        if (Objects.nonNull(storeGoods)) {
            // 命中的商品须支持到店售卖
            SaleChannelFlag channelFlag = new SaleChannelFlag(storeGoods.getSaleChannels());
            if (channelFlag.isInStore()) {
                filingDto.hitAndSetStoreGoods(storeGoods);
            } else {
                result.setAllowCreate(false);
                result.setBlockCreateMsg("商品仅在自营外卖渠道售卖");
            }
        }

        // 2、若门店下未命中，尝试匹配标品库
        if (!filingDto.hitStoreGoods()) {
            RetailGoodsDTO standardGoods = retailStandardGoodsService.queryByBarcode(barcode);
            if (Objects.nonNull(standardGoods)) {
                List<CategoryFlatDTO> standardCats = retailStandardGoodsService.listCategoryByIds(standardGoods.getShopCategoryIds());
                filingDto.hitAndSetStandardGoods(standardGoods, standardCats);
            }
        }

        // 3、转化成表单数据
        Supplier<ProductFilingData> formBuilder = () -> {
            ProductFilingData tmp = new ProductFilingData();
            tmp.setBarcode(barcode);
            tmp.setSaleUnit(CommonConstant.DEFAULT_UNIT);
            tmp.setProductType(ProductTypeEnum.COMMON.getCode());
            tmp.setSaleType(SaleTypeEnum.NUMBER.getCode());
            return tmp;
        };
        Supplier<List<CashierRetailCategory>> queryStoreCategory = () -> retailCategoryService.queryByStore(storeId);
        // 查询门店下面各分类的数量
        List<SpuCountDTO> spuCountDTOS = retailCategoryService.countProduct(storeId);
        ProductFilingData filingData = filingDto.convertToFilingData(formBuilder, queryStoreCategory, spuCountDTOS);

        // 4、返回结果
        result.setProductExist(filingDto.hitStoreGoods());
        result.setMatchSmartStandard(filingDto.getHitSmartStandard());
        result.setMatchNationalStandard(filingDto.getHitNationalStandard());
        if (filingDto.hitStoreGoods()) {
            result.setProduct(filingData);
        } else {
            result.setArchivalProduct(filingData);
        }
        return result;
    }

    @Override
    public ProductCreateResult create(CashierApiRequest<CashierRetailProductCreateParam> request) {

        String storeId = request.getBody().getStoreId();
        ProductFilingData filingData = request.getBody().getArchivalProduct();

        ProductCreateResult result = new ProductCreateResult();

        // 提取分类信息
        List<CashierRetailCategory> categories = Optional.ofNullable(filingData.getCategories()).orElse(null);
        if (CollectionUtils.isEmpty(categories)) {
            result.fail(ProductSaveFailTypeEnum.CATEGORY_NOT_EXIST);
            return result;
        }

        // 基础校验
        CashierRetailProduct.Stock stock = filingData.getStock();
        if (stock != null) {
            if (stock.getManageStock() == null) {
                result.fail(ProductSaveFailTypeEnum.STOCK_TYPE_NULL);
                return result;
            }
            if (stock.getStockValue() != null) {
                if (stock.getStockValue() < 0) {
                    result.fail(ProductSaveFailTypeEnum.STOCK_VALUE_LESS_THAN_ZERO);
                    return result;
                }
                if (stock.getStockValue() > MAX_VALUE) {
                    result.fail(ProductSaveFailTypeEnum.STOCK_VALUE_GREATER_THAN_MAX);
                    return result;
                }
            }
        }

        // 判断分类是否需要创建
        List<CashierRetailCategory> categoriesToUse = retailCategoryService.createByLevel(storeId, categories);
        filingData.setCategories(categoriesToUse);

        result = retailProductService.create(storeId, filingData);

        // 创建成功，补充商品信息
        boolean createSuc = Optional.ofNullable(result.getCreateSuc()).orElse(false);
        CashierRetailProduct productToUse = null;
        if (createSuc) {
            productToUse = retailProductService.queryBySpuId(storeId, result.getSpuId());
        }
        if (Objects.nonNull(productToUse)) {
            ProductFilingData filingDataToUse = new ProductFilingData();
            BeanUtils.copyProperties(productToUse, filingDataToUse);
            filingDataToUse.setCategories(categoriesToUse);
            result.setSpuId(productToUse.getSpuId());
            result.setProduct(filingDataToUse);
        }

        // 业务迭代：增加库存处理
        if (createSuc && stock != null && stock.getManageStock() != null && result != null) {
            StockBatchUpdateParam stockBatchUpdateParam = new StockBatchUpdateParam();
            stockBatchUpdateParam.setStoreId(storeId);
            String merchantUserId = Optional.ofNullable(request.getUser()).map(CashierUser::getMerchantUserId).orElse(null);
            stockBatchUpdateParam.setMerchantUserId(merchantUserId);
            StockBatchUpdateParam.StockItem stockItem = new StockBatchUpdateParam.StockItem();
            stockItem.setSpuId(result.getSpuId());
            stockItem.setSkuId(result.fetchSkuId());
            stockItem.setStock(stock);
            stockBatchUpdateParam.addStockItem(stockItem);
            stockBatchUpdateParam.setOperateType(ProductOperateEnum.ADD.getCode());
            retailStockService.batchUpdateStock(stockBatchUpdateParam);
        }

        return result;
    }

    @Override
    public CursorResult<CashierRetailProductStock> pageQueryProductStock(@Valid CashierApiRequest<CashierRetailProductStockQueryParam> request) {
        CashierRetailProductStockQueryParam cashierRetailProductStockQueryParam = request.getBody();
        return retailProductService.pageQueryProductStock(cashierRetailProductStockQueryParam);
    }


    @Override
    public ProductUpdateResult update(CashierApiRequest<CashierRetailProductUpdateParam> request) {
        CashierRetailProductUpdateParam updateParam = request.getBody();
        CashierRetailProductUpdateParam.Info coverUpdateProduct = updateParam.getArchivalProduct();
        String merchantUserId = Optional.ofNullable(request.getUser()).map(CashierUser::getMerchantUserId).orElse(null);

        // 转化成局部更新的入参形式，该接口仅做兼容，后续收银机升级后废弃
        CashierRetailProductPartialUpdateParam.Info partialUpdateProduct = new CashierRetailProductPartialUpdateParam.Info();
        partialUpdateProduct.setSpuId(coverUpdateProduct.getSpuId());
        partialUpdateProduct.setBarcode(Optional.ofNullable(coverUpdateProduct.getBarcode()).orElse(""));
        partialUpdateProduct.setExtendBarcodes(Optional.ofNullable(coverUpdateProduct.getExtendBarcodes()).orElseGet(ArrayList::new));
        partialUpdateProduct.setSpuTitle(coverUpdateProduct.getSpuTitle());
        partialUpdateProduct.setSalePrice(coverUpdateProduct.getSalePrice());
        partialUpdateProduct.setPurchasePrice(Optional.ofNullable(coverUpdateProduct.getPurchasePrice()).orElse(-1L));
        partialUpdateProduct.setCategories(coverUpdateProduct.getCategories());

        return retailProductService.updateSelective(updateParam.getStoreId(), partialUpdateProduct,null,merchantUserId);
    }

    @Override
    public ProductUpdateResult partialUpdate(CashierApiRequest<CashierRetailProductPartialUpdateParam> request) {
        ProductUpdateResult productUpdateResult = null;
        CashierRetailProductPartialUpdateParam updateParam = request.getBody();
        // 业务迭代：增加库存处理
        CashierRetailProduct.Stock stock = Optional.ofNullable(updateParam.getArchivalProduct()).map(CashierRetailProductPartialUpdateParam.Info::getStock).orElse(null);
        String merchantUserId = Optional.ofNullable(request.getUser()).map(CashierUser::getMerchantUserId).orElse(null);
        if (stock != null && stock.getManageStock() != null) {
            StockBatchUpdateParam stockBatchUpdateParam = new StockBatchUpdateParam();
            stockBatchUpdateParam.setStoreId(updateParam.getStoreId());
            stockBatchUpdateParam.setMerchantUserId(merchantUserId);
            StockBatchUpdateParam.StockItem stockItem = new StockBatchUpdateParam.StockItem();
            stockItem.setSpuId(updateParam.getArchivalProduct().getSpuId());
            stockItem.setStock(stock);
            stockBatchUpdateParam.addStockItem(stockItem);
            stockBatchUpdateParam.setOperateType(ProductOperateEnum.UPDATE.getCode());
            retailStockService.batchUpdateStock(stockBatchUpdateParam);
        }
        productUpdateResult = retailProductService.updateSelective(updateParam.getStoreId(), updateParam.getArchivalProduct(), updateParam.getClientInfo(),merchantUserId);
        return productUpdateResult;
    }

    private static void compileFilingDataOfCategory(ProductFilingData filingData, List<CashierRetailCategory> allCategory) {
        if (CollectionUtils.isEmpty(allCategory)) {
            return;
        }
        CashierRetailProduct.Category category = filingData.getCategory();
        List<CashierRetailCategory> categoryNodes = new ArrayList<>(2);
        filingData.setCategories(categoryNodes);

        // 有归属分类
        CashierRetailCategory self;
        if (Objects.nonNull(category)) {
            self = allCategory.stream().filter(c -> Objects.equals(category.getId(), c.getCategoryId())).findFirst().orElse(null);
            if (Objects.isNull(self)) {
                return;
            }
        } else {
            self = allCategory.stream().filter(c -> StringUtils.equals(c.getName(), CategoryConstant.DEFAULT_TITLE)).findFirst().orElse(null);
            if (Objects.isNull(self)) {
                self = new CashierRetailCategory();
                self.setName(CategoryConstant.DEFAULT_TITLE);
                categoryNodes.add(self);
                return;
            }
        }
        if (Objects.isNull(self.getParentCategoryId()) || Objects.equals(self.getParentCategoryId(), CommonConstant.DEFAULT_CATEGORY_PARENT_ID)) {
            categoryNodes.add(self);
            return;
        }
        CashierRetailCategory finalSelf = self;
        CashierRetailCategory parent = allCategory.stream().filter(c -> Objects.equals(finalSelf.getParentCategoryId(), c.getCategoryId())).findFirst().orElse(null);
        if (Objects.isNull(parent)) {
            return;
        }
        categoryNodes.add(parent);
        categoryNodes.add(self);
    }
}
