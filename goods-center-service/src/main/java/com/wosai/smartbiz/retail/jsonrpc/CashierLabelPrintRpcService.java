package com.wosai.smartbiz.retail.jsonrpc;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.base.apisix.cashier.CashierBaseParam;
import com.wosai.smartbiz.oms.api.pojo.CursorPage;
import com.wosai.smartbiz.retail.dto.CashierLabelPrintParam;
import com.wosai.smartbiz.retail.dto.LabelPrintWaterLineDTO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;

/**
 * 标签打印
 * <AUTHOR>
 * @create 2023/3/9
 */
@JsonRpcService(value = "rpc/retail/cashier/label/print")
@Validated
public interface CashierLabelPrintRpcService {

    /**
     * 商品标签打印修改
     * @param request
     * @return
     */
    boolean update(@Valid CashierApiRequest<CashierLabelPrintParam> request);

    /**
     * 获取标签打印最新水位值
     * @param request
     * @return
     */
    Long waterLine(CashierApiRequest<CashierBaseParam> request);

    /**
     * 同步标签打印数据
     * @param request
     * @return
     */
    CursorPage<LabelPrintWaterLineDTO> sync(CashierApiRequest<CashierLabelPrintParam> request);

    /**
     * 同步删除的标签打印数据
     * @param request
     * @return
     */
    CursorPage<LabelPrintWaterLineDTO> syncDeleted(CashierApiRequest<CashierLabelPrintParam> request);
}
