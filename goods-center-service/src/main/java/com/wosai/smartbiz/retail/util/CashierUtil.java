package com.wosai.smartbiz.retail.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.wosai.smartbiz.base.apisix.cashier.CashierClient;
import com.wosai.smartbiz.base.enums.SceneEnum;
import com.wosai.smartbiz.base.exceptions.BusinessException;
import com.wosai.smartbiz.base.utils.HmacSHA256SignUtil;
import com.wosai.smartbiz.base.utils.MD5Util;
import com.wosai.smartbiz.base.utils.WebUtil;
import com.wosai.smartbiz.uc.constants.Constants;
import com.wosai.smartbiz.user.user.enums.AppTypeEnum;
import com.wosai.smartbiz.user.user.enums.OsEnum;
import com.wosai.smartbiz.user.user.login.DeviceParam;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;

import static net.logstash.logback.argument.StructuredArguments.keyValue;

/**
 * @author: redhat
 */
public class CashierUtil {

    /**
     * 获取访问信息
     * @param client
     * @return
     */
    public static DeviceParam getRequestDeviceParam(CashierClient client){
        String userAgentStr = client.getUserAgent();
        if (StringUtils.isBlank(userAgentStr) || !userAgentStr.startsWith("sqb")){
            return DeviceParam.unknownDevice();
        }
        //sqb_cashier_dinner android/2.12.0;brand/Android;model/MuMu;Android/6.0.1;
        DeviceParam deviceParam = new DeviceParam();
        //这是我们自定义的user-agent
        String [] agentsArray = userAgentStr.split(";");
        if(null != agentsArray){
            if (agentsArray.length > 0){
                String [] appTypeArray = agentsArray[0].split(" ");
                if (appTypeArray[0].contains(Constants.UserAgentConstants.CASHIER_FLAG)){
                    deviceParam.setAppType(AppTypeEnum.CASHIER);
                }
            }
            if (agentsArray.length > 1){
                String [] deviceTypeArray = agentsArray[0].split(" ");
                deviceParam.setDeviceType(deviceTypeArray[0]);
            }
            if (agentsArray.length > 2){
                String [] osTypeArray = agentsArray[0].split(" ");

                OsEnum osType = EnumUtils.getEnum(OsEnum.class,osTypeArray[0]);
                if (osType == null){
                    osType = OsEnum.UNKNOWN;
                }
                deviceParam.setOs(osType);
            }


        }

        deviceParam.setUserAgent(userAgentStr);
        deviceParam.setSunmiDevice(isSunmiDevice(client));
        deviceParam.setDeviceScene(getCashierScene(userAgentStr));

        deviceParam.setAppVersion(getRequestedAppVersion(client));
        deviceParam.setDeviceId(client.getTerminalId());
        deviceParam.setDeviceFp(client.getTerminalSn());
        deviceParam.setDeviceMqttFp(client.getTerminalMqttSn());
        deviceParam.setLatitude(client.getLatitude());
        deviceParam.setLongitude(client.getLongitude());
        deviceParam.setIp((client.getIp()));
        deviceParam.setTerminalModel(client.getModel());
        deviceParam.setTerminalBrand(getRequestTerminalBrand(client));
        //deviceParam.setTerminalScreen(getRequestTerminalScreenSize(client));

        return deviceParam;
    }

    /**
     * 根据UA获取场景
     * @param userAgent
     * @return
     */
    public static SceneEnum getCashierScene(String userAgent){
        SceneEnum deviceScene = SceneEnum.RESTAURANT;
        if(StringUtils.containsIgnoreCase(userAgent, Constants.UserAgentConstants.RETAIL_CASHIER_FLAG)){
            deviceScene = SceneEnum.RETAIL;
        }
        return deviceScene;
    }

    /**
     * 判断设备是否是商米设备
     * @param client
     * @return
     */
    public static boolean isSunmiDevice(CashierClient client){
        String userAgentStr = client.getUserAgent();
        if (StringUtils.isBlank(userAgentStr) || !userAgentStr.startsWith("sqb")){
            return false;
        }
        String [] agentsArray = userAgentStr.split(";");
        if(null != agentsArray && agentsArray.length > 0){
            for(String str : Arrays.asList(agentsArray)) {
                boolean sunmiDevice = StringUtils.containsIgnoreCase(str, Constants.UserAgentConstants.SUNMI_DEVICE_FLAG);
                if(sunmiDevice){
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 获取设备屏幕尺寸数据
     * @param client
     * @return
     */
    public static BigDecimal getRequestTerminalScreenSize(CashierClient client){
        try {
            String userAgentStr = client.getUserAgent();
            if (StringUtils.isBlank(userAgentStr) || !userAgentStr.startsWith("sqb")){
                return null;
            }
            String [] agentsArray = userAgentStr.split(";");
            if(null != agentsArray && agentsArray.length > 0){
                for(String str : Arrays.asList(agentsArray)) {
                    boolean deviceScreenExists = StringUtils.containsIgnoreCase(str, Constants.UserAgentConstants.DEVICE_SCREEN_FLAG);
                    if(deviceScreenExists){
                        BigDecimal screenSize = null;
                        String screenString = str.replaceFirst(Constants.UserAgentConstants.DEVICE_SCREEN_FLAG, "");
                        if(StringUtils.isNotBlank(screenString)){
                            screenSize = new BigDecimal(screenString);
                        }
                        return screenSize;
                    }
                }
            }
            return null;
        }catch (Exception ex) {
            return null;
        }
    }


    /**
     * 获取收银机版本号
     * @param client
     * @return
     */
    public static String getRequestedAppVersion(CashierClient client) {
        String appVersion = "1.0.0";
        String userAgentStr = client.getUserAgent();
        if (StringUtils.isBlank(userAgentStr)){
            return appVersion;
        }
        //sqb_cashier_dinner android/2.12.0;brand/Android;model/MuMu;Android/6.0.1;
        if (userAgentStr.startsWith("sqb")){
            String [] agentsArray = userAgentStr.split(";");
            if (null != agentsArray && agentsArray.length > 0) {
                String[] appInfoArray = agentsArray[0].split(" ");
                if(appInfoArray.length > 1){
                    String[] versionArray = appInfoArray[1].split("/");
                    if(versionArray.length > 1){
                        appVersion = versionArray[1];
                    }
                }
            }
        }
        return appVersion;
    }

    /**
     * 获取收银机品牌
     * @param client
     * @return
     */
    public static String getRequestTerminalBrand(CashierClient client) {
        String terminalBrand = null;
        String userAgentStr = client.getUserAgent();
        if (StringUtils.isBlank(userAgentStr)){
            return terminalBrand;
        }
        //sqb_cashier_dinner android/2.12.0;brand/Android;model/MuMu;Android/6.0.1;
        if (userAgentStr.startsWith("sqb")){
            String [] agentsArray = userAgentStr.split(";");
            if (null != agentsArray && agentsArray.length >= 1 && StringUtils.isNotBlank(agentsArray[1])) {
                terminalBrand = agentsArray[1].replaceFirst("brand/", "");
            }
        }
        return terminalBrand;
    }

    public static void main(String[] args) {
        /*String ua = "sqb_cashier_retail android/1.9.9;brand/Xiaomi;model/2106118C;Android/13;";
        CashierClient client = new CashierClient();
        client.setUserAgent(ua);
        DeviceParam deviceParam =  getRequestDeviceParam(client);
        System.out.println(JSON.toJSONString(deviceParam));

        String deviceFp = new StringBuilder(Constants.CASHIER_TERMINAL_FP_PREFIX).append("85e7ed84-e75f-40d9-83d9-4af2049e63b6").toString();
        String abc = new StringBuilder(Constants.CASHIER_PREFIX).append(MD5Util.encryptWithMD5(deviceFp)).toString();
        System.out.println(abc);*/

        //checkSunmiDeviceSqbSbs("DA8823CV40926");
        checkYiminDeviceSqbSbs("ND3501XEA332C0575");
    }


    private static void checkSunmiDeviceSqbSbs(String deviceSn) {
        try {
            String appId = "34f4535cc1a548f06";
            String appSecretKey = "fc63121447191b9798cc2be311690594b79be765";
            String apiMidplatUrl = "https://api-midplat.sunmi.com";
            String sqbSbsSnCheckPath = "/pub-esb/sqb/sbs/sn/check";

            Long requestTimestamp = System.currentTimeMillis();
            String requestNonce = UUID.randomUUID().toString();

            String requestUrl = new StringBuilder(apiMidplatUrl).append(sqbSbsSnCheckPath).toString();
            Map<String, String> body = new HashMap<>();
            body.put("sn", deviceSn);

            String bodyJson = JSON.toJSONString(body);
            String str = String.format("%s%s%s%s", bodyJson, appId, requestTimestamp, requestNonce);
            String sign = HmacSHA256SignUtil.sign(str, appSecretKey);

            Map<String, String> extHeaderMap = new HashMap<>();
            extHeaderMap.put("Sunmi-Appcode", appId);
            extHeaderMap.put("Sunmi-Timestamp", String.valueOf(requestTimestamp));
            extHeaderMap.put("Sunmi-Nonce", requestNonce);
            extHeaderMap.put("Sunmi-Sign", sign);

            String response = WebUtil.doPostJson(requestUrl, body, 3000, 10000, extHeaderMap);

            System.out.println("response:" + response);
            System.out.println("header:" + JSON.toJSONString(extHeaderMap));


        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static void checkYiminDeviceSqbSbs(String deviceSn) {
        try {
            String apiUrl = "https://api.neostra.com";
            String queryDevicePath = "/device/openapi/v1/check";
            String kitAccount = "CRJD260073";
            String requestUrl = new StringBuilder(apiUrl).append(queryDevicePath).toString();
            Map<String, String> body = new HashMap<>();
            body.put("sn", deviceSn);
            body.put("account", kitAccount);

            String response = WebUtil.doPostJson(requestUrl, body, 3000, 10000, null);

            System.out.println("response:" + response);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}
