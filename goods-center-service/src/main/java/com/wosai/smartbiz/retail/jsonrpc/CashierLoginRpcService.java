package com.wosai.smartbiz.retail.jsonrpc;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.base.apisix.cashier.CashierBaseParam;
import com.wosai.smartbiz.retail.dto.RefreshTokenParam;
import com.wosai.smartbiz.user.user.login.LoginParam;
import com.wosai.smartbiz.user.user.vo.AccessTokenVO;
import com.wosai.smartbiz.user.user.vo.CashierStoreVO;
import com.wosai.smartbiz.user.user.vo.StoreActiveVO;
import com.wosai.smartbiz.user.user.vo.UserLoginV2VO;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/3/9
 */
@JsonRpcService(value = "rpc/retail/cashier/login")
@Validated
public interface CashierLoginRpcService {

    /**
     * 刷新token
     * @param request
     * @return
     */
    AccessTokenVO refreshToken(CashierApiRequest<RefreshTokenParam> request);

    /**
     * 收银机登录
     * @param request
     * @return
     */
    UserLoginV2VO login(CashierApiRequest<LoginParam> request);

    /**
     * 登出
     * @param request
     * @return
     */
    Boolean logout(CashierApiRequest<CashierBaseParam> request);

    /**
     * 收银机登录发送短信验证码
     * @param request
     * @return
     */
    Boolean sendSms(CashierApiRequest<LoginParam> request);

    /**
     * 验证码登录
     * @param request
     * @return
     */
    UserLoginV2VO authCodeLogin(CashierApiRequest<LoginParam> request);

    /**
     * 登录授权状态查询
     * @param request
     * @return
     */
    UserLoginV2VO authStatus(CashierApiRequest<LoginParam> request);

    /**
     * 门店列表
     * @param request
     * @return
     */
    List<CashierStoreVO> storeList(CashierApiRequest<CashierBaseParam> request);

    /**
     * 选择门店
     * @param request
     * @return
     */
    UserLoginV2VO choiceStore(CashierApiRequest<CashierBaseParam> request);

    /**
     * 解绑门店
     * @param request
     * @return
     */
    Boolean unbindStore(CashierApiRequest<CashierBaseParam> request);

    /**
     * 激活门店
     * @param request
     * @return
     */
    StoreActiveVO activeStore(CashierApiRequest<CashierBaseParam> request);
}
