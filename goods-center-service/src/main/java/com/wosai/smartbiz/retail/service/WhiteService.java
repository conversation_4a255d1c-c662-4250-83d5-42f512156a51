package com.wosai.smartbiz.retail.service;

import com.wosai.smartbiz.base.enums.SceneEnum;
import com.wosai.smartbiz.gds.vo.WhiteConfigVO;

import java.util.List;

/**
 * @author: huqi<PERSON><PERSON>
 * @createDate: 2024/1/25
 */
public interface WhiteService {

    /**
     * 批量获取白名单
     * @param scene
     * @param storeId
     * @return
     */
    List<WhiteConfigVO> whiteList(SceneEnum scene, String storeId);
}
