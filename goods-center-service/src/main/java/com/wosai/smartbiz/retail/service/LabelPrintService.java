package com.wosai.smartbiz.retail.service;

import com.wosai.smartbiz.oms.api.pojo.CursorPage;
import com.wosai.smartbiz.retail.dto.CashierLabelPrintParam;
import com.wosai.smartbiz.retail.dto.LabelPrintWaterLineDTO;

public interface LabelPrintService {

    /**
     * 修改标签打印接口
     * @param labelPrintParam
     * @return
     */
    boolean updateLabelPrint(CashierLabelPrintParam labelPrintParam);

    /**
     * 获取最大水位值
     * @param storeId
     * @return
     */
    Long getMaxWaterLine(String storeId);

    /**
     * 分页查询标签打印数据
     * @param labelPrintParam
     * @param deleted
     * @return
     */
    CursorPage<LabelPrintWaterLineDTO> cursorWaterLine(CashierLabelPrintParam labelPrintParam, Integer deleted);
}
