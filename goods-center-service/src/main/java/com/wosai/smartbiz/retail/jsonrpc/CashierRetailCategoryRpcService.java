package com.wosai.smartbiz.retail.jsonrpc;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.base.apisix.cashier.CashierBaseParam;
import com.wosai.smartbiz.gds.dto.CashierRetailCategory;
import com.wosai.web.api.ListResult;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @create 2023/5/15
 */
@JsonRpcService(value = "/rpc/retail/cashier/category")
@Validated
public interface CashierRetailCategoryRpcService {

    /**
     * 同步分类
     * @param request
     * @return
     */
    ListResult<CashierRetailCategory> sync(@Valid CashierApiRequest<CashierBaseParam> request);

}
