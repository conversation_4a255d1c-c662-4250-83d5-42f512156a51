package com.wosai.smartbiz.retail.jsonrpc.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.base.apisix.cashier.CashierBaseParam;
import com.wosai.smartbiz.retail.dto.*;
import com.wosai.smartbiz.retail.jsonrpc.BarcodeScaleProductDistributePlanRpcService;
import com.wosai.smartbiz.retail.service.BarcodeScaleProductDistributePlanService;
import com.wosai.web.api.ListResult;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/5/16
 */
@Service
@AutoJsonRpcServiceImpl
public class BarcodeScaleProductDistributePlanRpcServiceImpl implements BarcodeScaleProductDistributePlanRpcService {

    @Autowired
    private BarcodeScaleProductDistributePlanService barcodeScaleProductDistributePlanService;

    @Override
    public BarcodeScaleProductDistributePlanSaveRes create(CashierApiRequest<BarcodeScaleProductDistributePlanCreateReq> request) {
        BarcodeScaleProductDistributePlanCreateReq body = request.getBody();

        BarcodeScaleProductDistributePlanDTO planDTO = new BarcodeScaleProductDistributePlanDTO();
        // 暂时写死零售
        planDTO.setBizScene("retail");
        planDTO.setStoreId(body.getStoreId());
        planDTO.setTitle(body.getTitle());
        planDTO.setBarcodeFormat(body.getBarcodeFormat());
        planDTO.setProducts(body.getProducts());
        Long planId = barcodeScaleProductDistributePlanService.create(planDTO);

        BarcodeScaleProductDistributePlanSaveRes saveRes = new BarcodeScaleProductDistributePlanSaveRes();
        saveRes.setSuccess(Objects.nonNull(planId));
        saveRes.setPlanId(planId);
        return saveRes;
    }

    @Override
    public BarcodeScaleProductDistributePlanSaveRes update(CashierApiRequest<BarcodeScaleProductDistributePlanUpdateReq> request) {
        BarcodeScaleProductDistributePlanUpdateReq body = request.getBody();

        BarcodeScaleProductDistributePlanDTO planDTO = new BarcodeScaleProductDistributePlanDTO();
        planDTO.setPlanId(body.getPlanId());
        planDTO.setStoreId(body.getStoreId());
        planDTO.setTitle(body.getTitle());
        planDTO.setBarcodeFormat(body.getBarcodeFormat());
        planDTO.setProducts(body.getProducts());
        Long planId = barcodeScaleProductDistributePlanService.update(planDTO);

        BarcodeScaleProductDistributePlanSaveRes saveRes = new BarcodeScaleProductDistributePlanSaveRes();
        saveRes.setSuccess(Objects.nonNull(planId));
        saveRes.setPlanId(planId);
        return saveRes;
    }

    @Override
    public ListResult<BarcodeScaleProductDistributePlanRes> query(CashierApiRequest<CashierBaseParam> request) {
        CashierBaseParam body = request.getBody();

        List<BarcodeScaleProductDistributePlanDTO> planDTOList = barcodeScaleProductDistributePlanService.queryByStoreId(body.getStoreId());
        if (CollectionUtils.isEmpty(planDTOList)) {
            return ListResult.emptyResult();
        }

        List<BarcodeScaleProductDistributePlanRes> planResList = planDTOList.stream()
                .map(dto -> {
                    BarcodeScaleProductDistributePlanRes res = new BarcodeScaleProductDistributePlanRes();
                    res.setPlanId(dto.getPlanId());
                    res.setStoreId(dto.getStoreId());
                    res.setTitle(dto.getTitle());
                    res.setBarcodeFormat(dto.getBarcodeFormat());
                    res.setProducts(dto.getProducts());
                    res.setCtime(dto.getCtime());
                    res.setMtime(dto.getMtime());
                    return res;
                }).collect(Collectors.toList());
        return new ListResult<>(planResList);
    }
}
