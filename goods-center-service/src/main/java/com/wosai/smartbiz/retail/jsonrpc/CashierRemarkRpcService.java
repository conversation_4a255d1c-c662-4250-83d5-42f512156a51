package com.wosai.smartbiz.retail.jsonrpc;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.base.apisix.cashier.CashierBaseParam;
import com.wosai.smartbiz.dto.vo.remark.CashierOrderRemarkVO;
import com.wosai.smartbiz.vo.gds.VersionVO;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/3/9
 */
@JsonRpcService(value = "rpc/retail/cashier/remark")
@Validated
public interface CashierRemarkRpcService {

    /**
     * 获取收银机最新版本
     * @param request
     * @return
     */
    List<CashierOrderRemarkVO> list(CashierApiRequest<CashierBaseParam> request);
}
