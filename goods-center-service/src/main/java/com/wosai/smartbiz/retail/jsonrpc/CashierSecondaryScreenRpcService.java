package com.wosai.smartbiz.retail.jsonrpc;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.gds.vo.SecondaryScreenVO;
import com.wosai.smartbiz.retail.dto.SecondaryScreenDetailParam;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 * @create 2023/8/30
 */
@JsonRpcService("rpc/retail/secondary/screen")
@Validated
public interface CashierSecondaryScreenRpcService {

    /**
     * 门店副屏配置详情
     * @param request
     * @return
     */
    SecondaryScreenVO detail(CashierApiRequest<SecondaryScreenDetailParam> request);

}