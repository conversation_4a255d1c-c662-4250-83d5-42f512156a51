package com.wosai.smartbiz.retail.service.impl;

import com.wosai.smart.goods.common.constant.BusinessSceneEnum;
import com.wosai.smart.goods.common.constant.StockConstant;
import com.wosai.smart.goods.stock.IStockRpcService;
import com.wosai.smart.goods.stock.dto.StockWithSpuIdDTO;
import com.wosai.smart.goods.stock.req.StockBatchUpdateReq;
import com.wosai.smart.goods.stock.req.StockItemReq;
import com.wosai.smartbiz.gds.dto.CashierRetailProduct;
import com.wosai.smartbiz.retail.constant.ProductOperateEnum;
import com.wosai.smartbiz.retail.dto.StockBatchUpdateParam;
import com.wosai.smartbiz.retail.service.RetailStockService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class RetailStockServiceImpl implements RetailStockService {

    @Autowired
    private IStockRpcService stockRpcService;

    @Override
    public boolean batchUpdateStock(StockBatchUpdateParam stockBatchUpdateParam) {

        if (stockBatchUpdateParam == null || CollectionUtils.isEmpty(stockBatchUpdateParam.getStockItems())) {
            return false;
        }
        StockBatchUpdateReq stockBatchUpdateRequest = new StockBatchUpdateReq();
        List<StockItemReq> itemList = new ArrayList<>();

        for (StockBatchUpdateParam.StockItem stockItem : stockBatchUpdateParam.getStockItems()) {
            CashierRetailProduct.Stock stock = stockItem.getStock();
            StockItemReq stockItemRequest = new StockItemReq();
            stockItemRequest.setSpuId(stockItem.getSpuId());
            stockItemRequest.setSkuId(stockItem.getSkuId());
            Integer type = stock.getManageStock() ? StockConstant.StockTypeEnum.LIMITED.getCode() : StockConstant.StockTypeEnum.INFINITE.getCode();
            stockItemRequest.setType(type);
            BigDecimal stockValue = Optional.ofNullable(stock.getStockValue())
                    .map(value -> new BigDecimal(String.valueOf(value)))
                    .orElse(null);
            if (stockValue == null) {
                List<StockWithSpuIdDTO> stockWithSpuIdDTOS = stockRpcService.listBySpuIds(stockBatchUpdateParam.getStoreId(), Arrays.asList(stockItem.getSpuId()));
                if (stockWithSpuIdDTOS != null && stockWithSpuIdDTOS.size() > 0) {
                    StockWithSpuIdDTO stockWithSpuIdDTO = stockWithSpuIdDTOS.get(0);
                    if (stockWithSpuIdDTO != null) {
                        stockValue = stockWithSpuIdDTO.getQuantity();
                    }
                }
            }
            // 有限库存，兜底为0
            if (stock.getManageStock() && stockValue == null) {
                stockValue = new BigDecimal(0);
            }

            stockItemRequest.setQuantity(stockValue);
            itemList.add(stockItemRequest);
        }
        Map<String, Object> extra = new HashMap<>();
        if (Objects.equals(stockBatchUpdateParam.getOperateType(), ProductOperateEnum.ADD.getCode())) {
            extra.put(StockConstant.StockFlowExtraConstant.BIZ_TYPE, StockConstant.RemarkBizType.INIT_STOCK.getCode());
            stockBatchUpdateRequest.setBizType(StockConstant.BizReqTypeEnum.INIT_STOCK.getCode());
        }else{
            extra.put(StockConstant.StockFlowExtraConstant.BIZ_TYPE, StockConstant.RemarkBizType.PRODUCT_UPDATE.getCode());
            stockBatchUpdateRequest.setBizType(StockConstant.BizReqTypeEnum.STOCK_UPDATE.getCode());

        }
        stockBatchUpdateRequest.setExtra(extra);
        stockBatchUpdateRequest.setItemList(itemList);
        stockBatchUpdateRequest.setOperatorId(stockBatchUpdateParam.getMerchantUserId());
        stockBatchUpdateRequest.setBusinessScene(BusinessSceneEnum.RETAIL.getCode());

        stockBatchUpdateRequest.setStoreId(stockBatchUpdateParam.getStoreId());
        stockRpcService.batchUpdateStock(stockBatchUpdateRequest);
        return true;
    }
}
