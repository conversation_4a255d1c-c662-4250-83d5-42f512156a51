package com.wosai.smartbiz.retail.jsonrpc.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.base.apisix.cashier.CashierBaseParam;
import com.wosai.smartbiz.base.enums.SceneEnum;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.gds.vo.WhiteConfigVO;
import com.wosai.smartbiz.retail.jsonrpc.CashierRetailWhiteRpcService;
import com.wosai.smartbiz.retail.service.WhiteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: huqiaomu
 * @createDate: 2024/1/23
 */
@Slf4j
@Service
@AutoJsonRpcServiceImpl
public class CashierRetailWhiteRpcServiceImpl implements CashierRetailWhiteRpcService {

    @Autowired
    private WhiteService whiteService;

    @Override
    public List<WhiteConfigVO> retailWhiteList(CashierApiRequest<CashierBaseParam> request) {
        CashierBaseParam body = request.getBody();
        if (body == null || body.getStoreId() == null) {
            throw new ParamException("门店ID不能为空");
        }
        return whiteService.whiteList(SceneEnum.RETAIL, body.getStoreId());
    }
}
