package com.wosai.smartbiz.retail.service.impl;

import com.wosai.smart.standard.api.category.ICategoryRpcService;
import com.wosai.smart.standard.api.category.dto.CategoryFlatDTO;
import com.wosai.smart.standard.api.goods.IRetailGoodsRpcService;
import com.wosai.smart.standard.api.goods.dto.RetailGoodsDTO;
import com.wosai.smartbiz.retail.service.RetailStandardGoodsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/9/18
 */
@Service
@Slf4j
public class RetailStandardGoodsServiceImpl implements RetailStandardGoodsService {

    @Autowired
    private IRetailGoodsRpcService retailGoodsRpcService;

    @Autowired
    private ICategoryRpcService categoryRpcService;

    @Override
    public RetailGoodsDTO queryByBarcode(String barcode) {
        return retailGoodsRpcService.queryByBarcode(barcode);
    }

    @Override
    public List<CategoryFlatDTO> listCategoryByIds(List<Long> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return Collections.emptyList();
        }
        return categoryRpcService.queryFlatCategoryByIds(categoryIds);
    }
}
