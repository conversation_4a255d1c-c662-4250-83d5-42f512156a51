package com.wosai.smartbiz.retail.jsonrpc.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.base.apisix.cashier.CashierUser;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.gds.service.SecondaryScreenRpcService;
import com.wosai.smartbiz.gds.vo.SecondaryScreenVO;
import com.wosai.smartbiz.retail.dto.SecondaryScreenDetailParam;
import com.wosai.smartbiz.retail.jsonrpc.CashierSecondaryScreenRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: huqiaomu
 * @createDate: 2023/8/30
 */
@Slf4j
@Component
@AutoJsonRpcServiceImpl
public class CashierSecondaryScreenRpcServiceImpl implements CashierSecondaryScreenRpcService {

    @Autowired
    private SecondaryScreenRpcService secondaryScreenRpcService;

    @Override
    public SecondaryScreenVO detail(CashierApiRequest<SecondaryScreenDetailParam> request) {
        SecondaryScreenDetailParam detailParam = request.getBody();
        if(null == detailParam || StringUtils.isBlank(detailParam.getStoreId())){
            throw new ParamException("门店ID不能为空");
        }
        String storeId = detailParam.getStoreId();
        return secondaryScreenRpcService.secondaryScreenDetail(storeId);
    }

}
