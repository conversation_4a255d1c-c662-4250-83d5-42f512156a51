package com.wosai.smartbiz.retail.jsonrpc;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.base.apisix.cashier.CashierBaseParam;
import com.wosai.smartbiz.vo.gds.VersionVO;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 * @create 2023/3/9
 */
@JsonRpcService(value = "rpc/retail/cashier/version")
@Validated
public interface CashierVersionRpcService {

    /**
     * 获取收银机最新版本
     * @param request
     * @return
     */
    VersionVO latest(CashierApiRequest<CashierBaseParam> request);
}
