package com.wosai.smartbiz.retail.util;

import com.wosai.smart.goods.constant.CommonConstant;
import com.wosai.smartbiz.gds.dto.CashierRetailCategory;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023/9/18
 */
public class RetailCategoryConverter {


    public static CashierRetailCategory copy(CashierRetailCategory c) {
        Objects.requireNonNull(c);
        CashierRetailCategory cp = new CashierRetailCategory();
        cp.setCategoryId(c.getCategoryId());
        cp.setName(c.getName());
        cp.setSeq(c.getSeq());
        cp.setLevel(c.getLevel());
        cp.setParentCategoryId(c.getParentCategoryId());
        return cp;
    }

    public static List<CashierRetailCategory> chainNodesById(Long categoryId, List<CashierRetailCategory> storeCategories) {
        if (CollectionUtils.isEmpty(storeCategories)) {
            return Collections.emptyList();
        }
        Map<Long, CashierRetailCategory> catIdMap = storeCategories.stream()
                .collect(Collectors.toMap(CashierRetailCategory::getCategoryId, Function.identity(), (k1, k2) -> k1));
        // 当前节点
        CashierRetailCategory curNode = catIdMap.get(categoryId);
        if (Objects.isNull(curNode)) {
            return Collections.emptyList();
        }
        int level = Optional.ofNullable(curNode.getLevel()).orElse(1);
        List<CashierRetailCategory> nodes = new ArrayList<>(level);
        // 自下而上补全分类路径
        for (int i = 0; i < level; ++i) {
            if (Objects.isNull(curNode)) {
                break;
            }
            nodes.add(0, copy(curNode));
            if (curNode.getLevel() <= 1 || Objects.equals(curNode.getParentCategoryId(), CommonConstant.DEFAULT_CATEGORY_PARENT_ID)) {
                break;
            }
            curNode = catIdMap.get(curNode.getParentCategoryId());
        }
        return nodes;
    }

    public static List<CashierRetailCategory> chainNodeByName(String name, List<CashierRetailCategory> storeCategories) {
        if (CollectionUtils.isEmpty(storeCategories)) {
            return Collections.emptyList();
        }
        Map<String, CashierRetailCategory> nameMap = storeCategories.stream()
                .collect(Collectors.toMap(CashierRetailCategory::getName, Function.identity(), (k1, k2) -> k1));

        CashierRetailCategory cat = nameMap.get(name);
        if (Objects.isNull(cat)) {
            return Collections.emptyList();
        }
        return chainNodesById(cat.getCategoryId(), storeCategories);
    }


}
