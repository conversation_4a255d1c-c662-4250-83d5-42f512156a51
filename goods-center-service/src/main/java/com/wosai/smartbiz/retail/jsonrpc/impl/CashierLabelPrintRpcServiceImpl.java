package com.wosai.smartbiz.retail.jsonrpc.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.base.apisix.cashier.CashierBaseParam;
import com.wosai.smartbiz.base.enums.YesNoEnum;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.oms.api.pojo.CursorPage;
import com.wosai.smartbiz.retail.dto.CashierLabelPrintParam;
import com.wosai.smartbiz.retail.dto.LabelPrintWaterLineDTO;
import com.wosai.smartbiz.retail.jsonrpc.CashierLabelPrintRpcService;
import com.wosai.smartbiz.retail.service.LabelPrintService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: redhat
 */
@Slf4j
@Component
@AutoJsonRpcServiceImpl
public class CashierLabelPrintRpcServiceImpl implements CashierLabelPrintRpcService {
    private final static int MAX_LABEL_PRINT_GOODS_COUNT = 200;

    @Autowired
    private LabelPrintService labelPrintService;

    @Override
    public boolean update(CashierApiRequest<CashierLabelPrintParam> request) {
        CashierLabelPrintParam labelPrintParam = request.getBody();
        if(null == labelPrintParam || StringUtils.isBlank(labelPrintParam.getStoreId())){
            throw new ParamException("门店ID不能为空");
        }

        if(CollectionUtils.isEmpty(labelPrintParam.getGoods())){
            throw new ParamException("商品标签数据不能为空");
        }

        if(labelPrintParam.getGoods().size() > MAX_LABEL_PRINT_GOODS_COUNT){
            throw new ParamException("商品标签数据不能超过" + MAX_LABEL_PRINT_GOODS_COUNT + "条");
        }
        return labelPrintService.updateLabelPrint(labelPrintParam);
    }

    @Override
    public Long waterLine(CashierApiRequest<CashierBaseParam> request) {
        CashierBaseParam cashierBaseParam = request.getBody();
        if(null == cashierBaseParam || StringUtils.isBlank(cashierBaseParam.getStoreId())){
            throw new ParamException("门店ID不能为空");
        }
        return labelPrintService.getMaxWaterLine(cashierBaseParam.getStoreId());
    }

    @Override
    public CursorPage<LabelPrintWaterLineDTO> sync(CashierApiRequest<CashierLabelPrintParam> request) {
        CashierLabelPrintParam labelPrintParam = request.getBody();
        if(null == labelPrintParam || StringUtils.isBlank(labelPrintParam.getStoreId())){
            throw new ParamException("门店ID不能为空");
        }
        return labelPrintService.cursorWaterLine(labelPrintParam, YesNoEnum.N.getValue());
    }

    @Override
    public CursorPage<LabelPrintWaterLineDTO> syncDeleted(CashierApiRequest<CashierLabelPrintParam> request) {
        CashierLabelPrintParam labelPrintParam = request.getBody();
        if(null == labelPrintParam || StringUtils.isBlank(labelPrintParam.getStoreId())){
            throw new ParamException("门店ID不能为空");
        }
        return labelPrintService.cursorWaterLine(labelPrintParam, YesNoEnum.Y.getValue());
    }

}
