package com.wosai.smartbiz.retail.jsonrpc.impl;

import com.google.common.collect.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.base.apisix.cashier.CashierClient;
import com.wosai.smartbiz.base.apisix.cashier.CashierUser;
import com.wosai.smartbiz.base.enums.YesNoEnum;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.gds.dto.licence.LicenceClientInfoDTO;
import com.wosai.smartbiz.gds.enums.LicenceBrandEnum;
import com.wosai.smartbiz.gds.request.LicenceRequest;
import com.wosai.smartbiz.gds.service.ProductLicenceService;
import com.wosai.smartbiz.retail.dto.CashierAiActiveCheckDTO;
import com.wosai.smartbiz.retail.dto.CashierAiActiveDTO;
import com.wosai.smartbiz.retail.dto.LicenceActiveParam;
import com.wosai.smartbiz.retail.jsonrpc.CashierAiRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * @author: redhat
 */
@Slf4j
@Component
@AutoJsonRpcServiceImpl
public class CashierAiRpcServiceImpl implements CashierAiRpcService {

    @Value("${ai.alipay.isv-id}")
    private String alipayIsvId;
    @Value("${ai.osai.isv-id}")
    private String osaiIsvId;
    @Value("${ai.youyou.code}")
    private String youyouCode;
    @Value("${ai.youyou.key}")
    private String youyouKey;

    @Value("${ai.sunmi.app-id}")
    private String sunmiAiAppId;
    @Value("${ai.sunmi.app-key}")
    private String sunmiAiAppKey;

    @Autowired
    private ProductLicenceService productLicenceService;

    /**
     * 构建AI激活信息数据
     * @param clientId
     * @param clientInfoDTO
     * @param request
     * @param querySurplusQuantity
     * @return
     */
    private CashierAiActiveDTO buildCashierAiActiveDTO(String clientId, LicenceClientInfoDTO clientInfoDTO, LicenceRequest request, boolean querySurplusQuantity) {
        CashierAiActiveDTO aiActiveDTO = CashierAiActiveDTO.builder().build();
        if(null == clientInfoDTO){
            aiActiveDTO.setEnable(false);
            return aiActiveDTO;
        }
        CashierAiActiveDTO.AiInfo aiInfo = CashierAiActiveDTO.AiInfo.builder()
                .id(clientInfoDTO.getId())
                .brand(clientInfoDTO.getBrand())
                .productType(clientInfoDTO.getProductType())
                .licenceNo(clientInfoDTO.getLicenceNo())
                .activated(clientInfoDTO.getActivated())
                .extLicenceNo(clientInfoDTO.getExtLicenceNo())
                .extBusinessType(clientInfoDTO.getExtBusinessType())
                .build();
        switch (clientInfoDTO.getBrand()){
            case YOUYOU:
                aiInfo.setCode(youyouCode);
                aiInfo.setKey(youyouKey);
                break;
            case SUNMI:
                aiInfo.setCode(sunmiAiAppId);
                aiInfo.setKey(sunmiAiAppKey);
                break;
            case OSAI:
                aiInfo.setIsvId(osaiIsvId);
                break;
            case ALIPAY:
                aiInfo.setIsvId(alipayIsvId);
                break;
        }
        aiActiveDTO.setEnable(true);
        aiActiveDTO.setAiInfo(aiInfo);

        if(querySurplusQuantity){
            List<LicenceBrandEnum> brandList = Lists.newArrayList(LicenceBrandEnum.YOUYOU);
            if(request.isQuerySunmiAi()){
                brandList.add(LicenceBrandEnum.SUNMI);
            }
            if(request.isQueryOsAi()){
                brandList.add(LicenceBrandEnum.OSAI);
            }
            if(request.isQueryAlipayAi()){
                brandList.add(LicenceBrandEnum.ALIPAY);
            }
            //查询门店绑定可用许可证数量
            Integer storeBindProductCount = productLicenceService.getProductLicenceBindCount(clientInfoDTO.getMerchantId(), clientInfoDTO.getStoreId(), brandList, Lists.newArrayList(clientInfoDTO.getProductType()), YesNoEnum.N);
            aiActiveDTO.setSurplusQuantity(storeBindProductCount);
        }

        //如果获取到的clientId和当前设备不一致,则标记许可证和许可证状态为null
        if(StringUtils.isNotBlank(clientInfoDTO.getClientId()) && !Objects.equals(clientId, clientInfoDTO.getClientId())){
            aiInfo.setLicenceNo(null);
            aiInfo.setActivated(YesNoEnum.N);
        }
        return aiActiveDTO;
    }

    @Override
    public CashierAiActiveDTO activeDetail(CashierApiRequest<LicenceActiveParam> request) {
        LicenceActiveParam activeParam = request.getBody();
        CashierUser user = request.getUser();
        CashierClient client = request.getClient();
        if(null == activeParam || StringUtils.isBlank(activeParam.getStoreId())){
            throw new ParamException("门店ID不能为空");
        }
        if(StringUtils.isBlank(client.getTerminalId())){
            throw new ParamException("设备唯一ID不能为空");
        }
        String clientId = client.getTerminalId();
        LicenceRequest licenceRequest = new LicenceRequest()
                .setMerchantId(user.getMerchantId())
                .setStoreId(activeParam.getStoreId())
                .setClientId(client.getTerminalId())
                .setQueryStoreBind(true);
        LicenceClientInfoDTO clientInfoDTO = productLicenceService.aiActiveDetail(licenceRequest);
        return buildCashierAiActiveDTO(clientId, clientInfoDTO, licenceRequest,true);
    }

    @Override
    public CashierAiActiveDTO activeDetailV2(CashierApiRequest<LicenceActiveParam> request) {
        LicenceActiveParam activeParam = request.getBody();
        CashierUser user = request.getUser();
        CashierClient client = request.getClient();
        if(null == activeParam || StringUtils.isBlank(activeParam.getStoreId())){
            throw new ParamException("门店ID不能为空");
        }
        if(StringUtils.isBlank(client.getTerminalId())){
            throw new ParamException("设备唯一ID不能为空");
        }
        String clientId = client.getTerminalId();
        LicenceRequest licenceRequest = new LicenceRequest()
                .setMerchantId(user.getMerchantId())
                .setStoreId(activeParam.getStoreId())
                .setClientId(clientId)
                .setQueryStoreBind(false)
                .setQueryAlipayAi(true);

        LicenceClientInfoDTO clientInfoDTO = productLicenceService.aiActiveDetail(licenceRequest);
        return buildCashierAiActiveDTO(clientId, clientInfoDTO, licenceRequest,false);
    }

    @Override
    public CashierAiActiveDTO activeDetailV3(CashierApiRequest<LicenceActiveParam> request) {
        LicenceActiveParam activeParam = request.getBody();
        CashierUser user = request.getUser();
        CashierClient client = request.getClient();
        if(null == activeParam || StringUtils.isBlank(activeParam.getStoreId())){
            throw new ParamException("门店ID不能为空");
        }
        if(StringUtils.isBlank(client.getTerminalId())){
            throw new ParamException("设备唯一ID不能为空");
        }
        String clientId = client.getTerminalId();
        LicenceRequest licenceRequest = new LicenceRequest()
                .setMerchantId(user.getMerchantId())
                .setStoreId(activeParam.getStoreId())
                .setClientId(clientId)
                .setQueryStoreBind(false)
                .setQueryAlipayAi(true)
                .setQueryOsAi(true);

        LicenceClientInfoDTO clientInfoDTO = productLicenceService.aiActiveDetail(licenceRequest);
        return buildCashierAiActiveDTO(clientId, clientInfoDTO, licenceRequest,false);
    }

    @Override
    public CashierAiActiveDTO activeDetailV4(CashierApiRequest<LicenceActiveParam> request) {
        LicenceActiveParam activeParam = request.getBody();
        CashierUser user = request.getUser();
        CashierClient client = request.getClient();
        if(null == activeParam || StringUtils.isBlank(activeParam.getStoreId())){
            throw new ParamException("门店ID不能为空");
        }
        if(StringUtils.isBlank(client.getTerminalId())){
            throw new ParamException("设备唯一ID不能为空");
        }
        String clientId = client.getTerminalId();
        LicenceRequest licenceRequest = new LicenceRequest()
                .setMerchantId(user.getMerchantId())
                .setStoreId(activeParam.getStoreId())
                .setClientId(clientId)
                .setQueryStoreBind(false)
                .setQueryAlipayAi(true)
                .setQueryOsAi(true)
                .setQuerySunmiAi(true);

        LicenceClientInfoDTO clientInfoDTO = productLicenceService.aiActiveDetail(licenceRequest);
        return buildCashierAiActiveDTO(clientId, clientInfoDTO, licenceRequest,false);
    }

    @Override
    public CashierAiActiveDTO activeLicence(CashierApiRequest<LicenceActiveParam> request) {
        LicenceActiveParam activeParam = request.getBody();
        CashierUser user = request.getUser();
        CashierClient client = request.getClient();
        if(null == activeParam || StringUtils.isBlank(activeParam.getStoreId())){
            throw new ParamException("门店ID不能为空");
        }
        if(StringUtils.isBlank(client.getTerminalId())){
            throw new ParamException("设备唯一ID不能为空");
        }
        if(null == activeParam.getBrand()){
            throw new ParamException("品牌不能为空");
        }
        if(null == activeParam.getProductType()){
            throw new ParamException("产品类型不能为空");
        }
        LicenceRequest licenceRequest = new LicenceRequest()
                .setMerchantId(user.getMerchantId())
                .setStoreId(activeParam.getStoreId())
                .setClientId(client.getTerminalId());
        licenceRequest.setBrand(activeParam.getBrand());
        licenceRequest.setProductType(activeParam.getProductType());

        LicenceClientInfoDTO clientInfoDTO = productLicenceService.aiActiveLicence(licenceRequest);
        return buildCashierAiActiveDTO(client.getTerminalId(), clientInfoDTO, licenceRequest,true);
    }

    @Override
    public CashierAiActiveDTO activeLicenceV2(CashierApiRequest<LicenceActiveParam> request) {
        LicenceActiveParam activeParam = request.getBody();
        CashierUser user = request.getUser();
        CashierClient client = request.getClient();
        if(null == activeParam || StringUtils.isBlank(activeParam.getStoreId())){
            throw new ParamException("门店ID不能为空");
        }
        if(StringUtils.isBlank(client.getTerminalId())){
            throw new ParamException("设备唯一ID不能为空");
        }

        LicenceRequest licenceRequest = new LicenceRequest()
                .setMerchantId(user.getMerchantId())
                .setStoreId(activeParam.getStoreId())
                .setClientId(client.getTerminalId())
                .setQueryStoreBind(true)
                .setQueryAlipayAi(true);

        LicenceClientInfoDTO clientInfoDTO = productLicenceService.aiActiveLicenceV2(licenceRequest);
        return buildCashierAiActiveDTO(client.getTerminalId(), clientInfoDTO, licenceRequest,true);
    }

    @Override
    public CashierAiActiveDTO activeLicenceV3(CashierApiRequest<LicenceActiveParam> request) {
        LicenceActiveParam activeParam = request.getBody();
        CashierUser user = request.getUser();
        CashierClient client = request.getClient();
        if(null == activeParam || StringUtils.isBlank(activeParam.getStoreId())){
            throw new ParamException("门店ID不能为空");
        }
        if(StringUtils.isBlank(client.getTerminalId())){
            throw new ParamException("设备唯一ID不能为空");
        }
        LicenceRequest licenceRequest = new LicenceRequest()
                .setMerchantId(user.getMerchantId())
                .setStoreId(activeParam.getStoreId())
                .setClientId(client.getTerminalId())
                .setQueryStoreBind(true)
                .setQueryAlipayAi(true)
                .setQueryOsAi(true);

        LicenceClientInfoDTO clientInfoDTO = productLicenceService.aiActiveLicenceV2(licenceRequest);
        return buildCashierAiActiveDTO(client.getTerminalId(), clientInfoDTO, licenceRequest, true);
    }

    @Override
    public CashierAiActiveDTO activeLicenceV4(CashierApiRequest<LicenceActiveParam> request) {
        LicenceActiveParam activeParam = request.getBody();
        CashierUser user = request.getUser();
        CashierClient client = request.getClient();
        if(null == activeParam || StringUtils.isBlank(activeParam.getStoreId())){
            throw new ParamException("门店ID不能为空");
        }
        if(StringUtils.isBlank(client.getTerminalId())){
            throw new ParamException("设备唯一ID不能为空");
        }
        LicenceRequest licenceRequest = new LicenceRequest()
                .setMerchantId(user.getMerchantId())
                .setStoreId(activeParam.getStoreId())
                .setClientId(client.getTerminalId())
                .setQueryStoreBind(true)
                .setQueryAlipayAi(true)
                .setQueryOsAi(true)
                .setQuerySunmiAi(true);

        LicenceClientInfoDTO clientInfoDTO = productLicenceService.aiActiveLicenceV2(licenceRequest);
        return buildCashierAiActiveDTO(client.getTerminalId(), clientInfoDTO, licenceRequest, true);
    }

    @Override
    public CashierAiActiveCheckDTO activeCheck(CashierApiRequest<LicenceActiveParam> request) {
        LicenceActiveParam activeParam = request.getBody();
        CashierUser user = request.getUser();
        CashierClient client = request.getClient();
        LicenceRequest licenceRequest = new LicenceRequest()
                .setId(activeParam.getId())
                .setMerchantId(user.getMerchantId())
                .setStoreId(activeParam.getStoreId())
                .setClientId(client.getTerminalId());

        licenceRequest.setBrand(activeParam.getBrand());
        licenceRequest.setProductType(activeParam.getProductType());

        return productLicenceService.aiActiveCheck(licenceRequest);
    }

    @Override
    public CashierAiActiveDTO.AiInfo active(CashierApiRequest<LicenceActiveParam> request) {
        LicenceActiveParam activeParam = request.getBody();
        CashierUser user = request.getUser();
        CashierClient client = request.getClient();
        LicenceRequest licenceRequest = new LicenceRequest()
                .setId(activeParam.getId())
                .setMerchantId(user.getMerchantId())
                .setStoreId(activeParam.getStoreId())
                .setClientId(client.getTerminalId())
                .setClientName(null);
        LicenceClientInfoDTO clientInfoDTO = productLicenceService.aiActive(licenceRequest);
        CashierAiActiveDTO.AiInfo aiInfo = CashierAiActiveDTO.AiInfo.builder()
                .brand(clientInfoDTO.getBrand())
                .productType(clientInfoDTO.getProductType())
                .licenceNo(clientInfoDTO.getLicenceNo())
                .activated(clientInfoDTO.getActivated())
                .build();
        return aiInfo;
    }
}
