package com.wosai.smartbiz.retail.jsonrpc.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.smart.goods.common.constant.ProductTagEnum;
import com.wosai.smart.goods.common.model.BizResult;
import com.wosai.smart.goods.constant.TemplateCodeConstant;
import com.wosai.smart.goods.product.req.ProductReqWrapper;
import com.wosai.smart.goods.tool.IProductTagRpcService;
import com.wosai.smart.goods.tool.req.ProductTagCreateReq;
import com.wosai.smart.goods.tool.req.ProductTagQueryReq;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.base.exceptions.BusinessException;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.retail.dto.ProductTagCreateParam;
import com.wosai.smartbiz.retail.dto.ProductTagQueryParam;
import com.wosai.smartbiz.retail.jsonrpc.RetailProductTagRpcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoJsonRpcServiceImpl
public class RetailProductTagRpcServiceImpl implements RetailProductTagRpcService {

    @Value("${smart.goods.retail.biz-code}")
    private String retailBizCode;

    @Value("${smart.goods.retail.biz-scene}")
    private String retailBizScene;

    @Autowired
    private IProductTagRpcService productTagRpcService;

    @Override
    public void createProductTag(CashierApiRequest<ProductTagCreateParam> request) {
        ProductTagCreateParam productTagCreateParam = request.getBody();
        ProductTagEnum productTagEnum = ProductTagEnum.getByCode(productTagCreateParam.getTag());
        if(productTagEnum == null){
            throw new ParamException("tag类型不存在，请检查");
        }
        ProductReqWrapper<ProductTagCreateReq> productTagCreateReqProductReqWrapper = new ProductReqWrapper();
        ProductTagCreateReq productTagCreateReq = new ProductTagCreateReq();
        productTagCreateReq.setSpuIds(productTagCreateParam.getSpuIds());
        productTagCreateReq.setTag(productTagCreateParam.getTag());
        productTagCreateReqProductReqWrapper.setBizParams(productTagCreateReq);
        productTagCreateReqProductReqWrapper.setStoreId(productTagCreateParam.getStoreId());
        productTagCreateReqProductReqWrapper.setTemplateCode(TemplateCodeConstant.RETAIL);
        productTagCreateReqProductReqWrapper.setBizScene(retailBizScene);
        productTagCreateReqProductReqWrapper.setBizCode(retailBizCode);
        BizResult<Long> result = productTagRpcService.create(productTagCreateReqProductReqWrapper);
        if(!result.isSuccess()){
            throw new BusinessException(result.getErrorMessage());
        }
    }

    @Override
    public List<Long> findStoreTagProduct(CashierApiRequest<ProductTagQueryParam> request) {
        ProductTagQueryParam productTagQueryParam = request.getBody();
        ProductTagEnum productTagEnum = ProductTagEnum.getByCode(productTagQueryParam.getTag());
        if(productTagEnum == null){
            throw new ParamException("tag类型不存在，请检查");
        }

        ProductReqWrapper<ProductTagQueryReq> productTagQueryReqProductReqWrapper = new ProductReqWrapper();
        ProductTagQueryReq productTagQueryReq = new ProductTagQueryReq();
        productTagQueryReq.setTag(productTagQueryParam.getTag());
        productTagQueryReqProductReqWrapper.setBizParams(productTagQueryReq);
        productTagQueryReqProductReqWrapper.setStoreId(productTagQueryParam.getStoreId());
        productTagQueryReqProductReqWrapper.setTemplateCode(TemplateCodeConstant.RETAIL);
        productTagQueryReqProductReqWrapper.setBizScene(retailBizScene);
        productTagQueryReqProductReqWrapper.setBizCode(retailBizCode);
        BizResult<List<Long>>  result = productTagRpcService.findStoreTagProductList(productTagQueryReqProductReqWrapper);
        if(!result.isSuccess()){
            throw new BusinessException(result.getErrorMessage());
        }
        return result.getResult();
    }
}
