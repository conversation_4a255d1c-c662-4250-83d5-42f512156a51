package com.wosai.smartbiz.retail.service;

import com.wosai.smart.goods.dto.SpuCountDTO;
import com.wosai.smartbiz.gds.dto.CashierRetailCategory;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/8/21
 */
public interface RetailCategoryService {

    /**
     * 查询门店下所有分类
     * @param storeId
     * @return
     */
    List<CashierRetailCategory> queryByStore(String storeId);

    /**
     * 分级创建，传入的数据按一级、二级已排序，优先创建一级分类，再创建二级分类
     * @param storeId
     * @param categories
     * @return
     */
    List<CashierRetailCategory> createByLevel(String storeId, List<CashierRetailCategory> categories);

    /**
     * 创建分类并返回分类ID
     * @param storeId
     * @param category
     * @return
     */
    Long createAndReturn(String storeId, CashierRetailCategory category);

    /**
     * 查询门店下各分类的商品数量
     */
    List<SpuCountDTO> countProduct(String storeId);

}
