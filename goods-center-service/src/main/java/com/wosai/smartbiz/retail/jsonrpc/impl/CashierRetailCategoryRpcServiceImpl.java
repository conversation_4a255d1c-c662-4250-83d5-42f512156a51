package com.wosai.smartbiz.retail.jsonrpc.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.base.apisix.cashier.CashierBaseParam;
import com.wosai.smartbiz.gds.dto.CashierRetailCategory;
import com.wosai.smartbiz.retail.jsonrpc.CashierRetailCategoryRpcService;
import com.wosai.smartbiz.retail.service.RetailCategoryService;
import com.wosai.web.api.ListResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/5/15
 */
@Slf4j
@Service
@AutoJsonRpcServiceImpl
public class CashierRetailCategoryRpcServiceImpl implements CashierRetailCategoryRpcService {

    @Autowired
    private RetailCategoryService retailCategoryService;

    @Override
    public ListResult<CashierRetailCategory> sync(CashierApiRequest<CashierBaseParam> request) {
        String storeId = request.getBody().getStoreId();
        List<CashierRetailCategory> categories = retailCategoryService.queryByStore(storeId);
        return new ListResult<>(categories);
    }
}
