package com.wosai.smartbiz.retail.service.impl;

import com.google.common.collect.Maps;
import com.wosai.market.awesome.message.dto.RmqxMessage;
import com.wosai.market.awesome.message.enums.NoticeType;
import com.wosai.market.awesome.message.service.RmqxMessageRpcService;
import com.wosai.smartbiz.base.enums.BusinessTypeEnum;
import com.wosai.smartbiz.base.enums.OwnerTypeEnum;
import com.wosai.smartbiz.base.enums.SceneEnum;
import com.wosai.smartbiz.base.enums.YesNoEnum;
import com.wosai.smartbiz.base.exceptions.BusinessException;
import com.wosai.smartbiz.gds.dto.CashierRetailProduct;
import com.wosai.smartbiz.goods.direct.CashierWaterLineDAO;
import com.wosai.smartbiz.goods.direct.LabelPrintWaterLineDAO;
import com.wosai.smartbiz.goods.domain.CashierWaterLineDO;
import com.wosai.smartbiz.goods.domain.LabelPrintWaterLineDO;
import com.wosai.smartbiz.goods.query.CashierWaterLineQuery;
import com.wosai.smartbiz.oms.api.pojo.CursorPage;
import com.wosai.smartbiz.retail.dto.CashierLabelPrintParam;
import com.wosai.smartbiz.retail.dto.LabelPrintWaterLineDTO;
import com.wosai.smartbiz.retail.service.LabelPrintService;
import com.wosai.smartbiz.retail.service.RetailProductService;
import com.wosai.smartbiz.utils.ConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: redhat
 */
@Slf4j
@Service
public class LabelPrintServiceImpl implements LabelPrintService {
    @Autowired
    private CashierWaterLineDAO cashierWaterLineDAO;
    @Autowired
    private LabelPrintWaterLineDAO labelPrintWaterLineDAO;
    @Autowired
    private RetailProductService retailProductService;
    @Autowired
    private RmqxMessageRpcService awmRmqxMessageRpcService;

    @Override
    public boolean updateLabelPrint(CashierLabelPrintParam labelPrintParam) {
        String storeId = labelPrintParam.getStoreId();
        CashierWaterLineDO lineDO = new CashierWaterLineDO();
        lineDO.setOwnerId(storeId);
        lineDO.setOwnerType(OwnerTypeEnum.STORE);
        lineDO.setBusinessType(BusinessTypeEnum.LABEL_PRINT);
        int row = cashierWaterLineDAO.upsert(lineDO);
        if(row == 0){
            throw new BusinessException("标签打印数据保存失败，请稍后再试");
        }

        CashierWaterLineDO existsWaterLineDO = cashierWaterLineDAO.getByOwnerIdBusinessType(storeId, OwnerTypeEnum.STORE, BusinessTypeEnum.LABEL_PRINT);
        if(null == existsWaterLineDO){
            throw new BusinessException("标签打印数据保存失败，请稍后再试");
        }

        List<Long> spuIdList = labelPrintParam.getGoods().stream().map(CashierLabelPrintParam.LabelPrintGoods::getSpuId).collect(Collectors.toList());
        Map<Long, CashierRetailProduct> retailProductMap = retailProductService.queryExistProductBySpuIds(storeId, spuIdList);

        List<LabelPrintWaterLineDO> list = new ArrayList<>();
        labelPrintParam.getGoods().forEach(goods -> {
            CashierRetailProduct product = retailProductMap.get(goods.getSpuId());
            LabelPrintWaterLineDO lineDataDO = new LabelPrintWaterLineDO();
            lineDataDO.setOwnerId(storeId);
            lineDataDO.setSpuId(goods.getSpuId());
            if(null != product){
                lineDataDO.setSpuTitle(product.getSpuTitle());
                lineDataDO.setBarcode(product.getBarcode());
                lineDataDO.setCategoryId(Optional.ofNullable(product.getCategory()).map(CashierRetailProduct.Category::getId).orElse(null));
                lineDataDO.setSalePrice(product.getSalePrice());
                lineDataDO.setMemberPrice(product.getMemberPrice());
                lineDataDO.setProducer(product.getProducer());
                lineDataDO.setSpec(product.getSpec());
                lineDataDO.setSaleUnit(product.getSaleUnit());
            }
            lineDataDO.setWaterLine(existsWaterLineDO.getWaterLine());
            lineDataDO.setPrintStatus(goods.getPrintStatus());
            lineDataDO.setDeleted(YesNoEnum.N.getValue());

            list.add(lineDataDO);
        });
        labelPrintWaterLineDAO.batchInsert(list);

        //发送消息到收银机
        sendStoreCashierMessage(storeId, existsWaterLineDO.getWaterLine());

        return true;
    }

    @Override
    public Long getMaxWaterLine(String storeId) {
        CashierWaterLineDO waterLineDO = cashierWaterLineDAO.getByOwnerIdBusinessType(storeId, OwnerTypeEnum.STORE, BusinessTypeEnum.LABEL_PRINT);
        if(null == waterLineDO){
            return null;
        }
        return waterLineDO.getWaterLine();
    }

    /**
     * 查询标签打印数据
     * @param labelPrintParam
     * @return
     */
    @Override
    public CursorPage<LabelPrintWaterLineDTO> cursorWaterLine(CashierLabelPrintParam labelPrintParam, Integer deleted) {
        int pageSize = (null != labelPrintParam.getPageSize()) ? labelPrintParam.getPageSize() : 50;
        CashierWaterLineQuery query = new CashierWaterLineQuery();
        query.setOwnerId(labelPrintParam.getStoreId());
        query.setBusinessType(BusinessTypeEnum.LABEL_PRINT);
        query.setPageSize(pageSize);
        query.setLastId(labelPrintParam.getLastId());
        query.setMinWaterLine(labelPrintParam.getMinWaterLine());
        query.setMaxWaterLine(labelPrintParam.getMaxWaterLine());
        query.setDeleted(deleted);
        List<LabelPrintWaterLineDO> pageInfo = labelPrintWaterLineDAO.cursorWaterLine(query);
        CursorPage<LabelPrintWaterLineDTO> waterLineDTOCursorPage = CursorPage.toPage(pageInfo, pageSize, t->String.valueOf(t.getId()), t-> ConvertUtil.transLabelPrintWaterLineDTO(t));
        return waterLineDTOCursorPage;
    }

    /**
     * 发送消息到门店收银机
     * @param storeId
     * @param maxWaterLine
     * @return
     */
    private boolean sendStoreCashierMessage(String storeId, Long maxWaterLine) {
        try {
            Map<String, Object> contentMap = new HashMap<>();
            contentMap.put("storeId", storeId);
            contentMap.put("waterLine", maxWaterLine);
            RmqxMessage rmqxMessage = RmqxMessage.builder()
                    .type(NoticeType.GOODS_LABEL_PRINT)
                    .message(contentMap)
                    .build();
            return awmRmqxMessageRpcService.pushStoreMqttToCashier(storeId, SceneEnum.RETAIL.name(), rmqxMessage);
        } catch (Exception ex){
            log.error("LabelPrintServiceImpl.sendStoreCashierMessage.error.storeId:{},noticeType:{},maxWaterLine:{}", storeId, NoticeType.GOODS_LABEL_PRINT, maxWaterLine, ex);
            return false;
        }
    }

    private Map<Long, CashierRetailProduct> getRetailProductMap(List<CashierRetailProduct> productList) {
        if(CollectionUtils.isEmpty(productList)){
            return Maps.newHashMap();
        }
        return productList.stream().collect(Collectors.toMap(CashierRetailProduct::getSpuId, Function.identity()));
    }
}
