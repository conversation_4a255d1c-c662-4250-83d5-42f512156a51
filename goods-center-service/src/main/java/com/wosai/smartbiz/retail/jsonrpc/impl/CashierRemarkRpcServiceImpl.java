package com.wosai.smartbiz.retail.jsonrpc.impl;

import com.google.common.collect.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.base.apisix.cashier.CashierBaseParam;
import com.wosai.smartbiz.base.apisix.cashier.CashierUser;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.dto.req.remark.RemarkRpcQueryReq;
import com.wosai.smartbiz.dto.vo.remark.CashierOrderRemarkVO;
import com.wosai.smartbiz.dto.vo.remark.RemarkVO;
import com.wosai.smartbiz.goods.enums.repast.AttributeTypeEnum;
import com.wosai.smartbiz.gds.jsonrpc.RemarkRpcService;
import com.wosai.smartbiz.retail.jsonrpc.CashierRemarkRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023/3/9
 */
@Slf4j
@Component
@AutoJsonRpcServiceImpl
public class CashierRemarkRpcServiceImpl implements CashierRemarkRpcService {

    @Autowired
    private RemarkRpcService remarkRpcService;

    @Override
    public List<CashierOrderRemarkVO> list(CashierApiRequest<CashierBaseParam> request) {
        CashierBaseParam cashierBaseParam = request.getBody();
        CashierUser user = request.getUser();
        if(null == cashierBaseParam || StringUtils.isBlank(cashierBaseParam.getStoreId())){
            throw new ParamException("门店ID不能为空");
        }
        RemarkRpcQueryReq queryReq = new RemarkRpcQueryReq();
        queryReq.setMerchant_id(user.getMerchantId());
        queryReq.setBiz_store_id(cashierBaseParam.getStoreId());
        List<RemarkVO> remarkVOList = remarkRpcService.listByQuery(queryReq);
        if(CollectionUtils.isEmpty(remarkVOList)){
            return Lists.newArrayList();
        }
        return remarkVOList.stream()
                .map(p -> new CashierOrderRemarkVO()
                        .setType(EnumUtils.getEnum(AttributeTypeEnum.class, p.getType().toUpperCase()))
                        .setName(p.getName())
                        .setList(p.getTextList()))
                .collect(Collectors.toList());
    }
}
