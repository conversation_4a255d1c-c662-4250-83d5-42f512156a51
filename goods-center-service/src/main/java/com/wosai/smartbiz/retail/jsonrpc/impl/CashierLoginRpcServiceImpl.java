package com.wosai.smartbiz.retail.jsonrpc.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.base.apisix.cashier.CashierBaseParam;
import com.wosai.smartbiz.base.apisix.cashier.CashierClient;
import com.wosai.smartbiz.base.apisix.cashier.CashierUser;
import com.wosai.smartbiz.base.enums.SceneEnum;
import com.wosai.smartbiz.base.enums.YesNoEnum;
import com.wosai.smartbiz.base.exceptions.BusinessException;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.gds.enums.DeviceTypeEnum;
import com.wosai.smartbiz.gds.service.CashierLicenceService;
import com.wosai.smartbiz.gds.service.MccConfigHelperService;
import com.wosai.smartbiz.goods.domain.SoundCashDO;
import com.wosai.smartbiz.goods.domain.StoreDeviceInfoDO;
import com.wosai.smartbiz.retail.dto.RefreshTokenParam;
import com.wosai.smartbiz.retail.jsonrpc.CashierLoginRpcService;
import com.wosai.smartbiz.retail.util.CashierUtil;
import com.wosai.smartbiz.uc.config.RmqxConfigInitializer;
import com.wosai.smartbiz.uc.manager.*;
import com.wosai.smartbiz.uc.services.LoginV2Service;
import com.wosai.smartbiz.uc.services.TokenV2Service;
import com.wosai.smartbiz.uc.utils.common.DateUtils;
import com.wosai.smartbiz.user.user.enums.AppTypeEnum;
import com.wosai.smartbiz.user.user.enums.UcErrorCodeEnum;
import com.wosai.smartbiz.user.user.login.DeviceParam;
import com.wosai.smartbiz.user.user.login.LoginParam;
import com.wosai.smartbiz.user.user.login.LoginUserParam;
import com.wosai.smartbiz.user.user.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @create 2023/3/9
 */
@Slf4j
@Component
@AutoJsonRpcServiceImpl
public class CashierLoginRpcServiceImpl implements CashierLoginRpcService {
    @Autowired
    private TokenV2Service tokenV2Service;
    @Autowired
    private LoginV2Service loginV2Service;
    @Autowired
    private StoreV2Service storeV2Service;
    @Autowired
    private LoginHistoryService loginHistoryService;
    @Autowired
    private CashierLicenceService cashierLicenceService;
    @Autowired
    private MccConfigHelperService mccConfigHelperService;
    @Autowired
    private StoreDeviceInfoService storeDeviceInfoService;
    @Autowired
    private RmqxConfigInitializer rmqxConfigInitializer;
    @Autowired
    private SoundCashService soundCashService;
    @Autowired
    private UserTerminalService userTerminalService;

    @Override
    public AccessTokenVO refreshToken(CashierApiRequest<RefreshTokenParam> request) {
        RefreshTokenParam tokenParam = request.getBody();
        if(null == tokenParam || StringUtils.isBlank(tokenParam.getToken())){
            throw new ParamException("token不能为空");
        }
        String accessToken = tokenParam.getToken();
        return tokenV2Service.refreshToken(accessToken);
    }

    @Override
    public UserLoginV2VO login(CashierApiRequest<LoginParam> request) {
        LoginParam loginParam = request.getBody();
        CashierClient client = request.getClient();
        if(null == loginParam || StringUtils.isBlank(loginParam.getUserPhone())){
            throw new ParamException("登录手机号不能为空");
        }
        if(loginParam.getUserPhone().length() != 11){
            throw new ParamException("手机号为11位数字");
        }
        if(StringUtils.isBlank(loginParam.getUserPwd())){
            throw new ParamException("登录密码不能为空");
        }
        if(loginParam.getUserPwd().length() < 6 || loginParam.getUserPwd().length() > 20){
            throw new ParamException("登录密码必须为6-20位(必须包含数字和字母)");
        }
        if(StringUtils.isBlank(loginParam.getUserCode())){
            loginParam.setUserCode(loginParam.getUserPhone());
        }
        DeviceParam deviceParam = CashierUtil.getRequestDeviceParam(client);
        if(StringUtils.isBlank(deviceParam.getDeviceFp())){
            throw new ParamException("终端编号不能为空");
        }

        Result<UserLoginV2VO> result = loginV2Service.loginCashier(loginParam, deviceParam);
        if(!result.isSuccess()){
            throw new BusinessException(result.getErrorMsg());
        }
        return result.getData();
    }

    @Override
    public Boolean logout(CashierApiRequest<CashierBaseParam> request) {
        CashierBaseParam cashierBaseParam = request.getBody();
        CashierClient client = request.getClient();
        CashierUser user = request.getUser();
        if(null == cashierBaseParam || StringUtils.isBlank(cashierBaseParam.getStoreId())){
            throw new ParamException("门店ID不能为空");
        }
        SceneEnum deviceScene = CashierUtil.getCashierScene(client.getUserAgent());
        String storeId = cashierBaseParam.getStoreId();
        String merchantId = user.getMerchantId();
        String deviceFp = client.getTerminalSn();
        String accessToken = user.getToken();
        Result<Boolean> logoutResult = loginV2Service.logout(accessToken, merchantId, storeId, deviceFp, deviceScene);
        if(!logoutResult.isSuccess()){
            throw new BusinessException("登出失败");
        }
        return true;
    }

    @Override
    public Boolean sendSms(CashierApiRequest<LoginParam> request) {
        LoginParam loginParam = request.getBody();
        if(null == loginParam || StringUtils.isBlank(loginParam.getUserPhone())){
            throw new ParamException("登录手机号不能为空");
        }
        Result<Boolean> result = loginV2Service.sendLoginSmsCode(request.getBody().getUserPhone());
        if(!result.isSuccess()){
            throw new BusinessException(result.getErrorMsg());
        }
        return result.getData();
    }

    @Override
    public UserLoginV2VO authCodeLogin(CashierApiRequest<LoginParam> request) {
        LoginParam loginParam = request.getBody();
        CashierClient client = request.getClient();
        if(null == loginParam || StringUtils.isBlank(loginParam.getUserPhone())){
            throw new ParamException("登录手机号不能为空");
        }
        if(loginParam.getUserPhone().length() != 11){
            throw new ParamException("手机号为11位数字");
        }
        if(StringUtils.isBlank(loginParam.getAuthCode())){
            throw new ParamException("验证码不能为空");
        }

        DeviceParam deviceParam = CashierUtil.getRequestDeviceParam(client);
        if(StringUtils.isBlank(deviceParam.getDeviceFp())){
            throw new ParamException("终端编号不能为空");
        }
        Result<UserLoginV2VO> result = loginV2Service.loginCashierByAuthCode(loginParam, deviceParam);
        if(!result.isSuccess()){
            throw new BusinessException(result.getErrorMsg());
        }
        return result.getData();
    }

    @Override
    public UserLoginV2VO authStatus(CashierApiRequest<LoginParam> request) {
        LoginParam loginParam = request.getBody();
        if(null == loginParam || StringUtils.isBlank(loginParam.getAuthQrCode())){
            throw new ParamException("授权码信息不能为空");
        }
        Result<UserLoginV2VO> result = loginV2Service.loginAuthStatus(loginParam.getAuthQrCode());
        if(!result.isSuccess()){
            throw new BusinessException(result.getErrorMsg());
        }
        return result.getData();
    }

    @Override
    public List<CashierStoreVO> storeList(CashierApiRequest<CashierBaseParam> request) {
        CashierUser user = request.getUser();
        if(null == user){
            throw new ParamException("用户信息获取失败");
        }
        String ucUserId = request.getUser().getUcUserId();
        if(StringUtils.isBlank(ucUserId)){
            throw new ParamException("用户ID不能为空");
        }
        return storeV2Service.findCashierStoreListByUcUserId(ucUserId, SceneEnum.RETAIL);
    }

    @Override
    public UserLoginV2VO choiceStore(CashierApiRequest<CashierBaseParam> request) {
        CashierBaseParam cashierBaseParam = request.getBody();
        CashierClient client = request.getClient();
        CashierUser user = request.getUser();
        if(null == cashierBaseParam || StringUtils.isBlank(cashierBaseParam.getStoreId())){
            throw new ParamException("请选择门店");
        }
        String storeId = cashierBaseParam.getStoreId();
        String ucUserId = user.getUcUserId();
        String token = user.getToken();

        DeviceParam deviceParam = CashierUtil.getRequestDeviceParam(client);
        if(StringUtils.isBlank(deviceParam.getDeviceFp())){
            throw new ParamException("终端编号不能为空");
        }

        //如果更换了商户，则需要重新跟换登录用户信息
        if(StringUtils.isBlank(token)){
            throw new BusinessException(UcErrorCodeEnum.ACCESS_TOKEN_INVALID.getDesc());
        }

        Result<UserLoginV2VO> result = loginV2Service.changeChoiceStore(token, storeId, ucUserId, deviceParam);
        if(!result.isSuccess()){
            throw new BusinessException(result.getErrorMsg());
        }
        UserLoginV2VO userLoginV2VO = result.getData();

        Date currentTime = DateUtils.getFormatCurrentTime();
        userLoginV2VO.setLoginTime(currentTime.getTime());

        //选择门店后处理逻辑添加
        choiceStoreAfterProcess(storeId, userLoginV2VO, deviceParam, currentTime);

        return userLoginV2VO;
    }

    @Override
    public Boolean unbindStore(CashierApiRequest<CashierBaseParam> request) {
        CashierBaseParam cashierBaseParam = request.getBody();
        CashierClient client = request.getClient();
        if(null == cashierBaseParam || StringUtils.isBlank(cashierBaseParam.getStoreId())){
            throw new ParamException("请选择门店");
        }

        String storeId = cashierBaseParam.getStoreId();
        DeviceParam deviceParam = CashierUtil.getRequestDeviceParam(client);
        if(StringUtils.isBlank(deviceParam.getDeviceFp())){
            throw new ParamException("终端编号不能为空");
        }
        Result<Boolean> result = loginV2Service.unbindChoiceStore(storeId, deviceParam);
        if(!result.isSuccess()){
            throw new BusinessException(result.getErrorMsg());
        }

        //解绑门店设备信息
        unbindStoreAfterProcess(storeId, deviceParam, client);

        return result.getData();
    }

    @Override
    public StoreActiveVO activeStore(CashierApiRequest<CashierBaseParam> request) {
        CashierBaseParam cashierBaseParam = request.getBody();
        CashierClient client = request.getClient();
        CashierUser user = request.getUser();
        if(null == cashierBaseParam || StringUtils.isBlank(cashierBaseParam.getStoreId())){
            throw new ParamException("请选择门店");
        }

        String storeId = cashierBaseParam.getStoreId();

        LoginUserParam loginUserParam = new LoginUserParam();
        loginUserParam.setSqbUserId(user.getUcUserId());
        loginUserParam.setUserName(user.getUserName());
        loginUserParam.setUserPhone(user.getUserPhone());

        SceneEnum scene = CashierUtil.getCashierScene(client.getUserAgent());
        DeviceParam deviceParam = CashierUtil.getRequestDeviceParam(client);
        Result<StoreActiveVO> result = loginV2Service.active(storeId, scene, null, loginUserParam, deviceParam);
        if(!result.isSuccess()){
            throw new BusinessException(result.getErrorMsg());
        }
        CompletableFuture.runAsync(()-> cashierLicenceService.dataEvent4Active(storeId, "直营", false));
        return result.getData();
    }


    /**
     * 选择门店后处理逻辑
     * @param userLoginV2VO
     * @param deviceParam
     * @return
     */
    private boolean choiceStoreAfterProcess(String storeId, UserLoginV2VO userLoginV2VO, DeviceParam deviceParam, Date loginTime) {
        try {
            if(null == userLoginV2VO){
                return false;
            }
            SceneEnum scene = deviceParam.getDeviceScene();
            DeviceTypeEnum deviceType = DeviceTypeEnum.getCashierDeviceTypeByScene(scene);
            boolean needEnabledCashierMode = mccConfigHelperService.checkNeedEnabledCashierMode(storeId);
            if (needEnabledCashierMode) {
                // 启用开关的可用状态
                mccConfigHelperService.cashierLoginEnableCashierMode(storeId, scene);
            }

            if(null != deviceParam && StringUtils.isNotBlank(deviceParam.getDeviceFp())){
                UserLoginHistoryVO userLoginHistoryVO = new UserLoginHistoryVO();
                userLoginHistoryVO.setSqbUserId(userLoginV2VO.getSqbUserId());
                userLoginHistoryVO.setLoginCode(userLoginV2VO.getUserPhone());
                userLoginHistoryVO.setMerchantId(userLoginV2VO.getMerchantId());
                userLoginHistoryVO.setMerchantSn(userLoginV2VO.getMerchantCode());
                userLoginHistoryVO.setStoreId(userLoginV2VO.getStoreId());
                userLoginHistoryVO.setStoreSn(userLoginV2VO.getStoreCode());
                userLoginHistoryVO.setMerchantUserId(userLoginV2VO.getMerchantUserId());
                userLoginHistoryVO.setAppType(AppTypeEnum.CASHIER);
                userLoginHistoryVO.setVersion(deviceParam.getAppVersion());
                userLoginHistoryVO.setTerminalSn(deviceParam.getDeviceFp());
                userLoginHistoryVO.setLoginTime(loginTime);
                userLoginHistoryVO.setIp(deviceParam.getIp());
                userLoginHistoryVO.setUserAgent(deviceParam.getUserAgent());
                loginHistoryService.addLoginHistory(userLoginHistoryVO);
            }

            //记录门店和设备关系数据
            StoreDeviceInfoDO storeDeviceInfoDO = storeDeviceInfoService.getByStoreIdAndTerminalSnAndType(userLoginV2VO.getStoreId(), deviceParam.getDeviceFp(), deviceType);
            String deviceFingerprint = userTerminalService.buildDeviceFp(userLoginV2VO.getStoreId(), scene, deviceParam.getDeviceFp());
            if(null != storeDeviceInfoDO){
                storeDeviceInfoDO.setLastLoginTime(System.currentTimeMillis());
                storeDeviceInfoDO.setVersion(deviceParam.getAppVersion());
                storeDeviceInfoDO.setMqttClientId(rmqxConfigInitializer.getMqttClientId(deviceParam.getDeviceMqttFp(), deviceParam.getDeviceScene()));
                storeDeviceInfoDO.setDeviceFingerprint(deviceFingerprint);
                storeDeviceInfoDO.setOnline(YesNoEnum.Y);
                storeDeviceInfoService.update(storeDeviceInfoDO);
            }else {
                storeDeviceInfoDO = new StoreDeviceInfoDO()
                        .setMerchantId(userLoginV2VO.getMerchantId())
                        .setMerchantSn(userLoginV2VO.getMerchantCode())
                        .setStoreId(userLoginV2VO.getStoreId())
                        .setStoreSn(userLoginV2VO.getStoreCode())
                        .setTerminalId(StringUtils.isNotBlank(deviceParam.getDeviceId()) ? deviceParam.getDeviceId() : deviceParam.getDeviceFp())
                        .setTerminalSn(deviceParam.getDeviceFp())
                        .setMqttClientId(rmqxConfigInitializer.getMqttClientId(deviceParam.getDeviceMqttFp(), deviceParam.getDeviceScene()))
                        .setDeviceFingerprint(deviceFingerprint)
                        .setType(deviceType)
                        .setVersion(deviceParam.getAppVersion())
                        .setBrand(deviceParam.getTerminalBrand())
                        .setModel(deviceParam.getTerminalModel())
                        .setLastLoginTime(System.currentTimeMillis())
                        .setOnline(YesNoEnum.Y);
                storeDeviceInfoService.create(storeDeviceInfoDO);
            }
        } catch (Exception ex) {
            log.error("CashierLoginRpcServiceImpl.choiceStoreAfterProcess error, storeId:{}", userLoginV2VO.getStoreId(), ex);
        }
        return true;
    }

    /**
     * 解绑门店后处理逻辑
     * @param storeId
     * @param deviceParam
     * @return
     */
    private boolean unbindStoreAfterProcess(String storeId, DeviceParam deviceParam, CashierClient client){
        try {
            SceneEnum scene = CashierUtil.getCashierScene(client.getUserAgent());
            DeviceTypeEnum deviceType = DeviceTypeEnum.getCashierDeviceTypeByScene(scene);
            storeDeviceInfoService.delete(storeId, deviceParam.getDeviceFp(), deviceType);
            // 解绑音箱和收银机
            SoundCashDO soundCashDOParam = new SoundCashDO();
            soundCashDOParam.setStoreId(storeId);
            soundCashDOParam.setCashSn(deviceParam.getDeviceFp());
            boolean result = soundCashService.unbind(soundCashDOParam);
            if(result){
                log.info("成功解绑收银机和其关联的音箱 storeId:{}, cashSn:{}", soundCashDOParam.getStoreId(), soundCashDOParam.getCashSn());
            }else{
                log.warn("失败解绑收银机和其关联的音箱 storeId:{}, cashSn:{}", soundCashDOParam.getStoreId(), soundCashDOParam.getCashSn());
            }
        } catch (Exception ex) {
            log.error("StoreCashierController.unbindStoreAfterProcess error, storeId:{}, deviceFp:{}", storeId, deviceParam.getDeviceFp(), ex);
        }
        return true;
    }
}
