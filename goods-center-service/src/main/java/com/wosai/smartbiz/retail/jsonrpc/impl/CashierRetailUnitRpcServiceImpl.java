package com.wosai.smartbiz.retail.jsonrpc.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.smart.goods.unit.IUnitRpcService;
import com.wosai.smart.goods.unit.dto.UnitGroupDTO;
import com.wosai.smart.goods.unit.req.UnitQueryParamRequest;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.base.apisix.cashier.CashierBaseParam;
import com.wosai.smartbiz.gds.dto.CashierRetailUnitDTO;
import com.wosai.smartbiz.retail.jsonrpc.CashierRetailUnitRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Slf4j
@Service
@AutoJsonRpcServiceImpl
public class CashierRetailUnitRpcServiceImpl implements CashierRetailUnitRpcService {

    @Value("${smart.goods.retail.biz-code}")
    private String retailBizCode;

    @Value("${smart.goods.retail.biz-scene}")
    private String retailBizScene;

    @Autowired
    private IUnitRpcService unitRpcService;

    @Override
    public List<CashierRetailUnitDTO> queryAllUnit(CashierApiRequest<CashierBaseParam> request) {
        // 1. 构建基础查询参数
        UnitQueryParamRequest req = new UnitQueryParamRequest();
        req.setStoreId(request.getBody().getStoreId());
        req.setBizScene(retailBizScene);
        req.setBizCode(retailBizCode);
        // 2. 调用RPC服务查询可用单位
        List<UnitGroupDTO> result = unitRpcService.queryStoreAvailableUnit(req);

        // 4. 分离系统单位和自定义单位
        // 4.1 系统单位：ownerId为空的单位组
        List<UnitGroupDTO> systemGroups = result.stream()
                .filter(item -> Objects.equals(item.getOwnerType(), "system"))
                .collect(Collectors.toList());

        // 4.2 自定义单位：ownerId非空的单位组
        List<UnitGroupDTO> customGroups = result.stream()
                .filter(item -> !Objects.equals(item.getOwnerType(), "system"))
                .collect(Collectors.toList());

        // 5. 构建返回结果
        List<CashierRetailUnitDTO> items = new ArrayList<>();

        // 6. 处理系统单位（使用双括号初始化优化对象创建）
        systemGroups.forEach(group ->
                group.getUnitDTOS().forEach(unit ->
                        items.add(new CashierRetailUnitDTO() {{
                            setId(unit.getUnitId());
                            setName(unit.getValue());
                            setUnitSaleType(group.getCode());
                            setConversionRatio(unit.getConversionRatio());
                            setConversionRatioUnit(unit.getConversionRatioUnit());
                            setUnitSourceType(0);
                        }})
                )
        );
        // 7. 处理自定义单位（结构与系统单位处理保持对称）
        customGroups.forEach(group ->
                group.getUnitDTOS().forEach(unit ->
                        items.add(new CashierRetailUnitDTO() {{
                            setId(unit.getUnitId());
                            setName(unit.getValue());
                            setUnitSaleType(group.getCode());
                            if(unit.getConversionRatio()!=null){
                                setConversionRatio(new BigDecimal(unit.getConversionRatio()).setScale(3,BigDecimal.ROUND_HALF_UP).doubleValue());
                            }
                            if(StringUtils.isNotEmpty(unit.getConversionRatioUnit())){
                                setConversionRatioUnit(unit.getConversionRatioUnit());
                            }
                            setUnitSourceType(1);
                        }})
                )
        );
        return items;
    }
}
