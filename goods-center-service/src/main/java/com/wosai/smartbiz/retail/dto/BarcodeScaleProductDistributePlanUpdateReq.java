package com.wosai.smartbiz.retail.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 条码秤商品下发方案创建请求
 */
@Data
public class BarcodeScaleProductDistributePlanUpdateReq {

    /**
     * 方案ID
     */
    @NotNull(message = "方案ID不能为空")
    private Long planId;

    /**
     * 门店ID
     */
    @NotBlank(message = "门店ID不能为空")
    private String storeId;

    /**
     * 方案名称
     */
    @NotBlank(message = "方案名称不能为空")
    @Size(max = 20, message = "方案名称长度不能超过20")
    private String title;

    /**
     * 条码格式
     */
    @Size(max = 18, message = "条码格式长度不能超过18")
    private String barcodeFormat;

    /**
     * 具体商品配置，JSON字符串，前端解析使用
     * 例如：[{"spuId":"","hotKey":""}]
     */
    @NotBlank(message = "商品配置不能为空")
    private String products;

}
