package com.wosai.smartbiz.retail.adapter;

import com.wosai.smart.goods.common.constant.SaleChannelFlag;
import com.wosai.smartbiz.gds.dto.CashierRetailProduct;
import com.wosai.smartbiz.base.utils.VersionUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 历史版本收银机无法处理仅在外卖渠道售卖的商品，此适配器做以下功能：<p>
 * 1、将仅在外卖售卖的商品标记为删除状态
 */
@Service
public class CashierRetailProductSaleChannelAdapter implements ICashierRetailProductAdapter {

    /**
     * 兼容处理商品售卖渠道的最低版本
     */
    @Value("${retail.cashier.product-salechannel-adapter.min-version}")
    private String minVersion;

    @Override
    public int order() {
        return 0;
    }

    @Override
    public boolean isLessThanDestAppVersion(String version) {
        return VersionUtil.isLessThanDestAppVersion(version, minVersion);
    }

    @Override
    public void process(CashierRetailProduct product) {
        Objects.requireNonNull(product);
        // 检测到商品没有设置到店渠道，直接标记为删除
        SaleChannelFlag channelFlag = new SaleChannelFlag(product.getSaleChannels());
        if (!channelFlag.isInStore()) {
            product.setDeleted(true);
        }
    }

}
