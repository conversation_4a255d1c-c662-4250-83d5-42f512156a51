package com.wosai.smartbiz.retail.util;

import com.wosai.smart.goods.enums.ProductTypeEnum;
import com.wosai.smart.goods.enums.SaleTypeEnum;
import com.wosai.smart.goods.enums.SpuSaleStatusEnum;
import com.wosai.smart.standard.api.goods.dto.MediaDTO;
import com.wosai.smart.standard.api.goods.dto.RetailGoodsDTO;
import com.wosai.smart.standard.common.constant.RetailGoodsConstant;
import com.wosai.smartbiz.retail.dto.ProductFilingData;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @create 2023/9/18
 */
public class RetailStandardGoodsConverter {

    /**
     * 标品库售卖类型 => 零售商品售卖类型
     * @param saleType
     * @return
     */
    public static SaleTypeEnum toSmartGoodsSaleType(Integer saleType) {
        RetailGoodsConstant.SaleType ori = Arrays.stream(RetailGoodsConstant.SaleType.values())
                .filter(s -> Objects.equals(s.getCode(), saleType))
                .findFirst()
                .orElse(null);
        return RetailGoodsConstant.SaleType.WEIGHT == ori ? SaleTypeEnum.WEIGHT : SaleTypeEnum.NUMBER;
    }

    /**
     * 标品信息 => 收银机建档数据
     * @param standardGoods
     * @return
     */
    public static ProductFilingData toFilingData(RetailGoodsDTO standardGoods) {
        Objects.requireNonNull(standardGoods);
        ProductFilingData filingData = new ProductFilingData();
        // 基本信息
        filingData.setStandardGoodsId(standardGoods.getId());
        filingData.setSpuTitle(standardGoods.getTitle());
        filingData.setPinyin(standardGoods.getPinyin());
        filingData.setSaleStatus(SpuSaleStatusEnum.LISTING.getCode());
        filingData.setBarcode(standardGoods.getBarcode());
        filingData.setIsStandard(StringUtils.isNotEmpty(standardGoods.getBarcode()));
        filingData.setSaleUnit(standardGoods.getUnit());
        filingData.setProducer(standardGoods.getOriginArea());
        filingData.setSpec(standardGoods.getSpecDesc());
        // 商品主图
        String coverImage = Optional.ofNullable(standardGoods.getMediaDTOS())
                .orElseGet(ArrayList::new)
                .stream()
                .filter(m -> StringUtils.equals(m.getMediaType(), RetailGoodsConstant.MediaType.PICTURE.getCode())
                        && StringUtils.equals(m.getLocation(), RetailGoodsConstant.MediaLocation.MAIN.getCode()))
                .findFirst()
                .map(MediaDTO::getUrl)
                .orElse(null);
        filingData.setCoverImage(coverImage);
        // 关键类型
        filingData.setProductType(ProductTypeEnum.COMMON.getCode());
        filingData.setSaleType(toSmartGoodsSaleType(standardGoods.getSaleType()).getCode());
        return filingData;
    }

}
