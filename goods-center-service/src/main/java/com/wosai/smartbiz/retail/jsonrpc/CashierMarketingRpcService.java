package com.wosai.smartbiz.retail.jsonrpc;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.base.apisix.cashier.CashierBaseParam;
import com.wosai.smartbiz.retail.dto.StoreMemberActivityStatusRes;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;

@JsonRpcService(value = "rpc/retail/cashier/marketing")
@Validated
public interface CashierMarketingRpcService {

    /**
     * 查询门店是否开通了会员价活动
     */
    StoreMemberActivityStatusRes queryStoreMemberActivityStatus(@Valid CashierApiRequest<CashierBaseParam> request);

}
