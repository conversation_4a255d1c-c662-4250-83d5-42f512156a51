package com.wosai.smartbiz.retail.jsonrpc;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.retail.dto.CashierAiActiveCheckDTO;
import com.wosai.smartbiz.retail.dto.CashierAiActiveDTO;
import com.wosai.smartbiz.retail.dto.LicenceActiveParam;
import org.springframework.validation.annotation.Validated;

/**
 * 收银机AI激活接口
 * <AUTHOR>
 * @create 2023/3/9
 */
@JsonRpcService(value = "rpc/cashier/ai")
@Validated
public interface CashierAiRpcService {

    /**
     * 查询收银机AI情况（包含设备和门店绑定数据） （由由AI)
     * @param request
     * @return
     */
    CashierAiActiveDTO activeDetail(CashierApiRequest<LicenceActiveParam> request);

    /**
     * 查询设备收银机AI激活情况（只包含设备绑定数据） (由由AI + 支付宝AI)
     * @param request
     * @return
     */
    CashierAiActiveDTO activeDetailV2(CashierApiRequest<LicenceActiveParam> request);

    /**
     * 查询设备收银机AI激活情况（只包含设备绑定数据） (由由AI + 支付宝AI + 傲视AI)
     * @param request
     * @return
     */
    CashierAiActiveDTO activeDetailV3(CashierApiRequest<LicenceActiveParam> request);

    /**
     * 查询设备收银机AI激活情况（只包含设备绑定数据） (由由AI + 支付宝AI + 傲视AI + 商米AI)
     * @param request
     * @return
     */
    CashierAiActiveDTO activeDetailV4(CashierApiRequest<LicenceActiveParam> request);

    /**
     * 获取收银机激活Licence信息  (由由AI)
     * @param request
     * @return
     */
    CashierAiActiveDTO activeLicence(CashierApiRequest<LicenceActiveParam> request);

    /**
     * 获取收银机激活Licence信息(V2)  (由由AI + 支付宝AI)
     * @param request
     * @return
     */
    CashierAiActiveDTO activeLicenceV2(CashierApiRequest<LicenceActiveParam> request);

    /**
     * 获取收银机激活Licence信息(V3) (由由AI + 支付宝AI + 傲视AI)
     * @param request
     * @return
     */
    CashierAiActiveDTO activeLicenceV3(CashierApiRequest<LicenceActiveParam> request);

    /**
     * 获取收银机激活Licence信息(V4)  (由由AI + 支付宝AI + 傲视AI + 商米AI)
     * @param request
     * @return
     */
    CashierAiActiveDTO activeLicenceV4(CashierApiRequest<LicenceActiveParam> request);

    /**
     * 收银机AI产品激活检查
     * @param request
     * @return
     */
    CashierAiActiveCheckDTO activeCheck(CashierApiRequest<LicenceActiveParam> request);

    /**
     * 收银机AI产品激活状态同步
     * @param request
     * @return
     */
    CashierAiActiveDTO.AiInfo active(CashierApiRequest<LicenceActiveParam> request);
}
