package com.wosai.smartbiz.retail.adapter;

import com.wosai.smart.goods.common.constant.UnitConstant;
import com.wosai.smart.goods.enums.SaleTypeEnum;
import com.wosai.smartbiz.base.utils.VersionUtil;
import com.wosai.smartbiz.gds.dto.CashierRetailProduct;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
@Service
public class CashierRetailProductUnitAdapter implements ICashierRetailProductAdapter {

    private static final Map<String, BigDecimal> SPECIAL_UNIT_RATIO = new HashMap<>();
    public static final Set<String> UNIT_SET = new HashSet<>();


    static {
        // 初始化特殊单位转换比例
        SPECIAL_UNIT_RATIO.put("公斤", BigDecimal.ONE);          // 1公斤 = 1kg
        SPECIAL_UNIT_RATIO.put("两", new BigDecimal("0.05"));   // 1两 = 0.05kg
        SPECIAL_UNIT_RATIO.put("克", new BigDecimal("0.001"));  // 1克 = 0.001kg

        // 初始化合法单位集合
        UNIT_SET.add("kg");

        UNIT_SET.add("斤");
    }


    @Override
    public int order() {
        return 0;
    }

    @Override
    public boolean isLessThanDestAppVersion(String version) {
        return VersionUtil.isLessThanDestAppVersion(version, "2.11.0");
    }

    @Override
    public void process(CashierRetailProduct product) {
        Objects.requireNonNull(product);
        CashierRetailProduct.UnitInfo unitInfo = product.getUnitInfo();
        if (product.getSaleType() == SaleTypeEnum.WEIGHT.getCode()) {
            String currentUnit = product.getSaleUnit();
            if (!UNIT_SET.contains(currentUnit)) {
                BigDecimal ratio;

                // 处理特殊单位转换
                if (SPECIAL_UNIT_RATIO.containsKey(product.getSaleUnit())) {
                    ratio = SPECIAL_UNIT_RATIO.get(product.getSaleUnit());
                } else {
                    // 普通单位使用unitInfo中的比例
                    ratio = BigDecimal.valueOf(unitInfo.getConversionRatio());
                }

                // 计算新价格并四舍五入
                BigDecimal newPrice = new BigDecimal(product.getSalePrice()).divide(ratio, 0, RoundingMode.HALF_UP);
                // 应用价格范围限制
                newPrice = newPrice.max(BigDecimal.valueOf(0.1))
                        .min(BigDecimal.valueOf(99999999));
                // 设置新的价格和单位
                product.setSalePrice(newPrice.longValue());
                product.setSaleUnit(UnitConstant.DefaultUnitConstant.DEFAULT_WEIGHT_UNIT);
            }
        }
    }

}
