package com.wosai.smartbiz.kafka;

import com.alibaba.fastjson.JSON;
import com.wosai.databus.LogEntry;
import com.wosai.databus.avro.AvroEventEntry;
import com.wosai.databus.event.AbstractEvent;
import com.wosai.databus.event.awesome.message.StoreCashierBindRelationEvent;
import com.wosai.databus.event.awesome.message.StoreCashierModeChangeEvent;
import com.wosai.databus.event.bank.AccountTransferEvent;
import com.wosai.databus.event.bank.Pair;
import com.wosai.databus.event.bean.RefundOrderItem;
import com.wosai.databus.event.crmdatabus.crmCore.customerRelation.CustomerRelationEvent;
import com.wosai.databus.event.crmdatabus.crmCore.customerRelation.events.RelationAddEvent;
import com.wosai.databus.event.crmdatabus.crmCore.customerRelation.events.RelationChangeEvent;
import com.wosai.databus.event.crmdatabus.crmCore.org.events.OrgDaoEvent;
import com.wosai.databus.event.crmdatabus.crmCore.user.events.UserChangeMultiOrgEvent;
import com.wosai.databus.event.crmdatabus.crmCore.user.events.UserChangeOrgEvent;
import com.wosai.databus.event.crmdatabus.crmCore.user.events.UserDaoEvent;
import com.wosai.databus.event.device.store.DeviceStoreRelationOperateEvent;
import com.wosai.databus.event.oms.DingAuditSyncEvent;
import com.wosai.databus.event.order.basic.FTOrderBasicEvent;
import com.wosai.databus.event.order.basic.FTOrderBasicRefundEvent;
import com.wosai.databus.jackson.EventAwareJackson2PersistenceHelper;
import com.wosai.sales.core.service.OrganizationService;
import com.wosai.sales.core.service.UserService;
import com.wosai.sales.terminal.order.domain.entity.OrderItem;
import com.wosai.sales.terminal.order.enums.TerminalOrderItemRefundStatusEnum;
import com.wosai.sales.terminal.order.service.IOrderItemService;
import com.wosai.smartbiz.Constants;
import com.wosai.smartbiz.apollo.ApolloConfigHelper;
import com.wosai.smartbiz.apollo.pojo.CashierChargeVirtualItem;
import com.wosai.smartbiz.base.enums.YesNoEnum;
import com.wosai.smartbiz.config.DependencyProperties;
import com.wosai.smartbiz.gds.dto.licence.LicenceSaleInfoDTO;
import com.wosai.smartbiz.gds.enums.*;
import com.wosai.smartbiz.gds.request.LicenceDistributionRequest;
import com.wosai.smartbiz.gds.service.CashierLicenceService;
import com.wosai.smartbiz.gds.service.ProductLicenceService;
import com.wosai.smartbiz.goods.domain.LicenceOrganizationDO;
import com.wosai.smartbiz.goods.domain.LicenceUserDO;
import com.wosai.smartbiz.goods.domain.SoundCashDO;
import com.wosai.smartbiz.kafka.enums.TerminalOrderRefundStatusEnum;
import com.wosai.smartbiz.uc.manager.MerchantV2Service;
import com.wosai.smartbiz.uc.manager.SoundCashService;
import com.wosai.smartbiz.uc.manager.StoreDeviceWhitelistService;
import com.wosai.smartbiz.uc.manager.UserV2Service;
import com.wosai.smartbiz.user.user.vo.MerchantV2VO;
import com.wosai.smartbiz.utils.LicenceUtil;
import com.wosai.upay.user.kafka.avro.ChangeCellphoneMsgAvro;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.Schema;
import org.apache.avro.generic.GenericRecord;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.TopicPartition;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.listener.ConsumerSeekAware;
import org.springframework.stereotype.Component;

import java.nio.ByteBuffer;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * @author: redhat
 */
@Component
@Slf4j
public class KafkaConsumer implements ConsumerSeekAware {
    @Value("${crm.customer-relation.store-origin-config-code}")
    private String storeOriginConfigCode;
    @Autowired
    private UserV2Service userV2Service;
    @Autowired
    private MerchantV2Service merchantV2Service;
    @Autowired
    protected DependencyProperties dependencyProperties;
    @Autowired
    private OrganizationService organizationService;

    @Autowired
    private StoreDeviceWhitelistService storeDeviceWhitelistService;
    @Autowired
    private ProductLicenceService productLicenceService;
    @Autowired
    private CashierLicenceService cashierLicenceService;
    @Autowired
    private ApolloConfigHelper apolloConfigHelper;
    @Autowired
    private UserService salesUserService;
    @Autowired
    private IOrderItemService salesOrderItemService;

    @Autowired
    private SoundCashService soundCashService;

    /**
     * 支付订单审核成功状态值
     */
    public final static int FT_ORDER_PAY_AUDIT_SUCCESS_STATUS = 2;

    private ExecutorService pool = Executors.newFixedThreadPool(30);
    public static ThreadLocal<ConsumerSeekAware.ConsumerSeekCallback> seekCallbackThreadLocal = new ThreadLocal<>();
    private EventAwareJackson2PersistenceHelper persistenceHelper = new EventAwareJackson2PersistenceHelper();

    @Override
    public void registerSeekCallback(ConsumerSeekCallback callback) {
    }

    @Override
    public void onPartitionsAssigned(Map<TopicPartition, Long> assignments, ConsumerSeekCallback callback) {
        seekCallbackThreadLocal.set(callback);
    }

    @Override
    public void onIdleContainer(Map<TopicPartition, Long> assignments, ConsumerSeekCallback callback) {
    }

    /**
     * kafka消息处理
     *
     * @param record
     * @return
     */
    private AbstractEvent consume(ConsumerRecord<String, GenericRecord> record) {
        if (!Objects.equals(dependencyProperties.getKafkaConsumerEnable(), Boolean.TRUE)) {
            return null;
        }
        GenericRecord datum = record.value();
        ByteBuffer buffer = (ByteBuffer) datum.get(LogEntry.EVENT);
        AbstractEvent event = (AbstractEvent) persistenceHelper.fromJsonBytes(buffer.array(), AbstractEvent.class);
        event.setSeq((Long) datum.get(LogEntry.SEQ));
        event.setTimestamp((Long) datum.get(LogEntry.TIMESTAMP));
        return event;
    }

    private ChangeCellphoneMsgAvro changeCellphoneConsume(ConsumerRecord<String, GenericRecord> record) {
        if (!Objects.equals(dependencyProperties.getKafkaConsumerEnable(), Boolean.TRUE)) {
            return null;
        }
        GenericRecord datum = record.value();
        if (null == datum) {
            return null;
        }
        ChangeCellphoneMsgAvro avro = new ChangeCellphoneMsgAvro();
        Schema schema = datum.getSchema();
        if (null != schema.getField("merchant_user_id")) {
            CharSequence merchantUserId = (CharSequence) datum.get("merchant_user_id");
            if (null != merchantUserId) {
                avro.setMerchantUserId(merchantUserId);
            }
        }
        if (null != schema.getField("merchant_id")) {
            CharSequence merchantId = (CharSequence) datum.get("merchant_id");
            if (null != merchantId) {
                avro.setMerchantId(merchantId);
            }
        }
        if (null != schema.getField("old_uc_user_id")) {
            CharSequence oldUcUserId = (CharSequence) datum.get("old_uc_user_id");
            if (null != oldUcUserId) {
                avro.setOldUcUserId(oldUcUserId);
            }
        }
        if (null != schema.getField("new_uc_user_id")) {
            CharSequence newUcUserId = (CharSequence) datum.get("new_uc_user_id");
            if (null != newUcUserId) {
                avro.setNewUcUserId(newUcUserId);
            }
        }
        return avro;
    }

    /**
     * 用户信息修改事件
     *
     * @param avro
     */
    @KafkaListener(topics = "${spring.kafka.listener.upay-core-change-uc-user-topic}", containerFactory = "kafkaContainerFactory")
    public void upayCoreUcUserChange(ChangeCellphoneMsgAvro avro) {
        try {
            if (Objects.isNull(avro)) {
                log.warn("upayCoreUcUserChange error avro is null");
                return;
            }
            if (Objects.isNull(avro.getMerchantUserId())) {
                log.warn("upayCoreUcUserChange error avro.merchantUserId is null");
                return;
            }
            log.info("upayCoreUcUserChange.avro===>merchantUserId:{},merchantId:{},oldUcUserId:{},newUcUserId:{}", avro.getMerchantUserId(), avro.getMerchantId(), avro.getOldUcUserId(), avro.getNewUcUserId());
            userV2Service.expireLoginUserCacheByMerchantUserId(avro.getMerchantUserId().toString());
        } catch (Exception e) {
            log.error("consume upay-core-change-uc-user-topic kafka failed!", e);
        }
    }

    /*@KafkaListener(topics = "${spring.kafka.listener.upay-core-change-uc-user-topic}", containerFactory = "aliyunKafkaContainerFactory")
    public void upayCoreUcUserChange2(ConsumerRecord<String, GenericRecord> record) {
        ChangeCellphoneMsgAvro avro = null;
        try {
            avro = changeCellphoneConsume(record);
            if (null == avro) {
                return;
            }
            log.info("upayCoreUcUserChange.avro===>merchantUserId:{},merchantId:{},oldUcUserId:{},newUcUserId:{}", avro.getMerchantUserId(), avro.getMerchantId(), avro.getOldUcUserId(), avro.getNewUcUserId());

            userV2Service.expireLoginUserCacheByMerchantUserId(avro.getMerchantUserId().toString());
        } catch (Exception e) {
            log.error("consume upay-core-change-uc-user-topic kafka failed! avro: {}", JSON.toJSONString(avro), e);
        }
    }*/

    /**
     * 收银机门店事件
     *
     * @param record
     */
    @KafkaListener(topics = "events_MK_awm-store-cashier-change", containerFactory = "aliyunKafkaContainerFactory")
    public void storeCashierBindChange(ConsumerRecord<String, GenericRecord> record) {
        AbstractEvent event = null;
        try {
            event = consume(record);
            if (null == event || !(event instanceof AbstractEvent)) {
                return;
            }
            log.info("storeCashierBindChange.event===>{}", JSON.toJSONString(event));
            if (Objects.equals(event.getEventType(), StoreCashierBindRelationEvent.EVENT_TYPE_CASHIER_BIND_RELATION)) {
                StoreCashierBindRelationEvent storeCashierBindRelationEvent = (StoreCashierBindRelationEvent) event;
            } else if (Objects.equals(event.getEventType(), StoreCashierModeChangeEvent.EVENT_TYPE_CASHIER_MODE_CHANGE)) {
                StoreCashierModeChangeEvent modeChangeEvent = (StoreCashierModeChangeEvent) event;
            }
        } catch (Exception e) {
            log.error("consume kafka failed! event: {}", JSON.toJSONString(event), e);
        }
    }


    /**
     * 音箱和门店解绑事件
     *
     * @param avroEventEntry
     */
    @KafkaListener(topics = "${spring.kafka.listener.iot-TAP-device-bind-topic}", containerFactory = "aliyunKafkaConsumerFactory2")
    public void storeSoundUnbindConsumer(AvroEventEntry avroEventEntry) {
        DeviceStoreRelationOperateEvent event = null;
        try {
            EventAwareJackson2PersistenceHelper persistenceHelper = new EventAwareJackson2PersistenceHelper();
            event = (DeviceStoreRelationOperateEvent) persistenceHelper.fromJsonBytes(avroEventEntry.getEvent().array(), DeviceStoreRelationOperateEvent.class);
            if (event.getOperateType() == DeviceStoreRelationOperateEvent.OPERATE_TYPE_UNBIND
                    && event.getDeviceType() == DcDeviceTypeEnum.CASH_BOX.getType()) {
                log.info("音箱和门店解绑消息 deviceStoreRelationOperateEvent:{}", JSON.toJSONString(event));
                // 当门店和音箱解绑时，同时解绑音箱和收银机
                SoundCashDO soundCashDOParam = new SoundCashDO();
                soundCashDOParam.setStoreId(event.getStoreId());
                soundCashDOParam.setSoundSn(event.getDeviceSn());
                boolean result = soundCashService.unbind(soundCashDOParam);
                if(result){
                    log.info("成功解绑音箱和其关联的收银机 storeId:{}, soundSn:{}", soundCashDOParam.getStoreId(), soundCashDOParam.getSoundSn());
                }else{
                    log.warn("失败解绑音箱和其关联的收银机 storeId:{}, soundSn:{}", soundCashDOParam.getStoreId(), soundCashDOParam.getSoundSn());
                }
            }
        } catch (Exception e) {
            log.error("消费音箱和门店解绑消息处理异常, deviceStoreRelationOperateEvent:{}，error:", JSON.toJSONString(event), e);
        }
    }


    /**
     * 账户资产转移(异名换卡)
     *
     * @param record
     */
    @KafkaListener(topics = "${spring.kafka.listener.account-transfer-topic}", containerFactory = "kafkaContainerFactory")
    public void accountTransfer(ConsumerRecord<String, GenericRecord> record) {
        AbstractEvent event = null;
        try {
            event = consume(record);
            if (null == event || !(event instanceof AbstractEvent)) {
                return;
            }
            log.info("accountTransfer.event===>{}", JSON.toJSONString(event));
            if (Objects.equals(event.getEventType(), AccountTransferEvent.OBJECT_TYPE_ACCOUNT_TRANSFER)) {
                AccountTransferEvent accountTransferEvent = (AccountTransferEvent) event;
                accountTransferProcess(accountTransferEvent);
            }
        } catch (Exception e) {
            log.error("consume kafka failed! event: {}", JSON.toJSONString(event), e);
        }
    }


    /**
     * 直营商户采购消息接入
     *
     * @param record
     */
    @KafkaListener(topics = "${spring.kafka.listener.ft-order-topic}", containerFactory = "kafkaContainerFactory")
    public void ftOrderConsumer(ConsumerRecord<String, GenericRecord> record) {
        AbstractEvent event = null;
        try {
            event = consume(record);
            if (null == event || !(event instanceof AbstractEvent)) {
                return;
            }
            log.info("ftOrderConsumer.event===>{}", JSON.toJSONString(event));
            if (event instanceof FTOrderBasicEvent && StringUtils.equals("saas", ((FTOrderBasicEvent) event).getBizOwner())) {
                log.info("ftOrderConsumer.saas===>{}", JSON.toJSONString(event));
                // saas来源的续费、退款订单都拦截掉
                return;
            }
            if (Objects.equals(event.getEventType(), FTOrderBasicEvent.EVENT_TYPE_REFUND)) {
                FTOrderBasicRefundEvent refundEvent = (FTOrderBasicRefundEvent) event;
                if (Objects.equals(refundEvent.getRefundStatus(), TerminalOrderRefundStatusEnum.SUCCESS.getStatus())
                        && CollectionUtils.isNotEmpty(refundEvent.getRefundOrderItems())) {
                    pool.submit(new Runnable() {
                        @Override
                        public void run() {
                            directMerchantRefundLicence(refundEvent);
                        }
                    });
                }
            }
        } catch (Exception e) {
            log.error("consume kafka failed! event: {}", JSON.toJSONString(event), e);
        }
    }


    /**
     * 客户关系事件-修改白名单机构信息：变更激活白名单表store_device_whitelist中的organization_name和organization_code
     *
     * @param record
     */
    @KafkaListener(topics = "${spring.kafka.listener.customer-relation-topic}", containerFactory = "aliyunKafkaContainerFactory")
    public void customerRelationConsumer(ConsumerRecord<String, GenericRecord> record) {
        AbstractEvent event = null;
        try {
            event = consume(record);
            if (event == null) {
                return;
            }
            String storeId = null;
            String sellerId = null;
            if (Objects.equals(event.getEventType(), CustomerRelationEvent.EVENT_TYPE_RELATION_ADD)) {
                RelationAddEvent customerRelationEvent = (RelationAddEvent) event;
                //过滤掉无效的消息
                if (customerRelationEvent != null
                        && StringUtils.isNotBlank(customerRelationEvent.getConfigCode())
                        && Objects.equals(storeOriginConfigCode, customerRelationEvent.getConfigCode())) {
                    storeId = customerRelationEvent.getCustomerId();
                    sellerId = customerRelationEvent.getSellerId();
                }
            } else if (Objects.equals(event.getEventType(), CustomerRelationEvent.EVENT_TYPE_RELATION_CHANGE)) {
                RelationChangeEvent customerRelationEvent = (RelationChangeEvent) event;
                //过滤掉无效的消息
                if (customerRelationEvent != null
                        && StringUtils.isNotBlank(customerRelationEvent.getConfigCode())
                        && Objects.equals(storeOriginConfigCode, customerRelationEvent.getConfigCode())) {
                    storeId = customerRelationEvent.getCustomerId();
                    sellerId = customerRelationEvent.getAfterSellerId();
                }
            }

            if (StringUtils.isNotBlank(storeId) && StringUtils.isNotBlank(sellerId)) {
                String finalSellerId = sellerId;
                String finalStoreId = storeId;
                pool.submit(() -> updateStoreCustomerRelationChange(finalStoreId, finalSellerId));
            }
        } catch (Exception e) {
            log.error("consume kafka failed! event: {}", JSON.toJSONString(event), e);
        }
    }


    /**
     * 组织信息变更通知：根据OrganizationId维护licence_organization表
     *
     * @param record
     */
    @KafkaListener(topics = "${spring.kafka.listener.organization-change-topic}", containerFactory = "aliyunKafkaContainerFactory")
    public void organizationChangeConsumer(ConsumerRecord<String, GenericRecord> record) {
        AbstractEvent event = null;
        try {
            event = consume(record);
            if (null == event) {
                return;
            }
            log.info("organizationChangeConsumer.event===>{}", JSON.toJSONString(event));
            if (Objects.equals(event.getEventType(), OrgDaoEvent.EVENT_TYPE_ORG_DAO_UPDATE)) {
                OrgDaoEvent orgDaoEvent = (OrgDaoEvent) event;
                if (StringUtils.isBlank(orgDaoEvent.getOrganizationId())) {
                    return;
                }
                Map<String, Object> organizationMap = organizationService.getSimpleOrganization(orgDaoEvent.getOrganizationId());
                String id = MapUtils.getString(organizationMap, "id", null);
                Integer level = MapUtils.getInteger(organizationMap, "level", null);
                String codePath = MapUtils.getString(organizationMap, "path", null);
                String namePath = MapUtils.getString(organizationMap, "name_path", null);
                String code = MapUtils.getString(organizationMap, "code", null);
                String name = MapUtils.getString(organizationMap, "name", null);
                String parentId = MapUtils.getString(organizationMap, "parent", null);

                LicenceOrganizationDO organizationDO = new LicenceOrganizationDO();
                organizationDO.setId(id);
                organizationDO.setCode(code);
                organizationDO.setName(name);
                organizationDO.setCodePath(codePath);
                organizationDO.setNamePath(namePath);
                organizationDO.setLevel(level);
                organizationDO.setParentId(parentId);

                cashierLicenceService.updateOrganizationInfo(organizationDO);
            }
        } catch (Exception e) {
            log.error("consume kafka failed! event: {}", JSON.toJSONString(event), e);
        }
    }

    /**
     * CRM用户信息变更通知
     *
     * @param record
     */
    @KafkaListener(topics = "${spring.kafka.listener.crm-user-topic}", containerFactory = "aliyunKafkaContainerFactory")
    public void crmUserConsumer(ConsumerRecord<String, GenericRecord> record) {
        AbstractEvent event = null;
        try {
            event = consume(record);
            if (null == event) {
                return;
            }
            log.info("crmUserConsumer.event===>{}", JSON.toJSONString(event));

            if (Objects.equals(event.getEventType(), UserDaoEvent.EVENT_TYPE_USER_CHANGE)) {
                UserDaoEvent userDaoEvent = (UserDaoEvent) event;
                if (StringUtils.isBlank(userDaoEvent.getUserId())) {
                    return;
                }
                Map<String, Object> crmUserMap = salesUserService.getUserWithOrganization(userDaoEvent.getUserId());
                String id = MapUtils.getString(crmUserMap, "id", null);
                String code = MapUtils.getString(crmUserMap, "code", null);
                String cellphone = MapUtils.getString(crmUserMap, "cellphone", null);
                String linkman = MapUtils.getString(crmUserMap, "linkman", null);
                Integer status = MapUtils.getInteger(crmUserMap, "status", null);

                LicenceUserDO licenceUserDO = new LicenceUserDO();
                licenceUserDO.setUserId(id);
                licenceUserDO.setUserCode(code);
                licenceUserDO.setUserName(linkman);
                licenceUserDO.setCellphone(cellphone);
                licenceUserDO.setStatus(status);
                //更新用户信息：根据userId维护licence_user表
                cashierLicenceService.updateUser(licenceUserDO);
                //更新用户许可证信息，如果用户过期的话，需要将用户名下的许可证做迁移
                cashierLicenceService.transferInvalidUserLicence(crmUserMap);
            } else if (Objects.equals(event.getEventType(), UserChangeOrgEvent.EVENT_TYPE_USER_CHANGE_ORG)
                    || Objects.equals(event.getEventType(), UserChangeMultiOrgEvent.EVENT_TYPE_USER_CHANGE_MULTI_ORG)) {
                String userId = null;
                String sourceOrgId = null;
                String targetOrgId = null;
                if (Objects.equals(event.getEventType(), UserChangeOrgEvent.EVENT_TYPE_USER_CHANGE_ORG)) {
                    UserChangeOrgEvent userChangeOrgEvent = (UserChangeOrgEvent) event;

                    userId = userChangeOrgEvent.getId();
                    sourceOrgId = userChangeOrgEvent.getSourceOrgId();
                    targetOrgId = userChangeOrgEvent.getTargetOrgId();
                } else if (Objects.equals(event.getEventType(), UserChangeMultiOrgEvent.EVENT_TYPE_USER_CHANGE_MULTI_ORG)) {
                    UserChangeMultiOrgEvent userChangeMultiOrgEvent = (UserChangeMultiOrgEvent) event;

                    userId = userChangeMultiOrgEvent.getId();
                    sourceOrgId = userChangeMultiOrgEvent.getSourceMultiOrgId();
                    targetOrgId = userChangeMultiOrgEvent.getTargetMultiOrgId();
                }
                if (StringUtils.isBlank(userId) || StringUtils.isBlank(sourceOrgId) || StringUtils.isBlank(targetOrgId)) {
                    return;
                }
                Map<String, Object> sourceOrgMap = organizationService.getSimpleOrganization(sourceOrgId);
                Integer sourceLevel = MapUtils.getInteger(sourceOrgMap, "level", null);
                String sourceCodePath = MapUtils.getString(sourceOrgMap, "path", null);

                Map<String, Object> targetOrgMap = organizationService.getSimpleOrganization(targetOrgId);
                Integer targetLevel = MapUtils.getInteger(targetOrgMap, "level", null);
                String targetCodePath = MapUtils.getString(targetOrgMap, "path", null);

                boolean needMoveLicence = needMoveLicence(userId, sourceLevel, sourceCodePath, targetLevel, targetCodePath);
                if (needMoveLicence) {
                    log.info("crmUserConsumer process userId:{},sourceOrgId:{},sourceLevel:{},sourceCodePath:{},targetOrgId:{},targetLevel:{},targetCodePath:{}", userId, sourceOrgId, sourceLevel, sourceCodePath, targetOrgId, targetLevel, targetCodePath);
                    cashierLicenceService.updateOrganizationIdByUserId(userId, sourceOrgId, targetOrgId);
                }
            }
        } catch (Exception e) {
            log.error("consume kafka failed! event: {}", JSON.toJSONString(event), e);
        }
    }

    /**
     * oms叮叮审批数据同步（服务商许可证采购审批）
     *
     * @param record
     */
    @KafkaListener(topics = "${spring.kafka.listener.oms-ITSYS-ding-audit-sync-topic}", containerFactory = "kafkaContainerFactory")
    public void omsDingAuditSync(ConsumerRecord<String, GenericRecord> record) {
        AbstractEvent event = null;
        try {
            event = consume(record);
            if (null == event || !(event instanceof AbstractEvent)) {
                return;
            }
            log.info("omsDingAuditSync.event===>{}", JSON.toJSONString(event));
            DingAuditSyncEvent dingAuditSyncEvent = (DingAuditSyncEvent) event;

            String saleSource = dingAuditSyncEvent.getSaleSource();
            if (StringUtils.isBlank(saleSource)) {
                return;
            }
            //服务商采购逻辑处理
            if (saleSource.startsWith("crm_")) {
                omsDingAuditSyncProcess(dingAuditSyncEvent);
            }
            //直营商户采购逻辑处理
            if (saleSource.startsWith("zd_")) {
                directMerchantPayLicence4Oms(dingAuditSyncEvent);
            }
        } catch (Exception e) {
            log.error("consume kafka failed! event: {}", JSON.toJSONString(event), e);
        }
    }

    /**
     * 服务商采购审批消息处理
     *
     * @param dingAuditSyncEvent
     */
    public void omsDingAuditSyncProcess(DingAuditSyncEvent dingAuditSyncEvent) {
        try {
            if (StringUtils.isBlank(dingAuditSyncEvent.getUserId())) {
                return;
            }
            if (StringUtils.isBlank(dingAuditSyncEvent.getOrganizationCode())) {
                return;
            }
            Map<String, Object> crmUserMap = salesUserService.getSimpleUser(dingAuditSyncEvent.getUserId());
            if (MapUtils.isEmpty(crmUserMap)) {
                return;
            }
            Map<String, Object> organizationMap = organizationService.getSimpleOrganizationByCode(dingAuditSyncEvent.getOrganizationCode());
            if (MapUtils.isEmpty(organizationMap)) {
                return;
            }
            List<DingAuditSyncEvent.DingAuditSyncGoodsDetail> goodsDetails = dingAuditSyncEvent.getGoodsDetails();
            if (CollectionUtils.isEmpty(goodsDetails)) {
                return;
            }

            String userId = MapUtils.getString(crmUserMap, "id", null);
            String organizationId = MapUtils.getString(organizationMap, "id", null);

            //获取apollo上配置的服务商收银机商品数据
            List<CashierChargeVirtualItem> agentCashierVirtualItemList = apolloConfigHelper.getAgentCashierChargeVirtualItemsV4();
            LicenceDistributionRequest request = buildAgentDistributionDataRequest(organizationId, userId, agentCashierVirtualItemList, goodsDetails);
            if (null == request || CollectionUtils.isEmpty(request.getDistributionData())) {
                return;
            }
            request.setOperationTime(dingAuditSyncEvent.getBuyTime());
            request.setBizOrderId(dingAuditSyncEvent.getBizOrderId());
            request.setSaleSource(dingAuditSyncEvent.getSaleSource());

            cashierLicenceService.agentPayDistribution(request);
        } catch (Exception ex) {
            log.error("omsDingAuditSyncProcess error dingAuditSyncEvent: {}", JSON.toJSONString(dingAuditSyncEvent), ex);
        }
    }

    /**
     * 直营商户许可证购买处理(基于OMS消息)
     *
     * @param dingAuditSyncEvent
     */
    public void directMerchantPayLicence4Oms(DingAuditSyncEvent dingAuditSyncEvent) {
        try {
            String merchantSn = dingAuditSyncEvent.getMerchantSn();
            if (StringUtils.isBlank(merchantSn)) {
                return;
            }
            MerchantV2VO merchantV2VO = merchantV2Service.getMerchantByMerchantSn(merchantSn);
            if(Objects.isNull(merchantV2VO)){
                return;
            }
            List<DingAuditSyncEvent.DingAuditSyncGoodsDetail> goodsDetails = dingAuditSyncEvent.getGoodsDetails();
            if (CollectionUtils.isEmpty(goodsDetails)) {
                return;
            }

            List<CashierChargeVirtualItem> cashierVirtualItemList = apolloConfigHelper.getCashierChargeVirtualItemsV4();
            LicenceDistributionRequest request = buildDirectMerchantDistributionDataRequest(merchantV2VO.getMerchantId(), cashierVirtualItemList, goodsDetails);
            if (null == request || CollectionUtils.isEmpty(request.getDistributionData())) {
                return;
            }
            request.setOperationTime(dingAuditSyncEvent.getBuyTime());
            request.setBizOrderId(dingAuditSyncEvent.getBizOrderId());
            request.setSaleSource(dingAuditSyncEvent.getSaleSource());

            boolean processResult = cashierLicenceService.merchantPayDistribution(request);
            if (processResult) {
                log.info("directMerchantPayLicence4Oms process success dingAuditSyncEvent:{}", JSON.toJSONString(dingAuditSyncEvent));
            }
        } catch (Exception ex) {
            log.error("directMerchantPayLicence4Oms error, dingAuditSyncEvent:{}", JSON.toJSONString(dingAuditSyncEvent), ex);
        }
    }


    /**
     * 构建服务商许可证分配数据
     *
     * @param organizationId
     * @param userId
     * @param agentCashierVirtualItemList
     * @param goodsDetails
     * @return
     */
    private LicenceDistributionRequest buildAgentDistributionDataRequest(String organizationId,
                                                                         String userId,
                                                                         List<CashierChargeVirtualItem> agentCashierVirtualItemList,
                                                                         List<DingAuditSyncEvent.DingAuditSyncGoodsDetail> goodsDetails) {
        Map<String, List<CashierChargeVirtualItem>> agentCashierVirtualItemBySkuMap = LicenceUtil.groupCashierVirtualItemBySku(agentCashierVirtualItemList);
        Map<String, List<CashierChargeVirtualItem>> agentCashierVirtualItemByCodeAndNameMap = LicenceUtil.groupCashierVirtualItemByOmsCodeAndName(agentCashierVirtualItemList);
        if (MapUtils.isEmpty(agentCashierVirtualItemBySkuMap) && MapUtils.isEmpty(agentCashierVirtualItemByCodeAndNameMap)) {
            return null;
        }

        List<DingAuditSyncEvent.DingAuditSyncGoodsDetail> validGoodsDetailList = new ArrayList();
        List<LicenceDistributionRequest.DistributionData> distributionDataList = new ArrayList<>();
        for (DingAuditSyncEvent.DingAuditSyncGoodsDetail goodsDetail : goodsDetails) {
            CashierChargeVirtualItem virtualItem = null;
            if(StringUtils.isNotBlank(goodsDetail.getStandardSku())){
                String groupKey = goodsDetail.getStandardSku();
                List<CashierChargeVirtualItem> groupVirtualItemList = MapUtils.getObject(agentCashierVirtualItemBySkuMap, groupKey);
                if (CollectionUtils.isNotEmpty(groupVirtualItemList)) {
                    virtualItem = groupVirtualItemList.get(0);
                }
            }
            if(Objects.isNull(virtualItem)){
                String groupKey = LicenceUtil.getVirtualItemGroupKeyByCodeAndName(goodsDetail.getItemCode(), goodsDetail.getItemName());
                List<CashierChargeVirtualItem> groupVirtualItemList = MapUtils.getObject(agentCashierVirtualItemByCodeAndNameMap, groupKey);
                if (CollectionUtils.isNotEmpty(groupVirtualItemList)) {
                    virtualItem = groupVirtualItemList.get(0);
                }
            }
            if (Objects.isNull(virtualItem)) {
                continue;
            }

            LicenceDistributionRequest.DistributionData distributionData = new LicenceDistributionRequest.DistributionData();
            distributionData.setOrganizationId(organizationId);
            distributionData.setUserId(userId);
            distributionData.setVersionType(LicenceVersionTypeEnum.LIFE);
            distributionData.setBrand(virtualItem.getBrand());
            distributionData.setProductType(virtualItem.getProductType());
            distributionData.setRemark("购买");

            int quantity = (null != goodsDetail.getQuantity()) ? goodsDetail.getQuantity().intValue() : 1;
            List<LicenceSaleInfoDTO> saleInfoDTOList = new ArrayList<>();
            for (int i = 0; i < quantity; i++) {
                saleInfoDTOList.add(
                        LicenceSaleInfoDTO.builder()
                                .orderItemId(goodsDetail.getBizOrderGoodsId())
                                .omsItemCode(goodsDetail.getItemCode())
                                .omsItemName(goodsDetail.getItemName())
                                .sku(goodsDetail.getStandardSku())
                                .skuName(goodsDetail.getStandardSkuName())
                                .build());
            }
            distributionData.setQuantity(quantity);
            distributionData.setSaleInfoDTOList(saleInfoDTOList);

            distributionDataList.add(distributionData);
            validGoodsDetailList.add(goodsDetail);
        }
        if (CollectionUtils.isEmpty(distributionDataList)) {
            return null;
        }

        LicenceDistributionRequest request = new LicenceDistributionRequest();
        request.setUserType(LicenceUserTypeEnum.AGENT);
        request.setPermanent(YesNoEnum.Y);
        request.setRemark("购买");
        request.setOrganizationId(organizationId);
        request.setUserId(userId);

        if (CollectionUtils.isNotEmpty(validGoodsDetailList)) {
            request.setGoodsDetail(JSON.toJSONString(validGoodsDetailList));
            /*Map<String, DingAuditSyncEvent.DingAuditSyncGoodsDetail> saveGoodsDetailMap = validGoodsDetailList.stream()
                    .collect(Collectors.toMap(DingAuditSyncEvent.DingAuditSyncGoodsDetail::getStandardSku,
                            Function.identity(),
                            (n, o) -> {
                                n.setQuantity(n.getQuantity().longValue() + o.getQuantity().longValue());
                                return n;
                            }));
            if (MapUtils.isNotEmpty(saveGoodsDetailMap)) {
                List<DingAuditSyncEvent.DingAuditSyncGoodsDetail> saveValidGoodsDetailList = new ArrayList<>(saveGoodsDetailMap.values());
                if (CollectionUtils.isNotEmpty(saveValidGoodsDetailList)) {
                    request.setGoodsDetail(JSON.toJSONString(saveValidGoodsDetailList));
                }
            }*/
        }
        request.setDistributionData(distributionDataList);
        return request;
    }

    /**
     * 构建直营商户许可证分配数据
     * @param merchantId
     * @param cashierVirtualItemList
     * @param goodsDetails
     * @return
     */
    private LicenceDistributionRequest buildDirectMerchantDistributionDataRequest(String merchantId,
                                                                                  List<CashierChargeVirtualItem> cashierVirtualItemList,
                                                                                  List<DingAuditSyncEvent.DingAuditSyncGoodsDetail> goodsDetails) {
        Map<String, List<CashierChargeVirtualItem>> cashierVirtualItemBySkuMap = LicenceUtil.groupCashierVirtualItemBySku(cashierVirtualItemList);
        Map<String, List<CashierChargeVirtualItem>> cashierVirtualItemByCodeAndNameMap = LicenceUtil.groupCashierVirtualItemByOmsCodeAndName(cashierVirtualItemList);
        if (MapUtils.isEmpty(cashierVirtualItemBySkuMap) && MapUtils.isEmpty(cashierVirtualItemByCodeAndNameMap)) {
            return null;
        }

        List<LicenceDistributionRequest.DistributionVersionData> list = new ArrayList<>();
        for (DingAuditSyncEvent.DingAuditSyncGoodsDetail goodsDetail : goodsDetails) {
            CashierChargeVirtualItem virtualItem = null;
            if(StringUtils.isNotBlank(goodsDetail.getStandardSku())){
                String groupKey = goodsDetail.getStandardSku();
                List<CashierChargeVirtualItem> groupVirtualItemList = MapUtils.getObject(cashierVirtualItemBySkuMap, groupKey);
                if (CollectionUtils.isNotEmpty(groupVirtualItemList)) {
                    virtualItem = groupVirtualItemList.get(0);
                }
            }
            if(Objects.isNull(virtualItem)){
                String groupKey = LicenceUtil.getVirtualItemGroupKeyByCodeAndName(goodsDetail.getItemCode(), goodsDetail.getItemName());
                List<CashierChargeVirtualItem> groupVirtualItemList = MapUtils.getObject(cashierVirtualItemByCodeAndNameMap, groupKey);
                if (CollectionUtils.isNotEmpty(groupVirtualItemList)) {
                    virtualItem = groupVirtualItemList.get(0);
                }
            }
            if (null == virtualItem) {
                continue;
            }

            LicenceDistributionRequest.DistributionVersionData versionData = new LicenceDistributionRequest.DistributionVersionData();

            versionData.setVersionType(virtualItem.getVersionType());
            versionData.setProductType(virtualItem.getProductType());
            versionData.setBrand(virtualItem.getBrand());
            versionData.setQuantity(Optional.ofNullable(goodsDetail.getQuantity().intValue()).orElse(1));
            versionData.setOrderItemId(goodsDetail.getBizOrderGoodsId());
            versionData.setOmsItemCode(goodsDetail.getItemCode());
            versionData.setOmsItemName(goodsDetail.getItemName());
            versionData.setSku(goodsDetail.getStandardSku());
            versionData.setSkuName(goodsDetail.getStandardSkuName());
            list.add(versionData);
        }
        if (StringUtils.isBlank(merchantId) || CollectionUtils.isEmpty(list)) {
            return null;
        }

        List<LicenceDistributionRequest.DistributionData> finalDistributionDataList = new ArrayList<>();

        // 不能简单根据版本来聚合了
        Map<String, List<LicenceDistributionRequest.DistributionVersionData>> processDataMap = list
                .stream().collect(Collectors.groupingBy(it -> it.getBrand().name() + it.getProductType().name() + it.getVersionType().name()));

        for (Map.Entry<String, List<LicenceDistributionRequest.DistributionVersionData>> entry : processDataMap.entrySet()) {

            int quantity = entry.getValue().stream().mapToInt(LicenceDistributionRequest.DistributionVersionData::getQuantity).sum();
            LicenceDistributionRequest.DistributionVersionData firstRecord = entry.getValue().get(0);
            LicenceDistributionRequest.DistributionData distributionData = new LicenceDistributionRequest.DistributionData();
            distributionData.setOrganizationId(merchantId);
            distributionData.setUserId(null);
            distributionData.setVersionType(firstRecord.getVersionType());
            distributionData.setQuantity(quantity);
            distributionData.setBrand(firstRecord.getBrand());
            distributionData.setProductType(firstRecord.getProductType());

            if (CollectionUtils.isNotEmpty(entry.getValue())) {
                List<LicenceSaleInfoDTO> saleInfoDTOList = new ArrayList<>();
                for (LicenceDistributionRequest.DistributionVersionData versionData : entry.getValue()) {
                    LicenceSaleInfoDTO saleInfoDTO = new LicenceSaleInfoDTO();
                    BeanUtils.copyProperties(versionData, saleInfoDTO);

                    saleInfoDTOList.add(saleInfoDTO);
                }
                distributionData.setSaleInfoDTOList(saleInfoDTOList);
            }

            finalDistributionDataList.add(distributionData);
        }

        LicenceDistributionRequest request = new LicenceDistributionRequest();
        request.setPermanent(YesNoEnum.Y);
        request.setRemark("购买");
        request.setOrganizationId(merchantId);
        request.setDistributionData(finalDistributionDataList);
        request.setGoodsDetail(JSON.toJSONString(list));
        return request;
    }

    /**
     * 直营商户收银机退款后，需要取消指定数量的门店激活信息
     *
     * @param event
     */
    public void directMerchantRefundLicence(FTOrderBasicRefundEvent event) {
        try {
            List<RefundOrderItem> refundOrderItems = event.getRefundOrderItems();
            if (CollectionUtils.isEmpty(refundOrderItems)) {
                return;
            }

            boolean exists = productLicenceService.licenceOrderExists(event.getMerchantId(), event.getSn());
            if(!exists){
                return;
            }

            List<String> orderItemSnList = new ArrayList<>();
            for (RefundOrderItem refundOrderItem : refundOrderItems) {
                if (StringUtils.isBlank(refundOrderItem.getOrderItemSn())) {
                    continue;
                }
                OrderItem orderItem = salesOrderItemService.queryOrderItemById(refundOrderItem.getOrderItemSn());
                if (null == orderItem) {
                    continue;
                }
                orderItemSnList.add(refundOrderItem.getOrderItemSn());
            }
            if (CollectionUtils.isEmpty(orderItemSnList)) {
                return;
            }
            String merchantId = event.getMerchantId();
            String bizOrderId = event.getSn();
            //删除激活的收银机信息
            boolean deleteResult = productLicenceService.directMerchantRefundLicence(merchantId, bizOrderId, DeviceTypeEnum.CASHIER, ActiveDeviceSourceEnum.PAY, orderItemSnList);
            if (deleteResult) {
                log.info("directMerchantRefundLicence process success merchantId:{}, sn:{}, orderItemSnList:{}", event.getMerchantId(), event.getSn(), JSON.toJSONString(orderItemSnList));
            }
        } catch (Exception ex) {
            log.error("directMerchantRefundLicence error, event:{}", JSON.toJSONString(event), ex);
        }
    }

    /**
     * 异名换卡资产迁移处理
     *
     * @param event
     */
    public void accountTransferProcess(AccountTransferEvent event) {
        try {
            String merchantId = event.getMerchantId();
            String targetMerchantId = event.getTargetMerchantId();
            List<Pair> storeList = event.getStoreList();

            if (StringUtils.isBlank(merchantId) || StringUtils.isBlank(targetMerchantId) || CollectionUtils.isEmpty(storeList)) {
                return;
            }
            boolean transferResult = cashierLicenceService.accountLicenceTransfer(merchantId, targetMerchantId, storeList);
            if (transferResult) {
                log.info("accountTransferProcess process success merchantId:{},targetMerchantId:{},storeList:{}", merchantId, targetMerchantId, JSON.toJSONString(storeList));
            }
        } catch (Exception ex) {
            log.error("accountTransferProcess error, event:{}", JSON.toJSONString(event), ex);
        }
    }


    private void updateStoreCustomerRelationChange(String storeId, String sellerId) {
        log.info("updateStoreCustomerRelationChange process===>storeId:{},sellerId:{}", storeId, sellerId);
        try {
            Map<String, Object> organizationMap = organizationService.getSimpleOrganization(sellerId);
            String organizationName = MapUtils.getString(organizationMap, "name_path", null);
            String organizationCode = MapUtils.getString(organizationMap, "code", null);
            storeDeviceWhitelistService.updateOrganizationInfo(storeId, organizationName, organizationCode);
        } catch (Exception ex) {
            log.error("updateStoreCustomerRelationChange error, storeId:{},sellerId:{}", storeId, sellerId, ex);
        }
    }

    public CashierChargeVirtualItem getCashierChargeVirtualItemBySku(String sku, List<CashierChargeVirtualItem> apolloVirtualItemConfigList) {
        if (CollectionUtils.isEmpty(apolloVirtualItemConfigList)) {
            return null;
        }
        CashierChargeVirtualItem response = null;
        for (CashierChargeVirtualItem virtualItem : apolloVirtualItemConfigList) {
            if (Objects.equals(sku, virtualItem.getSku())) {
                response = virtualItem;
            }
        }
        return response;
    }


    /**
     * 验证是否需要迁移许可证信息，相同4级下的组织需要迁移
     *
     * @param userId
     * @param sourceLevel
     * @param sourceCodePath
     * @param targetLevel
     * @param targetCodePath
     * @return
     */
    private boolean needMoveLicence(String userId, Integer sourceLevel, String sourceCodePath, Integer targetLevel, String targetCodePath) {
        if (StringUtils.isBlank(userId) || null == sourceLevel || null == targetLevel) {
            return false;
        }
        if (!LicenceUtil.isAgentOrganization(sourceCodePath) || !LicenceUtil.isAgentOrganization(targetCodePath)) {
            return false;
        }

        String[] sourceCodeArray = StringUtils.split(sourceCodePath, ",");
        String[] targetCodeArray = StringUtils.split(targetCodePath, ",");
        if (sourceLevel < Constants.CrmConstants.ORGANIZATION_LEVEL_4
                || targetLevel < Constants.CrmConstants.ORGANIZATION_LEVEL_4
                || sourceCodeArray.length < Constants.CrmConstants.ORGANIZATION_LEVEL_4
                || targetCodeArray.length < Constants.CrmConstants.ORGANIZATION_LEVEL_4) {
            return false;
        }

        if (Objects.equals(sourceCodePath, targetCodePath)) {
            return false;
        }
        return true;
    }

    /**
     * 获取许可证数据
     *
     * @param merchantId
     * @param bizOrderId
     * @return
     */
    public List<LicenceDistributionRequest.DistributionData> getDirectMerchantLicenceData(String merchantId, String bizOrderId) {
        List<CashierChargeVirtualItem> cashierVirtualItemList = apolloConfigHelper.getCashierChargeVirtualItemsV4();
        Map<String, List<CashierChargeVirtualItem>> cashierVirtualItemBySkuMap = LicenceUtil.groupCashierVirtualItemBySku(cashierVirtualItemList);
        Map<String, List<CashierChargeVirtualItem>> cashierVirtualItemByCodeAndNameMap = LicenceUtil.groupCashierVirtualItemByOmsCodeAndName(cashierVirtualItemList);
        if (MapUtils.isEmpty(cashierVirtualItemBySkuMap) && MapUtils.isEmpty(cashierVirtualItemByCodeAndNameMap)) {
            return null;
        }

        List<OrderItem> orderItems = salesOrderItemService.queryOrderItemByOrderSn(bizOrderId);
        if (CollectionUtils.isEmpty(orderItems)) {
            return null;
        }
        List<LicenceDistributionRequest.DistributionVersionData> list = new ArrayList<>();
        for (OrderItem orderItem : orderItems) {
            CashierChargeVirtualItem virtualItem = null;
            if(StringUtils.isNotBlank(orderItem.getSkuCode())){
                String groupKey = orderItem.getSkuCode();
                List<CashierChargeVirtualItem> groupVirtualItemList = MapUtils.getObject(cashierVirtualItemBySkuMap, groupKey);
                if (CollectionUtils.isNotEmpty(groupVirtualItemList)) {
                    virtualItem = groupVirtualItemList.get(0);
                }
            }
            if(Objects.isNull(virtualItem)){
                String groupKey = LicenceUtil.getVirtualItemGroupKeyByCodeAndName(orderItem.getOmsCode(), orderItem.getTerminalName());
                List<CashierChargeVirtualItem> groupVirtualItemList = MapUtils.getObject(cashierVirtualItemByCodeAndNameMap, groupKey);
                if (CollectionUtils.isNotEmpty(groupVirtualItemList)) {
                    virtualItem = groupVirtualItemList.get(0);
                }
            }
            if (null == virtualItem) {
                continue;
            }

            LicenceDistributionRequest.DistributionVersionData versionData = new LicenceDistributionRequest.DistributionVersionData();

            versionData.setVersionType(virtualItem.getVersionType());
            versionData.setProductType(virtualItem.getProductType());
            versionData.setBrand(virtualItem.getBrand());
            versionData.setQuantity(orderItem.getQuantity());
            versionData.setOrderItemId(orderItem.getId());
            versionData.setCrmItemCode(
                    Optional.ofNullable(orderItem.getTerminalType())
                            .map(Object::toString)
                            .orElse(null)
            );
            versionData.setCrmItemName(orderItem.getTerminalName());
            versionData.setOmsItemCode(orderItem.getOmsCode());
            versionData.setOmsItemName(orderItem.getTerminalName());
            list.add(versionData);
        }
        if (StringUtils.isBlank(merchantId) || CollectionUtils.isEmpty(list)) {
            return null;
        }

        List<LicenceDistributionRequest.DistributionData> finalDistributionDataList = new ArrayList<>();

        // 不能简单根据版本来聚合了
        Map<String, List<LicenceDistributionRequest.DistributionVersionData>> processDataMap = list
                .stream().collect(Collectors.groupingBy(it -> it.getBrand().name() + it.getProductType().name() + it.getVersionType().name()));

        for (Map.Entry<String, List<LicenceDistributionRequest.DistributionVersionData>> entry : processDataMap.entrySet()) {

            int quantity = entry.getValue().stream().mapToInt(LicenceDistributionRequest.DistributionVersionData::getQuantity).sum();
            LicenceDistributionRequest.DistributionVersionData firstRecord = entry.getValue().get(0);
            LicenceDistributionRequest.DistributionData distributionData = new LicenceDistributionRequest.DistributionData();
            distributionData.setOrganizationId(merchantId);
            distributionData.setUserId(null);
            distributionData.setVersionType(firstRecord.getVersionType());
            distributionData.setQuantity(quantity);
            distributionData.setBrand(firstRecord.getBrand());
            distributionData.setProductType(firstRecord.getProductType());

            if (CollectionUtils.isNotEmpty(entry.getValue())) {
                List<LicenceSaleInfoDTO> saleInfoDTOList = new ArrayList<>();
                for (LicenceDistributionRequest.DistributionVersionData versionData : entry.getValue()) {
                    LicenceSaleInfoDTO saleInfoDTO = new LicenceSaleInfoDTO();
                    BeanUtils.copyProperties(versionData, saleInfoDTO);

                    saleInfoDTOList.add(saleInfoDTO);
                }
                distributionData.setSaleInfoDTOList(saleInfoDTOList);
            }

            finalDistributionDataList.add(distributionData);
        }
        return finalDistributionDataList;
    }

    /**
     * 获取直营商户订单退款明细ID
     *
     * @param merchantId
     * @param bizOrderId
     * @return
     */
    public List<String> getDirectMerchantLicenceRefundOrderItemIdList(String merchantId, String bizOrderId) {
        List<OrderItem> orderItems = salesOrderItemService.queryOrderItemByOrderSn(bizOrderId);
        if (CollectionUtils.isEmpty(orderItems)) {
            return null;
        }

        List<String> list = new ArrayList<>();
        for (OrderItem orderItem : orderItems) {
            if (!Objects.equals(merchantId, orderItem.getMerchantId())) {
                continue;
            }

            if (Objects.equals(orderItem.getRefundStatus(), TerminalOrderItemRefundStatusEnum.REFUNDED.name())
                    || Objects.equals(orderItem.getRefundStatus(), TerminalOrderItemRefundStatusEnum.MONEY_REFUNDED.name())) {
                list.add(orderItem.getId());
            }
        }
        return list;
    }
}
