package com.wosai.smartbiz.controller.uc;

import com.google.gson.Gson;
import com.wosai.smartbiz.base.enums.SceneEnum;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.gds.service.PermissionCheckService;
import com.wosai.smartbiz.uc.constants.Constants;
import com.wosai.smartbiz.uc.utils.UcRedisUtil;
import com.wosai.smartbiz.user.user.constants.UcConstants;
import com.wosai.smartbiz.user.user.enums.UcErrorCodeEnum;
import com.wosai.smartbiz.user.user.login.DeviceParam;
import com.wosai.smartbiz.uc.services.TokenV2Service;
import com.wosai.smartbiz.user.user.vo.PermissionTokenVO;
import com.wosai.smartbiz.user.user.vo.UserLoginV2VO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;


@RestController
@RequestMapping("/token")
public class TokenController extends BaseController {

    @Autowired
    private TokenV2Service tokenV2Service;
    @Autowired
    private PermissionCheckService permissionCheckService;

    @RequestMapping(value = "/v2/verify", method = RequestMethod.POST)
    public Result<UserLoginV2VO> verifyV2(@RequestParam(value = "needTerminal", required = false) boolean needTerminal,
                                          HttpServletRequest request,
                                          HttpServletResponse response){
        String authorization = getRequestAuthorization(request);
        if(StringUtils.isBlank(authorization)){
            return Result.error(UcErrorCodeEnum.ACCESS_TOKEN_INVALID);
        }
        if(!authorization.startsWith(UcConstants.RequestConstants.AUTHORIZATION_SCHAME)){
            return Result.error(UcErrorCodeEnum.ACCESS_TOKEN_INVALID);
        }

        DeviceParam deviceParam = null;
        if(needTerminal){
            String deviceFp = getRequestTerminalSn(request);
            SceneEnum deviceScene = getRequestDeviceScene(request);
            boolean sunmiDevice = isSunmiDevice(request);
            String appVersion = getRequestedAppVersion(request);
            if(StringUtils.isBlank(deviceFp)){
                return Result.error(UcErrorCodeEnum.TERMINAL_SN_ERROR);
            }
            deviceParam = new DeviceParam();
            deviceParam.setDeviceFp(deviceFp);
            deviceParam.setDeviceScene(deviceScene);
            deviceParam.setSunmiDevice(sunmiDevice);
            deviceParam.setAppVersion(appVersion);
        }


        String storeId = request.getParameter("storeId");
        String accessToken = authorization.substring(7);

        Result<UserLoginV2VO> result = tokenV2Service.verifyToken(accessToken, storeId, deviceParam, needTerminal);
        if(!result.isSuccess()){
            return Result.error(result.getErrorCode(), result.getErrorMsg());
        }

        String permissionCode = getRequestPermissionCode(request);

        String permissionToken = getRequestPermissionToken(request);

        //验证授权权限信息
        Result<Boolean> permissionResult = permissionCheckService.check(permissionToken,permissionCode,result.getData().getMerchantId());
        if(!permissionResult.isSuccess()){
            return Result.error(result.getErrorCode(), result.getErrorMsg());
        }
        return result;
    }


}
