package com.wosai.smartbiz.controller.gds.repast.v2;

import com.wosai.market.dto.category.CategoryDetail;
import com.wosai.market.dto.product.ProductDetail;
import com.wosai.market.dto.product.ProductWaterMark;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.catering.adapter.CashierCateringCustomUnitAdapter;
import com.wosai.smartbiz.controller.gds.BaseController;
import com.wosai.smartbiz.converter.gds.repast.CategoryV2ConvertUtils;
import com.wosai.smartbiz.converter.gds.repast.ProductV2ConvertUtils;
import com.wosai.smartbiz.gds.service.ProductSyncV2Service;
import com.wosai.smartbiz.goods.pojo.CursorPage;
import com.wosai.smartbiz.goods.query.repast.StoreProductCursorQuery;
import com.wosai.smartbiz.vo.gds.gds.v2.ProductCategoryVO;
import com.wosai.smartbiz.vo.gds.gds.v2.ProductWaterMarkVO;
import com.wosai.smartbiz.vo.gds.gds.v2.StoreProductVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2020/12/30
 */
@RestController
@RequestMapping(value = "/gds/repast/v2/product/sync")
public class ProductSyncV2Controller extends BaseController {

    @Autowired
    private ProductSyncV2Service productSyncV2Service;

    @Autowired
    private CashierCateringCustomUnitAdapter cashierCateringCustomUnitAdapter;

    /**
     * 同步商品信息至收银端
     * @param query
     * @param bindingResult
     * @return
     *
     */
    @PostMapping(value = "/cashier/product")
    public Result<CursorPage<StoreProductVO>> syncCashierProduct(@Valid StoreProductCursorQuery query, BindingResult bindingResult, HttpServletRequest httpServletRequest){
        if(bindingResult.hasErrors()){
            return processErrorResult(bindingResult);
        }
        return tryCatch(() -> {
            List<ProductDetail> detailList = productSyncV2Service.syncProductToCashier(query);
            if(CollectionUtils.isEmpty(detailList)){
                return new CursorPage<>();
            }
            List<StoreProductVO> detailVos = ProductV2ConvertUtils.buildStoreProductVO(detailList);

            // 针对老版本收银机做特殊处理
            String version = this.getRequestedAppVersion(httpServletRequest);
            if (cashierCateringCustomUnitAdapter.isLessThanDestAppVersion(version)) {
                detailVos.forEach(r -> cashierCateringCustomUnitAdapter.process(r));
            }

            return new CursorPage<>(detailVos, query.getPageSize(), StoreProductVO::getId);
        });
    }

    /**
     * 同步商品更新水位线至收银端
     * @param merchantId
     * @param storeId
     * @return
     */
    @PostMapping(value = "/cashier/product/waterMark")
    public Result<ProductWaterMarkVO> syncCashierWaterMark(@RequestParam(value = "merchantId") String merchantId,
                                                           @RequestParam(value = "storeId") String storeId)
    {
        return tryCatch(() -> {
            ProductWaterMark mark = productSyncV2Service.syncWaterMarkToCashier(merchantId, storeId);
            return ProductWaterMarkVO.builder()
                    .storeId(mark.getStoreId())
                    .waterMark(mark.getWaterMark())
                    .build();
        });
    }

    /**
     * 同步分类信息至收银端
     * @param merchantId
     * @param storeId
     * @return
     */
    @PostMapping(value = "/cashier/category")
    public Result<List<ProductCategoryVO>> syncCashierCategory(@RequestParam(value = "merchantId") String merchantId,
                                                               @RequestParam(value = "storeId") String storeId)
    {
        return tryCatch(() -> {
            List<CategoryDetail> detailList = productSyncV2Service.syncCategoryToCashier(merchantId, storeId);
            if(CollectionUtils.isEmpty(detailList)){
                return Collections.emptyList();
            }
            return CategoryV2ConvertUtils.buildProductCategoryVOList(detailList);
        });
    }

}
