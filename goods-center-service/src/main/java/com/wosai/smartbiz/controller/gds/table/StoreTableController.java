package com.wosai.smartbiz.controller.gds.table;

import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.controller.gds.BaseController;
import com.wosai.smartbiz.dto.req.table.*;
import com.wosai.smartbiz.dto.vo.table.StoreTableCountVO;
import com.wosai.smartbiz.dto.vo.table.StoreTableVO;
import com.wosai.smartbiz.gds.service.StoreTableService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * @author: KangYi<PERSON>un
 * @date: 2020-09-02
 */
@RestController
@RequestMapping(value = "/store/table")
public class StoreTableController extends BaseController {

    @Autowired
    private StoreTableService storeTableService;

    /**
     * 保存桌台
     * @param req
     * @param bindingResult
     * @param request
     * @return
     */
    @PostMapping(value = "/save")
    public Result<Boolean> save(@Valid StoreTableSaveReq req,
                                BindingResult bindingResult,
                                HttpServletRequest request)
    {
        if(bindingResult.hasErrors()){
            return processErrorResult(bindingResult);
        }
        this.completeReq(req, request);
        return tryCatch(() -> storeTableService.save(req));
    }

    /**
     * 桌台列表
     * @param req
     * @param bindingResult
     * @param request
     * @return
     */
    @PostMapping(value = "/list")
    public Result<List<StoreTableVO>> getList(@Valid StoreTableAreaBaseReq req,
                                              BindingResult bindingResult,
                                              HttpServletRequest request)
    {
        if(bindingResult.hasErrors()){
            return processErrorResult(bindingResult);
        }
        this.completeReq(req, request);
        return tryCatch(() -> storeTableService.getList(req));
    }

    /**
     * 删除桌台
     * @param req
     * @param bindingResult
     * @param request
     * @return
     */
    @PostMapping(value = "/delete")
    public Result<Boolean> delete(@Valid StoreTableDeleteReq req,
                                  BindingResult bindingResult,
                                  HttpServletRequest request)
    {
        if(bindingResult.hasErrors()){
            return processErrorResult(bindingResult);
        }
        this.completeReq(req, request);
        return tryCatch(() -> storeTableService.delete(req));
    }

    /**
     * 桌台排序
     * @param req
     * @param bindingResult
     * @param request
     * @return
     */
    @PostMapping(value = "/sort")
    public Result<Boolean> sort(@Valid StoreTableSortReq req,
                                BindingResult bindingResult,
                                HttpServletRequest request)
    {
        if(bindingResult.hasErrors()){
            return processErrorResult(bindingResult);
        }
        this.completeReq(req, request);
        return tryCatch(() -> storeTableService.sort(req));
    }

    /**
     * 批量创建桌台
     * @param req
     * @param bindingResult
     * @param request
     * @return
     */
    @PostMapping(value = "/batchCreate")
    public Result<Boolean> batchCreate(@Valid StoreTableBatchCreateReq req,
                                       BindingResult bindingResult,
                                       HttpServletRequest request)
    {
        if(bindingResult.hasErrors()){
            return processErrorResult(bindingResult);
        }
        this.completeReq(req, request);
        return tryCatch(() -> storeTableService.batchCreate(req));
    }

    /**
     * 根据门店code获取统计
     * @param
     * @return
     */
    @PostMapping(value = "/status/count")
    public Result<StoreTableCountVO> getCountStoreTable(String storeId,
                                             HttpServletRequest request)
    {

        return tryCatch(() -> storeTableService.getCountStoreTable(storeId));
    }

    protected <T extends StoreTableAreaBaseReq> void completeReq(T req, HttpServletRequest request){
//        AuthLoginUser loginUser = getRequestedUser(request);
//        req.setMerchantCode(loginUser.getMerchantCode());
//        req.setLoginUser(loginUser);
    }


}
