package com.wosai.smartbiz.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.wosai.market.mcc.api.dto.request.CreateConfigRequest;
import com.wosai.market.mcc.api.dto.request.CreateDefaultConfigRequest;
import com.wosai.market.mcc.api.dto.request.FindConfigByNameRequest;
import com.wosai.market.mcc.api.dto.response.ConfigResponse;
import com.wosai.market.mcc.api.enums.AppId;
import com.wosai.market.mcc.api.enums.OwnerType;
import com.wosai.market.mcc.api.service.ConfigRemoteService;
import com.wosai.market.mcc.api.service.DefaultConfigRemoteService;
import com.wosai.market.merchant.api.QRCodeRemoteService;
import com.wosai.market.merchant.dto.request.qrcode.QRCodeImageRequest;
import com.wosai.market.service.BatchCreateDataService;
import com.wosai.smartbiz.base.enums.YesNoEnum;
import com.wosai.smartbiz.base.mybatis.page.Page;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.base.utils.TagUtil;
import com.wosai.smartbiz.controller.gds.BaseController;
import com.wosai.smartbiz.enums.MccConfigEnum;
import com.wosai.smartbiz.gds.enums.ErrorCodeEnum;
import com.wosai.smartbiz.gds.enums.TableStatusEnum;
import com.wosai.smartbiz.goods.direct.AppEdgeBusinessReviseMapper;
import com.wosai.smartbiz.goods.direct.QrCodeMigrateDataMapper;
import com.wosai.smartbiz.goods.direct.StoreTableAreaDAO;
import com.wosai.smartbiz.goods.direct.StoreTableDAO;
import com.wosai.smartbiz.goods.domain.QrCodeMigrateDataDO;
import com.wosai.smartbiz.goods.domain.repast.table.StoreTableAreaDO;
import com.wosai.smartbiz.goods.domain.repast.table.StoreTableDO;
import com.wosai.smartbiz.goods.util.PageUtil;
import com.wosai.smartbiz.gds.service.StoreTableAreaService;
import com.wosai.smartbiz.gds.service.StoreTableService;
import com.wosai.smartbiz.uc.dao.QrCodeMapper;
import com.wosai.smartbiz.uc.dao.StorePrinterConfigMapper;
import com.wosai.smartbiz.uc.dao.StorePrinterConfigXrefMapper;
import com.wosai.smartbiz.uc.domain.QrCodeDO;
import com.wosai.smartbiz.uc.domain.UcStorePrinterConfigXrefDO;
import com.wosai.smartbiz.uc.utils.common.DateUtils;
import com.wosai.smartbiz.user.user.domain.StorePrinterConfigDO;
import com.wosai.smartbiz.user.user.enums.QrCodeBusinessType;
import com.wosai.smartbiz.user.user.enums.QrCodeStatus;
import com.wosai.smartbiz.user.user.enums.RemoteQrCodeType;
import com.wosai.smartbiz.user.user.enums.print.PrintRangeEnum;
import com.wosai.smartbiz.user.user.enums.print.PrintSceneTypeEnum;
import com.wosai.smartbiz.user.user.enums.print.PrinterConfigTagEnum;
import com.wosai.smartbiz.user.user.enums.print.PrinterXrefTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @create 2021/3/2
 */
@RestController
@RequestMapping(value = "/appEdge/business/revise")
public class AppEdgeBusinessReviseController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AppEdgeBusinessReviseController.class);

    @Autowired
    private QrCodeMigrateDataMapper qrCodeMigrateDataMapper;

    @Autowired
    private QrCodeMapper qrCodeMapper;

    @Autowired
    private StoreTableDAO storeTableDAO;

    @Autowired
    private StoreTableAreaDAO storeTableAreaDAO;

    @Autowired
    private StoreTableAreaService storeTableAreaService;

    @Autowired
    private StoreTableService storeTableService;

    @Autowired
    private QRCodeRemoteService qrCodeRemoteService;

    @Autowired
    private AppEdgeBusinessReviseMapper appEdgeBusinessReviseMapper;

    @Autowired
    private DefaultConfigRemoteService defaultConfigRemoteService;

    @Autowired
    private ConfigRemoteService configRemoteService;

    @Autowired
    private StorePrinterConfigMapper storePrinterConfigMapper;

    @Autowired
    private StorePrinterConfigXrefMapper storePrinterConfigXrefMapper;

    @Autowired
    private BatchCreateDataService batchCreateDataService;

    /**
     * 订正打印机关联信息
     * @param categoryMapString
     * @param productMapString
     * @return
     */
    @PostMapping(value = "/printer/xref")
    public Result<List<String>> revisePrinterXref(@RequestParam(value = "categoryMapString") String categoryMapString,
                                                  @RequestParam(value = "productMapString") String productMapString,
                                                  @RequestParam(value = "merchantId", required = false) String merchantId) {

        if(StringUtils.isAnyBlank(categoryMapString, productMapString)){
            return Result.success(Lists.newArrayList());
        }
        List<String> result = Lists.newArrayList();

        Map<String, String> oldCategoryIdToNewCategoryIdMap = new HashMap<>(16);
        Map<String, String> oldSpuIdToNewSpuIdMap = new HashMap<>(16);

        Gson gson = new Gson();
        oldCategoryIdToNewCategoryIdMap = gson.fromJson(categoryMapString, oldCategoryIdToNewCategoryIdMap.getClass());
        oldSpuIdToNewSpuIdMap = gson.fromJson(productMapString, oldSpuIdToNewSpuIdMap.getClass());

        // 所有打印机
        List<StorePrinterConfigDO> printerList;
        if(StringUtils.isBlank(merchantId)){
            printerList = storePrinterConfigMapper.listAll();
        } else {
            printerList = storePrinterConfigMapper.listByMerchantId(merchantId);
        }

        if(CollectionUtils.isEmpty(printerList)){
            result.add("没有可订正的打印机列表");
            return Result.success(result);
        }

        List<Long> printerIdList = printerList.stream()
                .map(StorePrinterConfigDO::getId)
                .collect(Collectors.toList());

        List<UcStorePrinterConfigXrefDO> xrefList = storePrinterConfigXrefMapper.selectByConfigIdAndType(
                printerIdList,
                Lists.newArrayList(PrinterXrefTypeEnum.CATEGORY, PrinterXrefTypeEnum.PRODUCT));

        Map<Long, List<UcStorePrinterConfigXrefDO>> printerIdToXrefMap = xrefList.stream()
                .collect(Collectors.groupingBy(UcStorePrinterConfigXrefDO::getConfigId));

        for(StorePrinterConfigDO oldPrinter : printerList) {

            StorePrinterConfigDO updatePrinter = oldPrinter;
            List<UcStorePrinterConfigXrefDO> updateXrefList = Lists.newArrayList();

            List<UcStorePrinterConfigXrefDO> oldXrefList = printerIdToXrefMap.getOrDefault(oldPrinter.getId(), Lists.newArrayList());
            LOGGER.info("订正打印机数据，原数据：{}，关联信息：{}", JSON.toJSONString(oldPrinter), JSON.toJSONString(oldXrefList));
            result.add(String.format("订正打印机数据，原数据：%s，关联信息：%s", JSON.toJSONString(oldPrinter), JSON.toJSONString(oldXrefList)));

            // 原打印份数 =》后厨打印份数
            updatePrinter.setBackPrintNum(oldPrinter.getPrintNum());
            updatePrinter.setPrintSpuRange(PrintRangeEnum.PART);

            // 配置打印所有分类 =》所有商品
            if(Objects.equals(updatePrinter.getGoodsCategoryIds(), PrintRangeEnum.ALL.name())){
                updatePrinter.setPrintSpuRange(PrintRangeEnum.ALL);
            }

            // 打印部分分类 =》修改分类ID
            if(Objects.equals(updatePrinter.getGoodsCategoryIds(), PrintRangeEnum.PART.name())){
                Map<String, String> finalOldCategoryIdToNewCategoryIdMap = oldCategoryIdToNewCategoryIdMap;
                List<UcStorePrinterConfigXrefDO> tmpCategoryXrefList = oldXrefList.stream()
                        .filter(p -> Objects.equals(p.getXrefType(), PrinterXrefTypeEnum.CATEGORY))
                        .collect(Collectors.toList());

                if(CollectionUtils.isNotEmpty(tmpCategoryXrefList)){
                    tmpCategoryXrefList.forEach(p -> {
                        String xrefId = p.getXrefId();
                        p.setXrefId(finalOldCategoryIdToNewCategoryIdMap.getOrDefault(xrefId, xrefId));
                    });
                    updateXrefList.addAll(tmpCategoryXrefList);
                }
            }

            List<UcStorePrinterConfigXrefDO> productXrefList = oldXrefList.stream()
                    .filter(p -> Objects.equals(p.getXrefType(), PrinterXrefTypeEnum.PRODUCT))
                    .collect(Collectors.toList());

            // 配置指定商品打印
            if(CollectionUtils.isNotEmpty(productXrefList) && Objects.equals(updatePrinter.getPrintSpuRange(), PrintRangeEnum.PART)){
                Map<String, String> finalOldSpuIdToNewSpuIdMap = oldSpuIdToNewSpuIdMap;
                productXrefList.forEach(p -> {
                    String xrefId = p.getXrefId();
                    p.setXrefId(finalOldSpuIdToNewSpuIdMap.getOrDefault(xrefId, xrefId));
                });
                updateXrefList.addAll(productXrefList);
            }


            LOGGER.info("订正打印机数据，新数据：{}，关联信息：{}", JSON.toJSONString(updatePrinter), JSON.toJSONString(updateXrefList));
            result.add(String.format("订正打印机数据，新数据：%s，关联信息：%s", JSON.toJSONString(updatePrinter), JSON.toJSONString(updateXrefList)));

            // 直接更新
            storePrinterConfigMapper.updateByPrimaryKey(updatePrinter);
            storePrinterConfigXrefMapper.deleteByConfigId(oldPrinter.getId());
            if(CollectionUtils.isNotEmpty(updateXrefList)){
                storePrinterConfigXrefMapper.insertBatch(updateXrefList);
            }
        }

        return Result.success(result);
    }

    /**
     * 添加MCC配置
     * @param appId
     * @param ownerType
     * @param ownerId
     * @param name
     * @param value
     * @param enabled
     * @return
     */
    @PostMapping(value = "/mcc/config")
    public Result<Boolean> insertConfig(@RequestParam(value = "appId") String appId,
                                        @RequestParam(value = "ownerType") String ownerType,
                                        @RequestParam(value = "ownerId") String ownerId,
                                        @RequestParam(value = "name") String name,
                                        @RequestParam(value = "value") String value,
                                        @RequestParam(value = "enabled", defaultValue = "true") String enabled){
        CreateConfigRequest request = new CreateConfigRequest();
        request.setAppId(appId);
        request.setOwnerType(ownerType);
        request.setOwnerId(ownerId);
        request.setName(name);
        request.setValue(value);
        request.setEnabled(Objects.equals(enabled, "true"));
        configRemoteService.upsertConfigByName(request);
        return Result.success(Boolean.TRUE);
    }

    /**
     * 添加MCC默认配置
     * @param appId
     * @param ownerType
     * @param name
     * @param value
     * @return
     */
    @PostMapping(value = "/mcc/default/config")
    public Result<Boolean> insertDefaultConfig(@RequestParam(value = "appId") String appId,
                                               @RequestParam(value = "ownerType") String ownerType,
                                               @RequestParam(value = "name") String name,
                                               @RequestParam(value = "value") String value) {
        CreateDefaultConfigRequest request = new CreateDefaultConfigRequest();
        request.setAppId(appId);
        request.setOwnerType(ownerType);
        request.setName(name);
        request.setValue(value);
        defaultConfigRemoteService.upsertDefaultConfigByName(request);
        return Result.success(Boolean.TRUE);
    }

    /**
     * 订正app-edge迁移业务数据
     * @param business
     * @return
     */
    @PostMapping(value = "/business")
    public Result<List<String>> reviseBusiness(@RequestParam(value = "business", required = false) String business,
                                               @RequestParam(value = "merchantId", required = false) String merchantId) {

        List<String> result = Lists.newArrayList();
        Set<Integer> businessSet;
        if(StringUtils.isNotBlank(business)){
            businessSet = Stream.of(StringUtils.split(business, ","))
                    .map(Integer::valueOf)
                    .collect(Collectors.toSet());
        } else {
            businessSet = Sets.newHashSet(1, 2, 3, 4, 5, 6, 7);
        }

        if(businessSet.contains(7)){
            int count = appEdgeBusinessReviseMapper.reviseTableStatus();
            result.add(String.format("桌台状态订正数据 %s 条", count));
            LOGGER.info("桌台状态订正数据 {} 条", count);
        }

        if(businessSet.contains(8) && StringUtils.isNotBlank(merchantId)){
            int count = batchCreateDataService.reviseMultiStoreStock(merchantId);
            result.add(String.format("多门店商户商品库存订正数据 %s 条", count));
            LOGGER.info("多门店商户商品库存订正数据 {} 条", count);
        }

        return Result.success(result);
    }

    /**
     * 订正绑错桌台的点单码
     * @param pageSize
     * @return
     */
    public Integer reviseWrongQrCodeBindInfo(Integer pageSize) {

        if(Objects.isNull(pageSize) || pageSize <= 0){
            pageSize = 5000;
        }
        int pageNumber = 1;
        int successCount = 0;
        boolean queryNextPage = true;

        do {
            // 分页取订正数据
            PageHelper.startPage(pageNumber, pageSize);
            List<QrCodeDO> allQrCodeDOList = qrCodeMapper.listAllByBusinessType(QrCodeBusinessType.TABLE_SCAN_CODE_ORDER);
            Page<QrCodeDO> qrCodeDOPage = PageUtil.toPage(allQrCodeDOList, pageSize);
            List<QrCodeDO> qrCodeDOList = qrCodeDOPage.getPageData();
            if(CollectionUtils.isEmpty(qrCodeDOList)){
                queryNextPage = false;
                break;
            }

            // 按商户分组处理
            Map<String, List<QrCodeDO>> merchantIdToQrCodeDOMap = qrCodeDOList.stream()
                    .collect(Collectors.groupingBy(QrCodeDO::getMerchantId));
            for(Map.Entry<String, List<QrCodeDO>> entry : merchantIdToQrCodeDOMap.entrySet()) {
                String merchantId = entry.getKey();
                List<QrCodeDO> tmpQrCodeDOList = entry.getValue();
                if(CollectionUtils.isEmpty(tmpQrCodeDOList)){
                    continue;
                }
                successCount += this.reviseQrCodeByMerchant(merchantId, tmpQrCodeDOList);
            }

            // 翻页
            pageNumber++;
        } while (queryNextPage);

        LOGGER.info("所有绑定桌台的点单码已订正，totalSuccessCount:{}, endTime:{}", successCount, System.currentTimeMillis());

        return successCount;
    }

    /**
     * 订正绑错桌台的点单码（按商户）
     * @param merchantId
     * @param qrCodeDOList
     * @return
     */
    private Integer reviseQrCodeByMerchant(String merchantId, List<QrCodeDO> qrCodeDOList) {
        int successCount = 0;
        if(CollectionUtils.isEmpty(qrCodeDOList)){
            return successCount;
        }

        Map<String, List<QrCodeDO>> storeIdToQrCodeDOMap = qrCodeDOList.stream()
                .collect(Collectors.groupingBy(QrCodeDO::getStoreId));

        List<StoreTableDO> tableDOList = storeTableDAO.listByMerchantId(merchantId);
        Map<String, List<StoreTableDO>> storeIdToTableDOMap = tableDOList.stream()
                .collect(Collectors.groupingBy(StoreTableDO::getStoreId));

        List<StoreTableAreaDO> areaDOList = storeTableAreaDAO.listByMerchantId(merchantId);
        Map<String, List<StoreTableAreaDO>> storeIdToAreaDOMap = areaDOList.stream()
                .collect(Collectors.groupingBy(StoreTableAreaDO::getStoreId));

        // 按门店分组处理
        for(Map.Entry<String, List<QrCodeDO>> storeEntry : storeIdToQrCodeDOMap.entrySet()) {
            String storeId = storeEntry.getKey();
            List<QrCodeDO> storeQrCodeDOList = storeEntry.getValue();
            Integer singleSuccessCount = this.reviseQrCodeByStore(
                    merchantId,
                    storeId,
                    storeIdToAreaDOMap.get(storeId),
                    storeIdToTableDOMap.get(storeId),
                    storeQrCodeDOList);
            successCount += Optional.ofNullable(singleSuccessCount).orElse(0);
            if(Objects.nonNull(singleSuccessCount) && singleSuccessCount > 0) {
                LOGGER.info("订正绑定桌台错误的点单码完成，merchantId:{}, storeId:{}, successCount:{}", merchantId, storeId, singleSuccessCount);
            }
        }

        return successCount;
    }

    /**
     * 订正绑错桌台的点单码（按门店）
     * @param merchantId
     * @param storeId
     * @param areaDOList
     * @param tableDOList
     * @param qrCodeDOList
     * @return
     */
    private Integer reviseQrCodeByStore(String merchantId,
                                        String storeId,
                                        List<StoreTableAreaDO> areaDOList,
                                        List<StoreTableDO> tableDOList,
                                        List<QrCodeDO> qrCodeDOList)
    {

        int successCount = 0;
        if(CollectionUtils.isEmpty(qrCodeDOList)){
            return successCount;
        }

        areaDOList = ListUtils.defaultIfNull(areaDOList, Lists.newArrayList());
        tableDOList = ListUtils.defaultIfNull(tableDOList, Lists.newArrayList());
        Map<Long, StoreTableDO> tableIdToDOMap;
        Map<String, StoreTableDO> tableNoToDOMap;
        Map<String, StoreTableAreaDO> areaNameToDOMap;

        // 准备关联的桌台+区域
        StoreTableAreaDO areaDO;
        StoreTableDO tableDO;

        for(QrCodeDO qrCodeDO : qrCodeDOList) {

            tableIdToDOMap = tableDOList.stream()
                    .collect(Collectors.toMap(StoreTableDO::getId, Function.identity(), (k1, k2) -> k1));

            // 检查点单码是否绑定了当前门店桌台
            Long bindTableId = StringUtils.isBlank(qrCodeDO.getBusinessId()) ? -1 : Long.parseLong(qrCodeDO.getBusinessId());
            tableDO = tableIdToDOMap.get(bindTableId);

            // 有桌台，说明是正常数据
            if(Objects.nonNull(tableDO)){
                continue;
            }

            // 没有找到桌台，说明当时绑定错了，需要新建或绑定到正确的桌台
            String tableNo = "";
            StoreTableDO errorTableDO = storeTableDAO.getByTableId(bindTableId);
            if(Objects.nonNull(errorTableDO)){
                tableNo = errorTableDO.getTableNo();
            }

            // 先按桌台号匹配
            if(StringUtils.isNotBlank(tableNo)){
                tableNoToDOMap = tableDOList.stream()
                        .collect(Collectors.toMap(StoreTableDO::getTableNo, Function.identity(), (k1, k2) -> k1));
                tableDO = tableNoToDOMap.get(tableNo);
            }

            // 匹配不上，走新建逻辑
            if(Objects.isNull(tableDO)){

                // 选定区域
                areaNameToDOMap = areaDOList.stream()
                        .collect(Collectors.toMap(StoreTableAreaDO::getAreaName, Function.identity(), (k1, k2) -> k1));
                areaDO = areaNameToDOMap.get("大厅");
                if(Objects.isNull(areaDO)){
                    areaDO = new StoreTableAreaDO();
                    areaDO.setMerchantId(merchantId);
                    areaDO.setStoreId(storeId);
                    areaDO.setAreaName("大厅");
                    areaDO.setSort(storeTableAreaService.getNextSortValue(merchantId, storeId));
                    storeTableAreaDAO.insert(areaDO);
                    areaDOList.add(areaDO);
                }

                // 生成桌台号
                if(StringUtils.isBlank(tableNo)){
                    Set<String> tmpTableNoSet = tableDOList.stream()
                            .map(StoreTableDO::getTableNo)
                            .collect(Collectors.toSet());
                    for(int startCount = 1; startCount <= 10000; ++startCount){
                        tableNo = "A" + startCount;
                        if(!tmpTableNoSet.contains(tableNo)){
                            break;
                        }
                    }
                }

                // 创建新桌台
                tableDO = new StoreTableDO()
                        .setMerchantId(merchantId)
                        .setStoreId(storeId)
                        .setAreaId(areaDO.getId())
                        .setTableNo(tableNo)
                        .setSort(storeTableService.getNextSortValue(merchantId, storeId))
                        .setBindQrCode(YesNoEnum.Y)
                        .setStatus(TableStatusEnum.FREE);
                storeTableDAO.insert(tableDO);
                tableDOList.add(tableDO);
            }

            // 修改绑定的businessId
            qrCodeMapper.updateBusinessIdByPrimaryKey(qrCodeDO.getId(), String.valueOf(tableDO.getId()));
            successCount++;
        }

        return successCount;
    }


    /**
     * 订正所有点单码数据
     * @param pageSize
     * @return
     */
    @PostMapping(value = "/qrcode/all")
    public Result<Integer> reviseAllQrcode(@RequestParam(value = "pageSize", required = false) Integer pageSize,
                                           @RequestParam(value = "business") Integer business)
    {
        business = Optional.ofNullable(business).orElse(1);
        switch (business) {
            case 1:
                return Result.success(this.reviseAll(pageSize));
            case 2:
                return Result.success(this.reviseWrongQrCodeBindInfo(pageSize));
            default:
                return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), "不合法的业务");
        }
    }

    /**
     * 订正所有点单码数据
     * @param merchantId
     * @return
     */
    @PostMapping(value = "/qrcode/merchant")
    public Result<Integer> reviseMerchantQrcode(@RequestParam(value = "merchantId") String merchantId,
                                                @RequestParam(value = "business") Integer business)
    {
        business = Optional.ofNullable(business).orElse(1);
        switch (business) {
            case 1:
                List<QrCodeMigrateDataDO> dataDOList = qrCodeMigrateDataMapper.listByMerchantId(merchantId);
                return Result.success(this.reviseByMerchant(merchantId, dataDOList));
            case 2:
                List<QrCodeDO> qrCodeDOList = qrCodeMapper.listByMerchantIdAndBusinessType(merchantId, QrCodeBusinessType.TABLE_SCAN_CODE_ORDER);
                return Result.success(this.reviseQrCodeByMerchant(merchantId, qrCodeDOList));
            default:
                return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), "不合法的业务");
        }
    }

    /**
     * 迁移所有数据
     * @return
     */
    private Integer reviseAll(Integer pageSize) {
        if(Objects.isNull(pageSize)){
            pageSize = 1000;
        }
        int pageNumber = 1;
        int totalSuccessCount = 0;

        do {
            // 分页查询需要迁移的数据
            PageHelper.startPage(pageNumber, pageSize);
            List<QrCodeMigrateDataDO> allDataDOList = qrCodeMigrateDataMapper.listAll();
            Page<QrCodeMigrateDataDO> dataDOPage = PageUtil.toPage(allDataDOList, pageSize);
            List<QrCodeMigrateDataDO> dataDOList = dataDOPage.getPageData();
            if(CollectionUtils.isEmpty(dataDOList)){
                break;
            }

            // 按商户分组处理
            Map<String, List<QrCodeMigrateDataDO>> merchantIdToDataListMap = dataDOList.stream()
                    .collect(Collectors.groupingBy(QrCodeMigrateDataDO::getMerchantId));
            for(Map.Entry<String, List<QrCodeMigrateDataDO>> entry : merchantIdToDataListMap.entrySet()){
                String merchantId = entry.getKey();
                List<QrCodeMigrateDataDO> tmpDataDOList = entry.getValue();
                if(CollectionUtils.isEmpty(tmpDataDOList)){
                    continue;
                }
                totalSuccessCount += this.reviseByMerchant(merchantId, tmpDataDOList);
            }
        } while (true);

        return totalSuccessCount;
    }

    /**
     * 按商户迁移数据
     * @param merchantId
     * @param dataList
     * @return
     */
    private Integer reviseByMerchant(String merchantId, List<QrCodeMigrateDataDO> dataList) {
        int successCount = 0;
        if(CollectionUtils.isEmpty(dataList)){
            return successCount;
        }

        Map<String, List<QrCodeMigrateDataDO>> storeIdToDataDoListMap = dataList.stream()
                .collect(Collectors.groupingBy(QrCodeMigrateDataDO::getStoreId));

        // 商户现有的点单码列表
        List<QrCodeDO> qrCodeDOList = qrCodeMapper.listByMerchantIdAndBusinessType(merchantId, QrCodeBusinessType.TABLE_SCAN_CODE_ORDER);
        Map<String, List<QrCodeDO>> storeIdToQrCodeDOListMap = qrCodeDOList.stream()
                .collect(Collectors.groupingBy(QrCodeDO::getStoreId));

        // 商户现有的桌台列表
        List<StoreTableDO> tableDOList = storeTableDAO.listByMerchantId(merchantId);
        Map<String, List<StoreTableDO>> storeIdToTableDOListMap = tableDOList.stream()
                .collect(Collectors.groupingBy(StoreTableDO::getStoreId));

        // 商户现有的区域列表
        List<StoreTableAreaDO> areaDOList = storeTableAreaDAO.listByMerchantId(merchantId);
        Map<String, List<StoreTableAreaDO>> storeIdToAreaDOListMap = areaDOList.stream()
                .collect(Collectors.groupingBy(StoreTableAreaDO::getStoreId));

        for(Map.Entry<String, List<QrCodeMigrateDataDO>> storeEntry : storeIdToDataDoListMap.entrySet()){
            String storeId = storeEntry.getKey();
            List<QrCodeMigrateDataDO> storeDataDOList = storeEntry.getValue();
            LOGGER.info("开始迁移点单码, merchantId:{}, storeId:{}", merchantId, storeId);
            Integer singleSuccessCount = this.reviseByStore(
                    merchantId,
                    storeId,
                    storeDataDOList,
                    storeIdToAreaDOListMap.get(storeId),
                    storeIdToTableDOListMap.get(storeId),
                    storeIdToQrCodeDOListMap.get(storeId));
            successCount += singleSuccessCount;
            LOGGER.info("点单码迁移完成, merchantId:{}, storeId:{}, successCount:{}", merchantId, storeId, singleSuccessCount);
        }

        // 将数据状态置为已迁移
        List<Long> ids = dataList.stream().map(QrCodeMigrateDataDO::getId).collect(Collectors.toList());
        qrCodeMigrateDataMapper.updateStatusById(ids, 1);

        return successCount;
    }


    /**
     * 按门店级迁移数据
     * @param merchantId
     * @param storeId
     * @param dataList
     * @param areaDOList
     * @param tableDOList
     * @param qrCodeDOList
     * @return
     */
    private Integer reviseByStore(String merchantId,
                                  String storeId,
                                  List<QrCodeMigrateDataDO> dataList,
                                  List<StoreTableAreaDO> areaDOList,
                                  List<StoreTableDO> tableDOList,
                                  List<QrCodeDO> qrCodeDOList)
    {
        int successCount = 0;
        if(CollectionUtils.isEmpty(dataList)){
            return successCount;
        }

        areaDOList = ListUtils.defaultIfNull(areaDOList, Lists.newArrayList());
        tableDOList = ListUtils.defaultIfNull(tableDOList, Lists.newArrayList());
        qrCodeDOList = ListUtils.defaultIfNull(qrCodeDOList, Lists.newArrayList());
        Map<String, QrCodeDO> qrCodeToDOMap;
        Map<Long, StoreTableDO> tableIdToDOMap;
        Map<String, StoreTableDO> tableNoToDOMap;
        Map<String, StoreTableAreaDO> areaNameToDOMap;
        int generateTableNoStartCount = 1;
        String qrCodeImageUrl;

        for(QrCodeMigrateDataDO data : dataList) {

            // 校验点单码是否有效
            try {
                QRCodeImageRequest imageRequest = new QRCodeImageRequest();
                imageRequest.setClientSn(data.getQrPrintCode());
                qrCodeImageUrl = qrCodeRemoteService.genQRCodeImage(imageRequest);
                if (StringUtils.isBlank(qrCodeImageUrl)) {
                    continue;
                }
            } catch (Exception ex) {
                LOGGER.error("绑定桌台失败，qrPrintCode：{}", data.getQrPrintCode(), ex);
                continue;
            }

            qrCodeToDOMap = qrCodeDOList.stream()
                    .collect(Collectors.toMap(QrCodeDO::getQrCodeNo, Function.identity(), (k1, k2) -> k1));

            // 准备关联的桌台+区域信息
            StoreTableAreaDO areaDO;
            StoreTableDO tableDO = null;

            // 校验点单码是否已有关联关系
            QrCodeDO tmpQrCodeDO = qrCodeToDOMap.get(data.getQrCode());
            if(Objects.nonNull(tmpQrCodeDO)){
                if(StringUtils.isNotBlank(tmpQrCodeDO.getBusinessId())){

                    tableIdToDOMap = tableDOList.stream()
                            .collect(Collectors.toMap(StoreTableDO::getId, Function.identity(), (k1, k2) -> k1));

                    StoreTableDO tmpTableDO = tableIdToDOMap.get(Long.valueOf(tmpQrCodeDO.getBusinessId()));
                    if(Objects.nonNull(tmpTableDO)){
                        successCount++;
                        continue;
                    } else {
                        qrCodeMapper.deleteByPrimaryKey(tmpQrCodeDO.getId());
                    }
                } else {
                    qrCodeMapper.deleteByPrimaryKey(tmpQrCodeDO.getId());
                }
            }

            // 校验是否有同名的桌台，有则直接关联
            if(StringUtils.isNotBlank(data.getName())){

                tableNoToDOMap = tableDOList.stream()
                        .collect(Collectors.toMap(StoreTableDO::getTableNo, Function.identity(), (k1, k2) -> k1));

                StoreTableDO tmpTableDO = tableNoToDOMap.get(data.getName());
                if(Objects.nonNull(tmpTableDO)){
                    tableDO = tmpTableDO;
                }
            }

            // 没有同名的桌台，新建
            if(Objects.isNull(tableDO)){

                areaNameToDOMap = areaDOList.stream()
                        .collect(Collectors.toMap(StoreTableAreaDO::getAreaName, Function.identity(), (k1, k2) -> k1));

                // 桌台默认所属区域"大厅"，没有则新建
                StoreTableAreaDO tmpAreaDO = areaNameToDOMap.get("大厅");
                if(Objects.isNull(tmpAreaDO)){
                    StoreTableAreaDO newAreaDO = new StoreTableAreaDO();
                    newAreaDO.setMerchantId(merchantId);
                    newAreaDO.setStoreId(storeId);
                    newAreaDO.setAreaName("大厅");
                    newAreaDO.setSort(storeTableAreaService.getNextSortValue(merchantId, storeId));
                    newAreaDO.setCreateBy(data.getUserId());
                    newAreaDO.setModifiedBy(data.getUserId());
                    storeTableAreaDAO.insert(newAreaDO);
                    areaDO = newAreaDO;
                    areaDOList.add(areaDO);
                } else {
                    areaDO = tmpAreaDO;
                }

                // 如果没传入名称，默认在"大厅"下以A开头创建桌台
                String tableNo = data.getName();
                Set<String> tmpTableNoSet = tableDOList.stream()
                        .map(StoreTableDO::getTableNo)
                        .collect(Collectors.toSet());
                if(StringUtils.isBlank(tableNo)){
                    String tmpTableNo;
                    while (generateTableNoStartCount > 0) {
                        tmpTableNo = "A" + generateTableNoStartCount;
                        if(!tmpTableNoSet.contains(tmpTableNo)){
                            tableNo = tmpTableNo;
                            break;
                        }
                        generateTableNoStartCount++;
                    }
                }
                StoreTableDO newTableDO = new StoreTableDO()
                        .setMerchantId(merchantId)
                        .setStoreId(storeId)
                        .setAreaId(areaDO.getId())
                        .setTableNo(tableNo)
                        .setSort(storeTableService.getNextSortValue(merchantId, storeId))
                        .setBindQrCode(YesNoEnum.Y)
                        .setStatus(TableStatusEnum.FREE)
                        .setCreateBy(data.getUserId())
                        .setModifiedBy(data.getUserId());
                storeTableDAO.insert(newTableDO);
                tableDOList.add(newTableDO);
                tableDO = newTableDO;
            }

            // 保存关联信息
            QrCodeDO newQrCodeDO = new QrCodeDO();
            newQrCodeDO.setMerchantId(merchantId);
            newQrCodeDO.setStoreId(storeId);
            newQrCodeDO.setQrCodeType(RemoteQrCodeType.TABLE);
            newQrCodeDO.setQrCodeNo(data.getQrCode());
            newQrCodeDO.setShowQrCodeNo(data.getQrPrintCode());
            newQrCodeDO.setBusinessId(String.valueOf(tableDO.getId()));
            newQrCodeDO.setBusinessType(QrCodeBusinessType.TABLE_SCAN_CODE_ORDER);
            newQrCodeDO.setStatus(QrCodeStatus.BIND);
            newQrCodeDO.setDeleteMark(YesNoEnum.N);
            newQrCodeDO.setBindTime(DateUtils.getCurrentTime());
            newQrCodeDO.setGmtCreate(DateUtils.getCurrentTime());
            newQrCodeDO.setGmtModified(DateUtils.getCurrentTime());
            newQrCodeDO.setQrCodeImageUrl(qrCodeImageUrl);
            qrCodeMapper.insert(newQrCodeDO);

            successCount++;
        }

        return successCount;
    }

    /**
     * 订正打印机配置的tag
     * @param merchantId
     * @return
     */
    @PostMapping(value = "/printer/config/v2")
    public Result<Integer> revisePrinterConfigV2(@RequestParam(value = "merchantId", required = false) String merchantId) {

        int successCount = 0;
        List<StorePrinterConfigDO> allConfigList;

        // 指定需要订正的数据
        if(StringUtils.isNotBlank(merchantId)){
            allConfigList = storePrinterConfigMapper.listByMerchantId(merchantId);
        } else {
            allConfigList = storePrinterConfigMapper.listAll();
        }
        if(CollectionUtils.isEmpty(allConfigList)){
            return Result.success(successCount);
        }

        // 采用逐个订正的方法
        for(StorePrinterConfigDO config : allConfigList){

            // 防止订正正确的数据
            long tag = Optional.ofNullable(config.getTag()).orElse(0L);
            String sceneString = config.getSceneType();

            if(StringUtils.isNotBlank(sceneString)){
                List<PrintSceneTypeEnum> sceneList = Arrays.stream(StringUtils.split(config.getSceneType(), ","))
                        .map(p -> EnumUtils.getEnum(PrintSceneTypeEnum.class, p))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                if (sceneList.contains(PrintSceneTypeEnum.KITCHEN_PRINT)) {
                    tag = TagUtil.addTag(tag, PrinterConfigTagEnum.PRINT_BACK_KITCHEN_RECEIPT.getValue());
                }
                if (sceneList.contains(PrintSceneTypeEnum.FRONT_PRINT)) {
                    tag = TagUtil.addTag(tag, PrinterConfigTagEnum.PRINT_CHECKOUT_RECEIPT.getValue());

                    // 需要额外判断门店的业务模式
                    FindConfigByNameRequest configRequest = new FindConfigByNameRequest();
                    configRequest.setAppId(AppId.UFOOD.getAppId());
                    configRequest.setOwnerType(OwnerType.STORE_ID.getOwnerType());
                    configRequest.setOwnerId(config.getStoreId());
                    configRequest.setName(MccConfigEnum.MEAL_TYPE.getName());
                    ConfigResponse configResponse = configRemoteService.findByName(configRequest);
                    String configValue = Optional.ofNullable(configResponse).map(ConfigResponse::getValue).orElse(null);

                    // 如果是围餐模式，打开点菜单开关
                    if(StringUtils.equals(configValue, "round_meal")) {
                        tag = TagUtil.addTag(tag, PrinterConfigTagEnum.PRINT_POINT_MENU_RECEIPT.getValue());
                        config.setPointMenuReceiptPrintNum(1);
                        config.setPrintAreaRange(PrintRangeEnum.ALL);
                    }
                }
            }

            // 放入订正值
            config.setTag(tag);
            boolean updateSuc = storePrinterConfigMapper.updateByPrimaryKey(config) > 0;
            if(updateSuc){
                successCount++;
            }
        }

        return Result.success(successCount);
    }


    //== 订正绑定了桌台的营销码 ==//

    private static final int MARKET_CODE_WAIT_REVISE_CODE = 3;

    private static final int MARKET_CODE_REVISE_SUCCESS_CODE = 4;

    @PostMapping(value = "/market/code")
    public Result<Integer> reviseMarketCode(@RequestParam(value = "merchantId", required = false) String merchantId,
                                            @RequestParam(value = "pageSize", required = false) Integer pageSize) {
        int successCount = 0;
        if(StringUtils.isNotBlank(merchantId)) {
            successCount += this.reviseMarketCodeByMerchant(merchantId);
        } else {
            successCount += this.reviseMarketCodesAll(pageSize);
        }
        return Result.success(successCount);
    }

    private Integer deleteMarketCodeRecords(List<QrCodeMigrateDataDO> data) {
        if(CollectionUtils.isEmpty(data)) {
            return 0;
        }
        List<String> deleteQrCodes = data.stream()
                .map(QrCodeMigrateDataDO::getQrCode)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        int deleteSuccessCount = qrCodeMapper.deleteByQrCodeNos(deleteQrCodes);

        // 订正的数据转态改变
        List<Long> ids = data.stream().map(QrCodeMigrateDataDO::getId).collect(Collectors.toList());
        qrCodeMigrateDataMapper.updateStatusById(ids, MARKET_CODE_REVISE_SUCCESS_CODE);

        return deleteSuccessCount;
    }

    private Integer reviseMarketCodeByMerchant(String merchantId) {
        if(StringUtils.isBlank(merchantId)) {
            return 0;
        }
        List<QrCodeMigrateDataDO> reviseData = qrCodeMigrateDataMapper.listByMerchantIdAndStatus(merchantId, MARKET_CODE_WAIT_REVISE_CODE);
        if(CollectionUtils.isEmpty(reviseData)) {
            return 0;
        }
        return this.deleteMarketCodeRecords(reviseData);
    }

    private Integer reviseMarketCodesAll(Integer pageSize) {
        pageSize = Optional.ofNullable(pageSize).orElse(1000);
        int successCount = 0;
        LOGGER.info("start delete non table qrcode records, startTime:{}", System.currentTimeMillis());
        do {
            List<QrCodeMigrateDataDO> reviseData = qrCodeMigrateDataMapper.listByStatusAndLimit(MARKET_CODE_WAIT_REVISE_CODE, pageSize);
            if(CollectionUtils.isEmpty(reviseData)) {
                break;
            }
            int deleteCount = this.deleteMarketCodeRecords(reviseData);
            LOGGER.info("batch query count:{}, delete count:{}", reviseData.size(), deleteCount);
            successCount += deleteCount;
        } while (true);
        LOGGER.info("delete non table qrcode records finish, finishTime:{}, delete total count:{}", System.currentTimeMillis(), successCount);
        return successCount;
    }


}
