package com.wosai.smartbiz.controller.uc;

import com.wosai.smartbiz.base.enums.SceneEnum;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.user.user.constants.UcConstants;
import com.wosai.smartbiz.user.user.enums.UcErrorCodeEnum;
import com.wosai.smartbiz.uc.services.LoginV2Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


@RestController
@RequestMapping("/logout")
public class LogoutController extends BaseController {

    @Autowired
    private LoginV2Service loginV2Service;

    @RequestMapping(value = "v2/cashier", method = RequestMethod.POST)
    public Result<Boolean> logoutCashierV2(@RequestParam(value = "storeId") String storeId,
                                           @RequestParam(value = "merchantId") String merchantId,
                                     HttpServletRequest request,
                                     HttpServletResponse response){
        String authorization = getRequestAuthorization(request);
        if(StringUtils.isBlank(authorization)){
            return Result.error(UcErrorCodeEnum.ACCESS_TOKEN_INVALID);
        }
        if(!authorization.startsWith(UcConstants.RequestConstants.AUTHORIZATION_SCHAME)){
            return Result.error(UcErrorCodeEnum.ACCESS_TOKEN_INVALID);
        }
        String accessToken = authorization.substring(7);
        String deviceFp = getRequestTerminalSn(request);
        return loginV2Service.logout(accessToken, merchantId, storeId, deviceFp, SceneEnum.RESTAURANT);
    }
}
