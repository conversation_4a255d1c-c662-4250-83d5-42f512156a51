package com.wosai.smartbiz.controller.tv;

import com.alibaba.fastjson.JSON;
import com.wosai.smartbiz.gds.service.StoreTvRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping(value = "/tv")
@Slf4j
public class TvWebHookController {

    @Autowired
    private StoreTvRpcService storeTvRpcService;

    @ResponseBody
    @RequestMapping(value = "/hook", method = RequestMethod.POST)
    public boolean tvWebHook(@RequestBody String body){
        if(StringUtils.isBlank(body)){
            return false;
        }
        Map<String, String> bodyMap = JSON.parseObject(body, Map.class);
        return storeTvRpcService.tvWebHook(bodyMap);
    }

}
