package com.wosai.smartbiz.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/11/22.
 * <p>
 * 注意: 不要、不要、千万不要删除，这个是作为接入bingo的必要条件
 */
@RestController
@RequestMapping("")
public class HealthyController {
    /**
     * 健康检查接口
     */
    @RequestMapping(value = "/check", method = RequestMethod.GET)
    public
    @ResponseBody
    String check() {
        return "success";
    }

    /**
     * 健康检查接口
     */
    @RequestMapping(value = "/health", method = RequestMethod.GET)
    public
    @ResponseBody
    boolean health() {
        return true;
    }
}