package com.wosai.smartbiz.controller.uc.cashier;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.controller.gds.BaseController;
import com.wosai.smartbiz.user.user.dto.req.MainCashierBaseReq;
import com.wosai.smartbiz.user.user.dto.req.MainCashierHealthCheckReq;
import com.wosai.smartbiz.user.user.dto.req.MainCashierRegisterReq;
import com.wosai.smartbiz.user.user.dto.req.MainCashierUntieAndRegisterReq;
import com.wosai.smartbiz.user.user.dto.vo.MainCashierCheckOnlineVO;
import com.wosai.smartbiz.user.user.dto.vo.MainCashierRegisterResp;
import com.wosai.smartbiz.user.user.services.MainCashierRegisterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * @author: KangYiJun
 * @date: 2020-08-21
 */
@RestController
@RequestMapping(value = "/cashier/main/cashier")
public class MainCashierController extends BaseController {

    @Autowired
    private MainCashierRegisterService mainCashierRegisterService;

    /**
     * 注册/获取主机信息
     * @param req
     * @return
     */
    @PostMapping(value = "/getOrRegister")
    public Result<MainCashierRegisterResp> register(@Valid MainCashierRegisterReq req,
                                                    BindingResult bindingResult,
                                                    HttpServletRequest request)
    {
        if(bindingResult.hasErrors()){
            processErrorResult(bindingResult);
        }
        req.setMqttClientSn(getRequestTerminalMqttSn(request));
        return tryCatch(() -> mainCashierRegisterService.register(req));
    }

    /**
     * 解绑/绑定新的主机
     * @param req
     * @return
     */
    @PostMapping(value = "/untieAndRegister")
    public Result<MainCashierRegisterResp> untieAndRegister(@Valid MainCashierUntieAndRegisterReq req,
                                                            BindingResult bindingResult,
                                                            HttpServletRequest request)
    {
        if(bindingResult.hasErrors()){
            processErrorResult(bindingResult);
        }
        req.setMqttClientSn(getRequestTerminalMqttSn(request));
        return tryCatch(() -> mainCashierRegisterService.untieAndRegister(req));
    }

    /**
     * 心跳检查
     * @param req
     * @return
     */
    @PostMapping(value = "/health/check")
    public Result<Boolean> healthCheck(@Valid MainCashierHealthCheckReq req,
                                       BindingResult bindingResult,
                                       HttpServletRequest request){
        return healthCheck4Sentinel(req, bindingResult, request);
    }

    /**
     * 检查主收银是否在线
     * @param req
     * @param bindingResult
     * @return
     */
    @PostMapping(value = "/check/online")
    public Result<MainCashierCheckOnlineVO> checkMainCashierOnline(@Valid MainCashierBaseReq req,
                                                                   BindingResult bindingResult)
    {
        if(bindingResult.hasErrors()){
            processErrorResult(bindingResult);
        }
        return tryCatch(() -> mainCashierRegisterService.checkMainCashierOnline(req));
    }



    @SentinelResource(value = "smart-biz/goods-center/cashier/main/cashier/health/check")
    private Result<Boolean> healthCheck4Sentinel(MainCashierHealthCheckReq req,
                                                 BindingResult bindingResult,
                                                 HttpServletRequest request){
        if(bindingResult.hasErrors()){
            processErrorResult(bindingResult);
        }
        req.setMqttClientSn(getRequestTerminalMqttSn(request));
        return tryCatch(() -> mainCashierRegisterService.healthCheck(req));
    }
}
