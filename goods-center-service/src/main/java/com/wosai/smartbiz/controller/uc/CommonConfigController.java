package com.wosai.smartbiz.controller.uc;


import com.wosai.market.mcc.api.dto.response.ConfigResponse;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.dto.req.ConfigRemoteReq;
import com.wosai.smartbiz.gds.enums.ErrorCodeEnum;
import com.wosai.smartbiz.goods.util.ParamUtils;
import com.wosai.smartbiz.gds.service.CommonConfigService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/common/config")
public class CommonConfigController {

    @Autowired
    private CommonConfigService commonConfigService;

    /**
     * 配置保存
     * @param configRemoteReq
     * @return
     */
    @PostMapping("/save")
    public Result<Boolean> save(ConfigRemoteReq configRemoteReq){


        return commonConfigService.addOrEditConfig(configRemoteReq.getOwnerId(), configRemoteReq.getName(), configRemoteReq.getValue());
    }

    /**
     * 配置删除
     * @param configRemoteReq
     * @return
     */
    @PostMapping("/del")
    public Result<Boolean> del(ConfigRemoteReq configRemoteReq){



        return commonConfigService.delConfig(configRemoteReq.getOwnerId(), configRemoteReq.getName());
    }

    /**
     * 获取单个配置
     * @param configRemoteReq
     * @return
     */
    @PostMapping("/get/detail")
    public Result<ConfigResponse> getDetail(ConfigRemoteReq configRemoteReq){



        return commonConfigService.getConfigResponse(configRemoteReq.getOwnerId(), configRemoteReq.getName());
    }

    /**
     * 获取单个String类型配置
     * @param configRemoteReq
     * @return
     */
    @PostMapping("/get/string/detail")
    public Result<String> getStringDetail(ConfigRemoteReq configRemoteReq){



        return commonConfigService.getStringConfigWithDefaultValue(configRemoteReq.getOwnerId(), configRemoteReq.getName());
    }

    /**
     * 获取单个boolean类型配置
     * @param configRemoteReq
     * @return
     */
    @PostMapping("/get/boolean/detail")
    public Result<Boolean> getBooleanDetail(ConfigRemoteReq configRemoteReq){


        return commonConfigService.getCheckConfigWithDefaultValue(configRemoteReq.getOwnerId(), configRemoteReq.getName());
    }

    /**
     * 获取单个Long类型配置
     * @param configRemoteReq
     * @return
     */
    @PostMapping("/get/long/detail")
    public Result<Long> getLongDetail(ConfigRemoteReq configRemoteReq){



        return commonConfigService.getLongConfigWithDefaultValue(configRemoteReq.getOwnerId(), configRemoteReq.getName());
    }



    /**
     * 配置批量保存
     * @param configRemoteReqs
     * @param ownerId
     * @return
     */
    @PostMapping("/save/batch")
    public Result<Boolean> save(String ownerId, String configRemoteReqs){

        if(StringUtils.isEmpty(configRemoteReqs)){
            //参数为空返回错误
            return Result.error(ErrorCodeEnum.BUSINESS_ERROR);
        }

        List<ConfigRemoteReq> configRemoteReqList = new ArrayList<>();

        try{
            configRemoteReqList = ParamUtils.parseJsonArray(configRemoteReqs, ConfigRemoteReq.class);
        }catch (Exception e){
            //转化失败，返回错误
            return Result.error(ErrorCodeEnum.BUSINESS_ERROR);
        }



        return commonConfigService.batchSave(ownerId, configRemoteReqList);
    }


}
