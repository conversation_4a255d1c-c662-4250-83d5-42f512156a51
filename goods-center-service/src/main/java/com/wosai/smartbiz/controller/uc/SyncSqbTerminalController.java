package com.wosai.smartbiz.controller.uc;

import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.service.StoreService;
import com.wosai.smartbiz.base.enums.SceneEnum;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.gds.enums.ErrorCodeEnum;
import com.wosai.smartbiz.uc.dto.PaymentTerminalDTO;
import com.wosai.smartbiz.uc.manager.UserTerminalService;
import com.wosai.smartbiz.user.user.login.DeviceParam;
import com.wosai.upay.core.service.TerminalService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * @author: redhat
 */
@RestController
@RequestMapping("/sync/terminal")
public class SyncSqbTerminalController extends BaseController {

    @Value("${vendor.vendor-app-appid}")
    private String vendorAppAppid;

    @Autowired
    private TerminalService sqbCoreBusinessTerminalService;

    @Autowired
    private UserTerminalService userTerminalService;

    @Autowired
    private StoreService sqbStoreService;

    @RequestMapping(value = "/get/by/sn", method = RequestMethod.POST)
    public Result<Map> getSqbTerminalBySn(@RequestParam(value="terminalSn", required = false) String terminalSn,
                                          HttpServletRequest request, HttpServletResponse response){
        if(StringUtils.isBlank(terminalSn)){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getCode(), "terminalSn参数必传");
        }
        return Result.success(sqbCoreBusinessTerminalService.getTerminalBySn(terminalSn));
    }

    @RequestMapping(value = "/get/by/fp", method = RequestMethod.POST)
    public Result<Map> getSqbTerminalByFp(@RequestParam(value="terminalFp", required = false) String terminalFp,
                                          HttpServletRequest request, HttpServletResponse response){
        if(StringUtils.isBlank(terminalFp)){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getCode(), "terminalFp参数必传");
        }
        return Result.success(sqbCoreBusinessTerminalService.getTerminalByDeviceFingerprint(terminalFp));
    }

    @RequestMapping(value = "/get/by/vendorIdAndFp", method = RequestMethod.POST)
    public Result<Map> getSqbTerminalByVendorIdAndFp(@RequestParam(value="terminalFp", required = false) String terminalFp,
                                          HttpServletRequest request, HttpServletResponse response){
        if(StringUtils.isBlank(terminalFp)){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getCode(), "terminalFp参数必传");
        }
        return Result.success(sqbCoreBusinessTerminalService.getTerminalByDeviceFp(vendorAppAppid, terminalFp));
    }

    @RequestMapping(value = "/get/secret", method = RequestMethod.POST)
    public Result<Map> getSqbTerminalSecretBySn(@RequestParam(value="terminalSn", required = false) String terminalSn,
                                          HttpServletRequest request, HttpServletResponse response){
        if(StringUtils.isBlank(terminalSn)){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getCode(), "terminalSn参数必传");
        }
        return Result.success(sqbCoreBusinessTerminalService.getSecret(terminalSn));
    }

    @RequestMapping(value = "/sync/cashier/sn", method = RequestMethod.POST)
    public Result<PaymentTerminalDTO> syncStoreCashierTerminalSn(@RequestParam(value="storeId", required = false) String storeId,
                                                                 @RequestParam(value="storeCode", required = false) String storeCode,
                                                                 @RequestParam(value="deviceScene", required = false) SceneEnum deviceScene,
                                                             HttpServletRequest request, HttpServletResponse response){
        if(StringUtils.isBlank(storeId) && StringUtils.isBlank(storeCode)){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getCode(), "门店id或者门店sn必传");
        }
        StoreInfo storeInfo = null;
        if(StringUtils.isNotBlank(storeId)){
            storeInfo = sqbStoreService.getStoreById(storeId, null);
        }else {
            storeInfo = sqbStoreService.getStoreBySn(storeCode, null);
        }
        if(storeInfo == null){
            return Result.error(ErrorCodeEnum.SYSTEM_ERROR.getCode(), "门店信息不存在");
        }
        if(deviceScene == null){
            return Result.error(ErrorCodeEnum.SYSTEM_ERROR.getCode(), "设备使用场景不能为空");
        }

        DeviceParam deviceParam = new DeviceParam();
        deviceParam.setDeviceScene(deviceScene);

        PaymentTerminalDTO paymentTerminalDTO = userTerminalService.getPaymentTerminalInfoByStoreId(storeInfo.getId(), storeInfo.getSn(), storeInfo.getName(), deviceParam);
        return Result.success(paymentTerminalDTO);
    }

    @RequestMapping(value = "/sync/jjz/store_code", method = RequestMethod.POST)
    public Result<PaymentTerminalDTO> syncStoreJjzTerminalSn(@RequestParam(value="storeCode", required = false) String storeCode,
                                                             HttpServletRequest request, HttpServletResponse response){
        if(StringUtils.isBlank(storeCode)){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getCode(), "门店sn必传");
        }
        StoreInfo storeInfo = sqbStoreService.getStoreBySn(storeCode, null);
        if(storeInfo == null){
            return Result.error(ErrorCodeEnum.SYSTEM_ERROR.getCode(), "门店信息不存在");
        }
        DeviceParam deviceParam = new DeviceParam();
        deviceParam.setDeviceScene(SceneEnum.RESTAURANT);

        PaymentTerminalDTO paymentTerminalDTO = userTerminalService.syncJjzTerminal(storeInfo.getId(), storeInfo.getSn(), storeInfo.getName(), deviceParam);
        return Result.success(paymentTerminalDTO);
    }
}
