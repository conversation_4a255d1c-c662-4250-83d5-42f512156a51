package com.wosai.smartbiz.controller.rmqx;

import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.gds.dto.tv.TvBaseDTO;
import com.wosai.smartbiz.gds.enums.ErrorCodeEnum;
import com.wosai.smartbiz.gds.vo.DeviceMqttConfigVO;
import com.wosai.smartbiz.message.MqttConfig;
import com.wosai.smartbiz.utils.RmqxUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * RMQX 消息服务器鉴权接口
 */
@RestController
@RequestMapping(value = "/rmqx/v1")
@Slf4j
public class RmqxConfigController {

    @Autowired
    private MqttConfig mqttConfig;
    @Autowired
    private RmqxUtil rmqxUtil;

    /**
     * 获取TV设备Mqtt配置
     * @param body
     * @return
     */
    @PostMapping("/tv/config")
    public Result<DeviceMqttConfigVO> tvMqttConfig(TvBaseDTO body) {
        if(null == body){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR);
        }
        if(StringUtils.isBlank(body.getSn())){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), "设备SN不能为空");
        }
        DeviceMqttConfigVO mqttConfigVO = new DeviceMqttConfigVO();
        mqttConfigVO.setHost(mqttConfig.getHost());
        mqttConfigVO.setDeviceClientIdPrefix(mqttConfig.getTvClientIdPrefix());
        mqttConfigVO.setUser(mqttConfig.getUser());
        mqttConfigVO.setPwd(mqttConfig.getPwd());
        mqttConfigVO.setSuperuser(mqttConfig.getSuperuser());
        mqttConfigVO.setQos(mqttConfig.getQos());

        mqttConfigVO.setPubs(rmqxUtil.getTvPubsPrefix());
        mqttConfigVO.setSubs(rmqxUtil.getTvSubsPrefix());

        return Result.success(mqttConfigVO);
    }


    /**
     * 获取收银机设备Mqtt配置
     * @param body
     * @return
     */
    @PostMapping("/cashier/config")
    public Result<DeviceMqttConfigVO> cashierMqttConfig(TvBaseDTO body) {
        if(null == body){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR);
        }
        if(StringUtils.isBlank(body.getSn())){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), "设备SN不能为空");
        }
        DeviceMqttConfigVO mqttConfigVO = new DeviceMqttConfigVO();
        mqttConfigVO.setHost(mqttConfig.getHost());
        mqttConfigVO.setDeviceClientIdPrefix(mqttConfig.getCashierClientIdPrefix());
        mqttConfigVO.setUser(mqttConfig.getUser());
        mqttConfigVO.setPwd(mqttConfig.getPwd());
        mqttConfigVO.setSuperuser(mqttConfig.getSuperuser());
        mqttConfigVO.setQos(mqttConfig.getQos());

        mqttConfigVO.setPubs(rmqxUtil.getCashierPubsPrefix());
        mqttConfigVO.setSubs(rmqxUtil.getCashierSubsPrefix());

        return Result.success(mqttConfigVO);
    }
}
