package com.wosai.smartbiz.controller.uc.repast;

import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.controller.gds.BaseController;
import com.wosai.smartbiz.converter.gds.StorePrinterConfigConverter;
import com.wosai.smartbiz.uc.manager.StorePrinterConfigService;
import com.wosai.smartbiz.user.user.dto.StorePrinterConfigDTO;
import com.wosai.smartbiz.user.user.vo.printer.v2.StorePrinterConfigVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021/3/29
 */
@RestController
@RequestMapping(value = "/cashier/v2/store/printer")
public class StorePrinterControllerV2 extends BaseController {

    @Autowired
    private StorePrinterConfigService storePrinterConfigService;

    /**
     * 打印机配置列表
     * @param merchantId
     * @param storeId
     * @return
     */
    @PostMapping(value = "/list")
    public Result<List<StorePrinterConfigVO>> list(@RequestParam(value = "merchantId") String merchantId,
                                                   @RequestParam(value = "storeId") String storeId)
    {
        return tryCatch(() -> {

            List<StorePrinterConfigDTO> configDTOList = storePrinterConfigService.listConfigByStoreId(merchantId, storeId);
            if(CollectionUtils.isEmpty(configDTOList)){
                return Collections.emptyList();
            }

            return configDTOList.stream()
                    .map(StorePrinterConfigConverter::toCashierVO)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        });
    }

}
