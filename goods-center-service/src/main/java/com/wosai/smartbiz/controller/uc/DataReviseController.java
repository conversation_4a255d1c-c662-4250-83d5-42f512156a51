package com.wosai.smartbiz.controller.uc;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.wosai.databus.event.bean.RefundOrderItem;
import com.wosai.databus.event.oms.DingAuditSyncEvent;
import com.wosai.databus.event.order.basic.FTOrderBasicRefundEvent;
import com.wosai.smartbiz.base.enums.YesNoEnum;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.gds.enums.*;
import com.wosai.smartbiz.gds.request.StoreDeviceWhitelistCreateRequest;
import com.wosai.smartbiz.gds.service.CustomerRelationService;
import com.wosai.smartbiz.gds.service.MccConfigHelperService;
import com.wosai.smartbiz.gds.service.StoreDeviceWhitelistRpcService;
import com.wosai.smartbiz.goods.direct.TvDeviceDAO;
import com.wosai.smartbiz.goods.domain.StoreDeviceActiveDO;
import com.wosai.smartbiz.goods.domain.TvDeviceDO;
import com.wosai.smartbiz.kafka.KafkaConsumer;
import com.wosai.smartbiz.uc.dao.StorePrinterConfigMapper;
import com.wosai.smartbiz.uc.dao.StorePrinterConfigXrefMapper;
import com.wosai.smartbiz.uc.dao.UserLoginHistoryMapper;
import com.wosai.smartbiz.uc.domain.UcStorePrinterConfigXrefDO;
import com.wosai.smartbiz.uc.manager.QrCodeService;
import com.wosai.smartbiz.uc.manager.StoreDeviceActiveService;
import com.wosai.smartbiz.uc.manager.StoreV2Service;
import com.wosai.smartbiz.uc.utils.common.DateUtils;
import com.wosai.smartbiz.user.user.domain.StorePrinterConfigDO;
import com.wosai.smartbiz.user.user.enums.print.PrintRangeEnum;
import com.wosai.smartbiz.user.user.enums.print.PrinterXrefTypeEnum;
import com.wosai.smartbiz.user.user.vo.CashierStoreVO;
import com.wosai.smartbiz.user.user.vo.StoreV2VO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/data/revise")
@Slf4j
public class DataReviseController extends BaseController{

    private static final Logger LOGGER = LoggerFactory.getLogger(DataReviseController.class);

    @Autowired
    private StorePrinterConfigMapper storePrinterConfigMapper;

    @Autowired
    private StorePrinterConfigXrefMapper storePrinterConfigXrefMapper;

    @Autowired
    private QrCodeService qrCodeService;

    @Autowired
    private TvDeviceDAO tvDeviceDAO;

    @Autowired
    private StoreV2Service storeV2Service;
    @Autowired
    private MccConfigHelperService mccConfigHelperService;
    @Autowired
    private StoreDeviceWhitelistRpcService storeDeviceWhitelistRpcService;
    @Autowired
    private StoreDeviceActiveService storeDeviceActiveService;
    @Autowired
    UserLoginHistoryMapper userLoginHistoryMapper;
    @Autowired
    private KafkaConsumer kafkaConsumer;
    @Autowired
    private CustomerRelationService customerRelationService;

    /**
     * 订正TV设备信息表中的商户信息、门店信息
     * @return
     */
    @PostMapping(value = "/tv/device/store_sn")
    public Result<Boolean> reviseTvDeviceStoreSn(@RequestParam(value = "merchantIds") String merchantIds) {
        List<String> merchantIdList = Arrays.asList(StringUtils.split( merchantIds, ","));
        if(org.apache.commons.collections.CollectionUtils.isEmpty(merchantIdList)){
            Result.error(ErrorCodeEnum.PARAMETER_ERROR, "商户ID不能为空");
        }
        List<TvDeviceDO> list = tvDeviceDAO.listByMerchantIds(merchantIdList);
        if(CollectionUtils.isNotEmpty(list)){
            list.stream().forEach(tvDeviceDO -> {
                if(StringUtils.isNotBlank(tvDeviceDO.getStoreId())){
                    StoreV2VO storeV2VO = storeV2Service.getStoreByStoreId(tvDeviceDO.getStoreId());
                    if(null != storeV2VO){
                        TvDeviceDO updateTvDeviceDO = new TvDeviceDO();
                        updateTvDeviceDO.setId(tvDeviceDO.getId());
                        updateTvDeviceDO.setStoreId(storeV2VO.getStoreId());
                        updateTvDeviceDO.setStoreSn(storeV2VO.getStoreCode());
                        updateTvDeviceDO.setMerchantId(storeV2VO.getMerchantId());
                        updateTvDeviceDO.setMerchantSn(storeV2VO.getMerchantCode());
                        int row = tvDeviceDAO.updateById(updateTvDeviceDO);
                        log.info("DataReviseController.reviseTvDeviceStoreSn success, sn:{}", tvDeviceDO.getSn());
                    }
                }
            });
        }
        return Result.success(true);
    }

    /**
     * 解绑历史的点单码
     * @param oldMerchantId
     * @param oldStoreId
     * @param newMerchantId
     * @param newStoreId
     * @return
     */
    @GetMapping(value = "/unbind/old/qrcode")
    public int unbindOldQrCodes(@RequestParam(value = "oldMerchantId") String oldMerchantId,
                                @RequestParam(value = "oldStoreId") String oldStoreId,
                                @RequestParam(value = "newMerchantId") String newMerchantId,
                                @RequestParam(value = "newStoreId", required = false) String newStoreId,
                                @RequestParam(value = "operatorId") String operatorId) {
        return qrCodeService.unbindAll(oldMerchantId, oldStoreId, newMerchantId, newStoreId, operatorId);
    }

    /**
     * 订正打印机分类配置
     * @return
     */
    @PostMapping(value = "/printer/config/category")
    public Result<String> revisePrinterConfig(){
        try {

            LOGGER.info("=== 1.start to revise printer config ===");

            List<StorePrinterConfigDO> reviseConfigList = storePrinterConfigMapper.selectList4Revise();
            if(org.apache.commons.collections4.CollectionUtils.isEmpty(reviseConfigList)){
                return Result.error(ErrorCodeEnum.BUSINESS_ERROR.getErrCode(), "没有可订正的数据");
            }

            // 组装订正的关联数据
            List<UcStorePrinterConfigXrefDO> insertXrefList = Lists.newArrayList();
            for(StorePrinterConfigDO reviseConfig : reviseConfigList){

                String printCategoryIds = reviseConfig.getGoodsCategoryIds();
                if(StringUtils.isBlank(printCategoryIds)){
                    continue;
                }

                List<String> printCategoryIdList = Lists.newArrayList(StringUtils.split(printCategoryIds, ","));

                if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(printCategoryIdList)){
                    insertXrefList.addAll(printCategoryIdList.stream().map(p -> {
                        UcStorePrinterConfigXrefDO tmpXref = new UcStorePrinterConfigXrefDO();
                        tmpXref.setConfigId(reviseConfig.getId());
                        tmpXref.setXrefType(PrinterXrefTypeEnum.CATEGORY);
                        tmpXref.setXrefId(p);
                        return tmpXref;
                    }).collect(Collectors.toList()));
                }
            }

            List<Long> reviseConfigIdList = reviseConfigList.stream()
                    .map(StorePrinterConfigDO::getId)
                    .collect(Collectors.toList());
            storePrinterConfigMapper.updateGoodCategoryIds4Revise(reviseConfigIdList, PrintRangeEnum.PART);

            storePrinterConfigXrefMapper.insertBatch(insertXrefList);

            LOGGER.info("=== 2.revise printer config finish ===");
            return Result.success("订正成功");

        } catch (Exception ex){
            LOGGER.error("printer config revise data error", ex);
            return Result.error(ErrorCodeEnum.SYSTEM_ERROR.getErrCode(), ex.getMessage());
        }
    }



    /**
     * 订正门店绑定设备同步到MCC
     * @return
     */
    @PostMapping(value = "/store/bind/device/sync/mcc")
    public Result<String> storeBindDeviceSyncMcc(@RequestParam(value = "storeIds") String storeIds) {
        try {
            List<String> storeIdList = Arrays.asList(StringUtils.split( storeIds, ","));
            if(CollectionUtils.isEmpty(storeIdList)){
                Result.error(ErrorCodeEnum.PARAMETER_ERROR, "门店ID不能为空");
            }
            for(String storeId : storeIdList) {
                try {
                    CashierStoreVO cashierStoreVO = storeV2Service.getCashierStoreByStoreId(storeId);
                    if(null != cashierStoreVO){
                        int count = tvDeviceDAO.countBindTvDeviceByStoreId(storeId);
                        if(count > 0){
                            boolean syncResult = mccConfigHelperService.syncStoreBindTvDeviceToMcc(storeId, YesNoEnum.Y);
                            LOGGER.info("storeBindDeviceSyncMcc success, storeId:{}, bindTvDevice:{}", storeId, YesNoEnum.Y);
                        }else {
                            boolean syncResult = mccConfigHelperService.syncStoreBindTvDeviceToMcc(storeId, YesNoEnum.N);
                            LOGGER.info("storeBindDeviceSyncMcc success, storeId:{}, bindTvDevice:{}", storeId, YesNoEnum.N);
                        }
                    }
                } catch (Exception ex) {
                    LOGGER.error("storeBindDeviceSyncMcc exception, storeId:{}", storeId, ex);
                    continue;
                }
            }
            return Result.success("订正成功");
        } catch (Exception ex){
            LOGGER.error("storeBindDeviceSyncMcc revise data error", ex);
            return Result.error(ErrorCodeEnum.SYSTEM_ERROR.getErrCode(), ex.getMessage());
        }
    }

    /**
     * 订正历史门店添加到白名单
     * @return
     */
    @PostMapping(value = "/store/add/store/device/whitelist")
    public Result<Boolean> storeAddStoreDeviceWhitelist(@RequestParam(value = "storeIds") String storeIds) {
        try {
            List<String> storeIdList = Arrays.asList(StringUtils.split( storeIds, ","));
            if(CollectionUtils.isEmpty(storeIdList)){
                Result.error(ErrorCodeEnum.PARAMETER_ERROR, "门店ID不能为空");
            }
            StoreDeviceWhitelistCreateRequest request = new StoreDeviceWhitelistCreateRequest();
            request.setStoreIds(storeIdList);
            request.setType(DeviceTypeEnum.CASHIER);
            request.setSource(WhitelistSourceEnum.HISTORY);
            request.setStatus(WhitelistStatusEnum.NORMAL);
            request.setRemark("历史商户门店");
            request.setOperator("redhat");
            request.setOperatorId("redhat");
            boolean createResult = storeDeviceWhitelistRpcService.create(request);
            if(createResult){
                LOGGER.info("DataReviseController.storeAddStoreDeviceWhitelist.success，storeIdList:{}", JSON.toJSONString(storeIdList));
            }else {
                LOGGER.warn("DataReviseController.storeAddStoreDeviceWhitelist.error，storeIdList:{}", JSON.toJSONString(storeIdList));
            }
            return Result.success(Boolean.TRUE);
        } catch (Exception ex){
            LOGGER.error("DataReviseController.storeAddStoreDeviceWhitelist revise data error", ex);
            return Result.error(ErrorCodeEnum.SYSTEM_ERROR.getErrCode(), ex.getMessage());
        }
    }

    /**
     * 门店添加到激活信息中
     *
     * @return
     */
    @PostMapping(value = "/store/add/device/active")
    public Result<Boolean> storeAddDeviceActive(@RequestParam(value = "storeIds", required = false) String storeIds,
                                                @RequestParam(value = "startTimeStr", required = false) String startTimeStr,
                                                @RequestParam(value = "endTimeStr", required = false) String endTimeStr) {
        try {
            List<String> storeIdList = null;
            if(StringUtils.isBlank(storeIds)){
                if(StringUtils.isBlank(startTimeStr) || StringUtils.isBlank(endTimeStr)){
                    return Result.error(ErrorCodeEnum.PARAMETER_ERROR, "开始时间和结束时间不能同时为空");
                }
                Date startTime = transToDate(startTimeStr,"yyyy-MM-dd HH:mm:ss");
                Date endTime = transToDate(endTimeStr,"yyyy-MM-dd HH:mm:ss");
                if(null == startTime || null == endTime){
                    return Result.error(ErrorCodeEnum.PARAMETER_ERROR, "开始时间或结束时间格式不正确");
                }
                if(endTime.getTime() <= startTime.getTime()){
                    return Result.error(ErrorCodeEnum.PARAMETER_ERROR, "结束时间必须大于开始时间");
                }
                storeIdList = userLoginHistoryMapper.findStoreIdList(startTime.getTime(), endTime.getTime());
            }else {
                storeIdList = Arrays.asList(StringUtils.split( storeIds, ","));
            }

            if(CollectionUtils.isEmpty(storeIdList)){
                return Result.error(ErrorCodeEnum.PARAMETER_ERROR, "门店ID不能为空");
            }

            Map<String, DeviceTypeEnum> storeIdTypeMap = storeIdList.stream().collect(Collectors.toMap(it -> it, it -> DeviceTypeEnum.CASHIER));
            Map<String, String> relationUserId = customerRelationService.getRelationUserId(storeIdTypeMap);

            storeIdList.stream().forEach(storeId -> {
               try {
                   CashierStoreVO cashierStoreVO = storeV2Service.getCashierStoreByStoreId(storeId);
                   if(null == cashierStoreVO){
                       return;
                   }

                   StoreDeviceActiveDO existsActiveDO = storeDeviceActiveService.getStoreActivated(storeId, DeviceTypeEnum.CASHIER);
                   if(null == existsActiveDO){
                       StoreDeviceActiveDO activeDO = new StoreDeviceActiveDO();
                       Long activeTime = System.currentTimeMillis();
                       Long validEndTime = DateUtils.PERMANENT_TIME;
                       activeDO
                               .setStoreId(cashierStoreVO.getStoreId())
                               .setStoreSn(cashierStoreVO.getStoreCode())
                               .setMerchantId(cashierStoreVO.getMerchantId())
                               .setMerchantSn(cashierStoreVO.getMerchantCode())
                               .setType(DeviceTypeEnum.CASHIER)
                               .setSource(ActiveDeviceSourceEnum.WHITELIST)
                               .setActiveTime(activeTime)
                               .setValidEndTime(validEndTime)
                               .setActiveUserId(null)
                               .setActiveUserName(null)
                               .setActiveUserPhone(null)
                               .setLicenceRelatedCrmUserId(relationUserId.get(cashierStoreVO.getStoreId()))
                               .setRefId(null)
                               .setBind(YesNoEnum.Y)
                               .setDeleteMark(YesNoEnum.N)
                               .setRemark("历史门店");
                       boolean createResult = storeDeviceActiveService.createStoreDeviceActive(activeDO);
                       if(createResult){
                           LOGGER.info("DataReviseController.storeAddDeviceActive.success，storeId:{}", storeId);
                       }else {
                           LOGGER.warn("DataReviseController.storeAddDeviceActive.error，storeId:{}", storeId);
                       }
                   }else {
                       LOGGER.warn("DataReviseController.storeAddDeviceActive.store exists，storeId:{}", storeId);
                   }
               } catch (Exception ex) {
                   LOGGER.error("DataReviseController.storeAddDeviceActive.error，storeId:{}", storeId, ex);
               }
            });
            return Result.success(Boolean.TRUE);
        } catch (Exception ex){
            LOGGER.error("DataReviseController.storeAddStoreDeviceWhitelist revise data error", ex);
            return Result.error(ErrorCodeEnum.SYSTEM_ERROR.getErrCode(), ex.getMessage());
        }
    }

    private Date transToDate(String dateStr, String pattern){
        Date result = null;
        if (StringUtils.isNotBlank(dateStr)){
            try {
                result = org.apache.commons.lang3.time.DateUtils.parseDate(dateStr,pattern);
            } catch (ParseException e) {
                throw new ParamException(ErrorCodeEnum.SYSTEM_ERROR);
            }
        }
        return result;
    }

    /**
     * 商户许可证退款
     *
     * @return
     */
    @PostMapping(value = "/direct/merchant/refund/licence")
    public Result<Boolean> directMerchantRefundLicence(@RequestParam(value = "merchantId", required = false) String merchantId,
                                                       @RequestParam(value = "refundOrderIdListStr", required = false) String refundOrderIdListStr){
        if(StringUtils.isBlank(merchantId)){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR, "商户ID不能为空");
        }
        if(StringUtils.isBlank(refundOrderIdListStr)){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR, "退款订单ID不能为空");
        }
        List<String> refundOrderIdList = Arrays.asList(StringUtils.split( refundOrderIdListStr, ","));
        if(CollectionUtils.isEmpty(refundOrderIdList)){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR, "退款订单ID错误");
        }

        List<RefundOrderItem> refundOrderItems = new ArrayList<>();
        refundOrderIdList.stream().forEach(refundOrderId -> {
            RefundOrderItem refundOrderItem = new RefundOrderItem();
            refundOrderItem.setOrderItemSn(refundOrderId);

            refundOrderItems.add(refundOrderItem);
        });

        FTOrderBasicRefundEvent refundEvent = new FTOrderBasicRefundEvent();
        refundEvent.setMerchantId(merchantId);
        refundEvent.setRefundOrderItems(refundOrderItems);

        kafkaConsumer.directMerchantRefundLicence(refundEvent);
        return Result.success(true);
    }

    @PostMapping(value = "/redoOmsDingAudit")
    public void redoOmsDingAudit(@RequestBody DingAuditSyncEvent event) {
        kafkaConsumer.omsDingAuditSyncProcess(event);
    }
}
