package com.wosai.smartbiz.controller.gds;


import com.wosai.smartbiz.base.enums.CommonErrorCodeEnum;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.goods.enums.base.LogLevelEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

@RestController
@RequestMapping("/common")
@Api("公共处理")
public class CommonController {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommonController.class);

    @ApiOperation("日志收集")
    @PostMapping("/log/collect")
    public Result<Boolean> logCollect(
            String client,
            LogLevelEnum level,
            String logContent) {
        if (StringUtils.isAnyBlank(client, logContent)) {
            return Result.error(CommonErrorCodeEnum.PARAMETER_ERROR);
        }
        if (Objects.isNull(level)) {
            return Result.error(CommonErrorCodeEnum.PARAMETER_ERROR);
        }
        String finalLogContent = "[" + client + "] " + logContent;
        if (level == LogLevelEnum.INFO) {
            LOGGER.info(finalLogContent);
        }
        if (level == LogLevelEnum.WARN) {
            LOGGER.warn(finalLogContent);
        }
        if (level == LogLevelEnum.ERROR) {
            LOGGER.error(finalLogContent);
        }
        return Result.success(Boolean.TRUE);

    }
}
