package com.wosai.smartbiz.controller;

import com.googlecode.jsonrpc4j.JsonRpcHttpClient;
import com.wosai.smartbiz.controller.uc.BaseController;
import com.wosai.smartbiz.demo.apollo.ApolloBean;
import com.wosai.smartbiz.demo.redis.Person;
import com.wosai.smartbiz.uc.services.UcTerminalSnService;
import com.wosai.smartbiz.utils.RedisUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.net.URL;

/**
 * <AUTHOR> binwang
 * @date : 2020/3/4
 */
@RestController
@RequestMapping("demo")
public class DemosController extends BaseController {

  private static final Logger logger = LoggerFactory.getLogger(DemosController.class);

  @Autowired
  private RedisUtil redisUtil;

  @Autowired
  private JdbcTemplate jdbcTemplateA;

  @Autowired
  private ApolloBean apolloBean; //可把多个Apollo配置放到一个bean里

  //可通过@Value，直接从apollo读取配置, 并实时更新
  @Value("${valueFromApollo:local}")  //本apollo项目下的配置
  private String valueFromApollo;

  @Value("${basicTest:local}") //其他apollo项目下，公共命名空间的配置。公共的命名空间需要在yaml文件配置
  private String basicTest;

    @Value("${beiyu.test:false}")
  private String beiyuTest;

    @Autowired
    private UcTerminalSnService ucTerminalSnService;

//  @Autowired
//  ElasticsearchOperations elasticsearchOperations;

//  @RequestMapping(value = "/es/highLevelRestClient", method = RequestMethod.GET)
//  public @ResponseBody
//  String insertAndSelectHighLevel() throws Exception {
//    ESProductDO product = new ESProductDO();
//    product.setId(3); // 一般 ES 的 ID 编号，使用 DB 数据对应的编号。这里，先写死
//    product.setName("wosai 测试");
//    product.setSellPoint("愿半生编码，如一生老友");
//    product.setDescription("我只是一个描述");
//    product.setCid(1);
//    product.setCategoryName("技术");
//
//    IndexQuery indexQuery = new IndexQueryBuilder()
//        .withId(product.getId().toString())
//        .withObject(product)
//        .build();
//    String documentId = elasticsearchOperations.index(indexQuery);
//
//    ESProductDO productDOQuery = elasticsearchOperations.queryForObject(GetQuery.getById(product.getId().toString()), ESProductDO.class);
//    return productDOQuery.toString();
//  }

  @RequestMapping(value = "/redis", method = RequestMethod.GET)
  public @ResponseBody String setAndGet() throws Exception {
    String demoKey = "demo11";
    Person person = new Person("ww2",12);
    System.out.println();
    boolean result = redisUtil.set(demoKey, person, 1);
    if (result) {
      System.out.println("####################set redis success");
    }
    return "hi";
  }

  @RequestMapping(value = "/jdbc", method = RequestMethod.GET)
  public @ResponseBody String find() throws Exception {
    return "suc";

  }

  @RequestMapping(value = "/jsonRpc", method = RequestMethod.GET)
  public @ResponseBody String callRemoteApi() {
    String result = "";
    try {
      JsonRpcHttpClient client = new JsonRpcHttpClient(
          new URL("http://merchant-user-goods-center-service.beta.iwosai.com/rpc/merchantuser"));

      result = client.invoke("getSuperAdminOperatorIdByMerchantId", new Object[] { "9ae7a7b6-b529-4945-805a-4496ef0faa4e" }, String.class);
    } catch (Exception e) {
      e.printStackTrace();
      return "failed";
    } catch (Throwable throwable) {
      throwable.printStackTrace();
      return "failed";
    }
    return "success" + result;
  }

  @RequestMapping(value = "/log", method = RequestMethod.GET)
  public @ResponseBody String log() {
    logger.info("logging info,{}", "hello world");
    logger.error("logging error");
    if (true) {
      throw new RuntimeException("runtime exception");
    }
    return "log";
  }

  @RequestMapping(value = "/apollo", method = RequestMethod.GET)
  public @ResponseBody String apollo() {
    return valueFromApollo + "###" + apolloBean + "###"+beiyuTest + "###" + basicTest;
  }
}
