package com.wosai.smartbiz.controller.uc;

import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.gds.enums.ErrorCodeEnum;
import com.wosai.smartbiz.uc.manager.LoginHistoryService;
import com.wosai.smartbiz.user.user.login.DeviceParam;
import com.wosai.smartbiz.user.user.login.LoginParam;
import com.wosai.smartbiz.uc.services.LoginV2Service;
import com.wosai.smartbiz.user.user.vo.UserLoginV2VO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


@Slf4j
@RestController
@RequestMapping("/login")
public class LoginController extends BaseController{

    @Autowired
    LoginV2Service loginV2Service;

    @Autowired
    LoginHistoryService loginHistoryService;

    /**
     * 收银台V2版本登录
     * @param loginParam
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value="/v2/cashier", method = RequestMethod.POST)
    public Result<UserLoginV2VO> loginCashierV2(LoginParam loginParam,
                                                HttpServletRequest request,
                                                HttpServletResponse response){
        if(null == loginParam || StringUtils.isBlank(loginParam.getUserCode())){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), "登录手机号不能为空");
        }
        if(loginParam.getUserCode().length() != 11){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), "手机号为11位数字");
        }
        if(StringUtils.isBlank(loginParam.getUserPwd())){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), "登录密码不能为空");
        }
        if(loginParam.getUserPwd().length() < 6 || loginParam.getUserPwd().length() > 20){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), "登录密码必须为6-20位(必须包含数字和字母)");
        }
        DeviceParam deviceParam = getRequestDeviceParam(request);
        if(StringUtils.isBlank(deviceParam.getDeviceFp())){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), "终端编号不能为空");
        }

        Result<UserLoginV2VO> result = loginV2Service.loginCashier(loginParam, deviceParam);
        if(!result.isSuccess()){
            return Result.error(result.getErrorCode(), result.getErrorMsg());
        }
        return result;
    }

    /**
     * 登录授权状态查询
     * @param authQrCode
     * @param request
     * @return
     */
    @RequestMapping(value="/v2/auth/status", method = RequestMethod.POST)
    public Result<UserLoginV2VO> loginAuthStatus(@RequestParam(value = "authQrCode") String authQrCode,
                                                 HttpServletRequest request){
        Result<UserLoginV2VO> result = loginV2Service.loginAuthStatus(authQrCode);
        if(!result.isSuccess()){
            return Result.error(result.getErrorCode(), result.getErrorMsg());
        }
        return result;
    }


    /**
     * 获取登录短信验证码
     * @param userPhone
     * @param request
     * @return
     */
    @RequestMapping(value="/v2/cashier/sms/code", method = RequestMethod.POST)
    public Result<Boolean> smsCode(@RequestParam(value = "userPhone") String userPhone,
                                                 HttpServletRequest request){
        if(StringUtils.isBlank(userPhone)){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), "登录手机号不能为空");
        }
        return loginV2Service.sendLoginSmsCode(userPhone);
    }

    /**
     * 短信验证码登录
     * @param loginParam
     * @param request
     * @return
     */
    @RequestMapping(value="/v2/cashier/by/authcode", method = RequestMethod.POST)
    public Result<UserLoginV2VO> loginCashierByAuthCode(LoginParam loginParam,
                                         HttpServletRequest request){
        if(null == loginParam || StringUtils.isBlank(loginParam.getUserPhone())){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), "登录手机号不能为空");
        }
        if(loginParam.getUserPhone().length() != 11){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), "手机号为11位数字");
        }
        if(StringUtils.isBlank(loginParam.getAuthCode())){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), "验证码不能为空");
        }
        DeviceParam deviceParam = getRequestDeviceParam(request);
        if(StringUtils.isBlank(deviceParam.getDeviceFp())){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), "终端编号不能为空");
        }

        Result<UserLoginV2VO> result = loginV2Service.loginCashierByAuthCode(loginParam, deviceParam);
        if(!result.isSuccess()){
            return Result.error(result.getErrorCode(), result.getErrorMsg());
        }
        return result;
    }
}
