package com.wosai.smartbiz.controller.rmqx;

import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.controller.uc.BaseController;
import com.wosai.smartbiz.gds.enums.ErrorCodeEnum;
import com.wosai.smartbiz.message.MqttConfig;
import com.wosai.smartbiz.message.MqttPushClient;
import com.wosai.smartbiz.utils.RmqxUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * RMQX 消息收发
 */
@RestController
@RequestMapping(value = "/rmqx/message")
@Slf4j
public class RmqxMessageController extends BaseController {

    @Autowired
    private MqttConfig mqttConfig;
    @Autowired
    private MqttPushClient mqttPushClient;
    @Autowired
    private RmqxUtil rmqxUtil;

    @ResponseBody
    @RequestMapping(value = "/send", method = RequestMethod.POST)
    public Result<Boolean> send(@RequestParam(value = "content", required = false) String content, HttpServletRequest request){
        if(StringUtils.isBlank(content)){
            return Result.error(ErrorCodeEnum.SYSTEM_ERROR);
        }
        String deviceMqttSn = getRequestTerminalMqttSn(request);
        if(StringUtils.isBlank(deviceMqttSn)){
            return Result.error(ErrorCodeEnum.SYSTEM_ERROR);
        }
        String topic = rmqxUtil.getDeviceCashierMessageTopic(deviceMqttSn);

        /*mqttPushClient.publish("/abc/def/12345678", body);

        Map<String, Object> contentMap = new HashMap<>();
        contentMap.put("content", content);
        contentMap.put("deviceMqttSn", deviceMqttSn);
        cashierMessageService.sendDeviceCashierMessage(deviceMqttSn, NoticeType.SYSTEM_MESSAGE, contentMap);
*/
        return Result.success(true);
    }

}
