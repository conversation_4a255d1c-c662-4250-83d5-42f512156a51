package com.wosai.smartbiz.controller.uc.repast;

import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.controller.uc.BaseController;
import com.wosai.smartbiz.gds.enums.ErrorCodeEnum;
import com.wosai.smartbiz.uc.manager.StorePrinterConfigService;
import com.wosai.smartbiz.uc.utils.converter.StorePrinterConfigUtils;
import com.wosai.smartbiz.user.user.dto.StorePrinterConfigDTO;
import com.wosai.smartbiz.user.user.vo.printer.StorePrinterConfigVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/app/kmzg/v1/store/printer")
public class StorePrinterController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(StorePrinterController.class);

    @Autowired
    private StorePrinterConfigService storePrinterConfigService;

    /**
     * 打印机列表
     */
    @PostMapping("/list")
    public Result<List<StorePrinterConfigVO>> list(
            @RequestParam(value = "merchantId") String merchantId,
            @RequestParam(value = "storeId") String storeId){

        try {
            List<StorePrinterConfigDTO> configDTOList = storePrinterConfigService.listConfigByStoreId(merchantId, storeId);
            return Result.success(StorePrinterConfigUtils.convertToConfigVOs(configDTOList));
        } catch (ParamException e) {
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getCode(),e.getMessage());
        } catch (Exception e) {
            LOGGER.error("list store printer config error", e);
            return Result.error(ErrorCodeEnum.SYSTEM_ERROR.getCode(),e.getMessage());
        }
    }
}
