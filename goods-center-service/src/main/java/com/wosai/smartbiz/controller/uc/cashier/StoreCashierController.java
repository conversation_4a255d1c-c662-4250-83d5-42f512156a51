package com.wosai.smartbiz.controller.uc.cashier;

import com.wosai.market.boss.circle.user.api.UserFollowAndFansService;
import com.wosai.market.boss.circle.user.api.dto.request.follow.AutoFollowRequest;
import com.wosai.smartbiz.base.enums.SceneEnum;
import com.wosai.smartbiz.base.enums.YesNoEnum;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.controller.uc.BaseController;
import com.wosai.smartbiz.gds.dto.licence.LicenceRenewRequest;
import com.wosai.smartbiz.gds.dto.licence.RenewUrlDetail;
import com.wosai.smartbiz.gds.dto.licence.StoreLicenceDetail;
import com.wosai.smartbiz.gds.enums.DeviceTypeEnum;
import com.wosai.smartbiz.gds.enums.ErrorCodeEnum;
import com.wosai.smartbiz.gds.enums.LicenceVersionTypeEnum;
import com.wosai.smartbiz.gds.service.CashierLicenceService;
import com.wosai.smartbiz.gds.service.MccConfigHelperService;
import com.wosai.smartbiz.gds.service.StoreLicenceRenewService;
import com.wosai.smartbiz.goods.direct.PollingDeviceInfoDAO;
import com.wosai.smartbiz.goods.domain.PollingDeviceInfoDO;
import com.wosai.smartbiz.goods.domain.SoundCashDO;
import com.wosai.smartbiz.goods.domain.StoreDeviceInfoDO;
import com.wosai.smartbiz.uc.config.RmqxConfigInitializer;
import com.wosai.smartbiz.uc.manager.*;
import com.wosai.smartbiz.uc.services.LoginV2Service;
import com.wosai.smartbiz.user.user.constants.UcConstants;
import com.wosai.smartbiz.user.user.enums.AppTypeEnum;
import com.wosai.smartbiz.user.user.enums.UcErrorCodeEnum;
import com.wosai.smartbiz.user.user.login.DeviceParam;
import com.wosai.smartbiz.user.user.login.LoginUserParam;
import com.wosai.smartbiz.user.user.vo.*;
import eu.bitwalker.useragentutils.Browser;
import eu.bitwalker.useragentutils.OperatingSystem;
import eu.bitwalker.useragentutils.UserAgent;
import eu.bitwalker.useragentutils.Version;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Validated
@RestController
@RequestMapping("/store/cashier")
public class StoreCashierController extends BaseController{
    private static final String UNKNOWN = "Unknown";
    @Autowired
    private LoginV2Service loginV2Service;

    @Autowired
    private StoreV2Service storeV2Service;

    @Autowired
    private MccConfigHelperService mccConfigHelperService;

    @Autowired
    private LoginHistoryService loginHistoryService;

    @Autowired
    private UserFollowAndFansService userFollowAndFansService;
    @Autowired
    private UserTerminalService userTerminalService;

    @Autowired
    private RmqxConfigInitializer rmqxConfigInitializer;

    @Autowired
    private PollingDeviceInfoDAO pollingDeviceInfoDAO;
    @Autowired
    private StoreDeviceInfoService storeDeviceInfoService;
    @Autowired
    private StoreLicenceRenewService storeLicenceRenewService;
    @Autowired
    private CashierLicenceService cashierLicenceService;

    @Autowired
    private SoundCashService soundCashService;

    /**
     * 收银台获取门店列表
     * @param request
     * @return
     */
    @RequestMapping(value="/list", method = RequestMethod.POST)
    public Result<List<CashierStoreVO>> cashierStoreList(HttpServletRequest request) {
        String ucUserId = getRequestedSqbUserId(request);
        List<CashierStoreVO>  cashierStoreVOList = storeV2Service.findCashierStoreListByUcUserId(ucUserId, SceneEnum.RESTAURANT);
        return Result.success(cashierStoreVOList);
    }

    /**
     * 收银台选择门店
     * @param request
     * @return
     */
    @RequestMapping(value="/choice", method = RequestMethod.POST)
    public Result<UserLoginV2VO> cashierChoice(@RequestParam(value = "storeId", required = false) String storeId,
                                               @RequestParam(value = "sqbUserId", required = false) String sqbUserId,
                                               @RequestParam(value = "userName", required = false) String userName,
                                               @RequestParam(value = "userPhone", required = false) String userPhone,
                                               HttpServletRequest request) {
        if(StringUtils.isBlank(storeId)){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getCode(), "请选择门店");
        }
        //兼容老版本接口代码，如果参数为空，则去请求头获取
        if(StringUtils.isBlank(sqbUserId)){
            sqbUserId = getRequestedSqbUserId(request);
        }

        DeviceParam deviceParam = getRequestDeviceParam(request);
        if(StringUtils.isBlank(deviceParam.getDeviceFp())){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), "终端编号不能为空");
        }

        //如果更换了商户，则需要重新跟换登录用户信息
        String authorization = getRequestAuthorization(request);
        if(StringUtils.isBlank(authorization)){
            return Result.error(UcErrorCodeEnum.ACCESS_TOKEN_INVALID);
        }
        if(!authorization.startsWith(UcConstants.RequestConstants.AUTHORIZATION_SCHAME)){
            return Result.error(UcErrorCodeEnum.ACCESS_TOKEN_INVALID);
        }
        String accessToken = authorization.substring(7);

        Result<UserLoginV2VO> result = loginV2Service.changeChoiceStore(accessToken, storeId, sqbUserId, deviceParam);
        if(!result.isSuccess()){
            return result;
        }

        UserLoginV2VO userLoginV2VO = result.getData();

        //选择门店后处理逻辑添加
        choiceStoreAfterProcess(storeId, userLoginV2VO, deviceParam, request);

        return Result.success(userLoginV2VO);
    }

    /**
     * 解绑设备和门店
     * @param request
     * @return
     */
    @RequestMapping(value="/unbind", method = RequestMethod.POST)
    public Result<Boolean> unbindDevice(@RequestParam(value = "storeId", required = false) String storeId,
                                        HttpServletRequest request) {
        if(StringUtils.isBlank(storeId)){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getCode(), "请选择门店");
        }
        DeviceParam deviceParam = getRequestDeviceParam(request);
        if(StringUtils.isBlank(deviceParam.getDeviceFp())){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), "终端编号不能为空");
        }
        Result<Boolean> result = loginV2Service.unbindChoiceStore(storeId, deviceParam);
        if(result.isSuccess()){
            unbindStoreAfterProcess(storeId, deviceParam, request);
        }
        return result;
    }

    /**
     * 门店激活
     * @param request
     * @return
     */
    @RequestMapping(value="/active", method = RequestMethod.POST)
    public Result<Boolean> active(@RequestParam(value = "storeId", required = false) String storeId,
                                  @ApiParam(value = "登录用户信息", required = false) LoginUserParam loginUserParam,
                                  HttpServletRequest request) {
        if(StringUtils.isBlank(storeId)){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getCode(), "请选择门店");
        }
        Result<StoreActiveVO> result = loginV2Service.active(storeId, SceneEnum.RESTAURANT, null, loginUserParam, null);
        if(result.isSuccess()){
            //发送开通收银机成功通知到老板圈
            bossCircleFollowEvent(storeId);
            //火山事件添加
            cashierLicenceService.dataEvent4Active(storeId, "直营", false);
            return Result.success(Boolean.TRUE);
        }else {
            return Result.error(result.getErrorCode(), result.getErrorMsg());
        }
    }

    /**
     * 门店激活
     * @param request
     * @return
     */
    @RequestMapping(value="/v3/active", method = RequestMethod.POST)
    public Result<StoreActiveVO> activeV3(@RequestParam(value = "storeId", required = false) String storeId,
                                          @RequestParam(value = "versionType", required = false) LicenceVersionTypeEnum versionType,
                                          @ApiParam(value = "登录用户信息", required = false) LoginUserParam loginUserParam,
                                          HttpServletRequest request) {
        if(StringUtils.isBlank(storeId)){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getCode(), "请选择门店");
        }
        DeviceParam deviceParam = getRequestDeviceParam(request);
        Result<StoreActiveVO> result = loginV2Service.active(storeId, SceneEnum.RESTAURANT, versionType, loginUserParam, deviceParam);
        if(result.isSuccess()){
            //发送开通收银机成功通知到老板圈
            bossCircleFollowEvent(storeId);
            //火山事件添加
            cashierLicenceService.dataEvent4Active(storeId, "直营", false);
        }
        return result;
    }

    /**
     * 同步到老板圈
     * @param storeId
     */
    private void bossCircleFollowEvent(String storeId) {
        //异步执行
        CompletableFuture.runAsync(() -> {
            try {
                //通知收银机小助手关注商户
                StoreV2VO store = storeV2Service.getStoreByStoreId(storeId);
                if (store != null && StringUtils.isNotBlank(store.getMerchantId())) {
                    AutoFollowRequest autoFollowRequest = new AutoFollowRequest();
                    autoFollowRequest.setBusinessId(9);
                    autoFollowRequest.setTargetMerchantId(store.getMerchantId());
                    userFollowAndFansService.autoFollowUser(autoFollowRequest);
                }
            } catch (Exception e) {
                log.error("老板圈收银机小助手服务号关注异常，storeId={}", storeId, e);
            }
        });
    }

    @RequestMapping(value="/getLicenceInfo", method = RequestMethod.POST)
    public Result<StoreLicenceDetail> getLicenceInfo(@RequestBody LicenceRenewRequest request) {
        StoreLicenceDetail licenceInfo = storeLicenceRenewService.getLicenceInfo(request);
        return Result.success(licenceInfo);
    }

    @RequestMapping(value="/getRenewCashierLicenceUrl", method = RequestMethod.POST)
    public Result<RenewUrlDetail> getRenewCashierLicenceUrl(@RequestBody LicenceRenewRequest request) {
        RenewUrlDetail renewCashierLicenceUrl = storeLicenceRenewService.getRenewCashierLicenceUrl(request);
        return Result.success(renewCashierLicenceUrl);
    }


    /**
     * 选择门店后处理逻辑
     * @param storeId
     * @param userLoginV2VO
     * @param deviceParam
     * @param request
     * @return
     */
    private boolean choiceStoreAfterProcess(String storeId,UserLoginV2VO userLoginV2VO, DeviceParam deviceParam, HttpServletRequest request){
        try {
            if(null == userLoginV2VO){
                return false;
            }
            boolean needEnabledCashierMode = mccConfigHelperService.checkNeedEnabledCashierMode(storeId);
            if (needEnabledCashierMode) {
                // 启用开关的可用状态
                mccConfigHelperService.cashierLoginEnableCashierMode(storeId, SceneEnum.RESTAURANT);
            }

            if(null != deviceParam && StringUtils.isNotBlank(deviceParam.getDeviceFp())){
                String userAgentString = getRequestUserAgent(request);
                UserAgent userAgent = getUserAgent(request);
                Browser browser = userAgent.getBrowser();
                Version version = browser.getVersion(userAgentString);
                String browserVersion = UNKNOWN;
                if (! Objects.isNull(version)){
                    browserVersion = version.getVersion();
                }
                OperatingSystem os = userAgent.getOperatingSystem();
                UserLoginHistoryVO userLoginHistoryVO = new UserLoginHistoryVO();
                userLoginHistoryVO.setSqbUserId(userLoginV2VO.getSqbUserId());
                userLoginHistoryVO.setLoginCode(userLoginV2VO.getUserPhone());
                userLoginHistoryVO.setMerchantId(userLoginV2VO.getMerchantId());
                userLoginHistoryVO.setMerchantSn(userLoginV2VO.getMerchantCode());
                userLoginHistoryVO.setStoreId(userLoginV2VO.getStoreId());
                userLoginHistoryVO.setStoreSn(userLoginV2VO.getStoreCode());
                userLoginHistoryVO.setMerchantUserId(userLoginV2VO.getMerchantUserId());
                userLoginHistoryVO.setAppType(AppTypeEnum.CASHIER);
                userLoginHistoryVO.setVersion(deviceParam.getAppVersion());
                userLoginHistoryVO.setTerminalSn(deviceParam.getDeviceFp());
                userLoginHistoryVO.setLoginTime(new Date());
                userLoginHistoryVO.setIp(getIpAddress(request));
                userLoginHistoryVO.setBroswer(browser.getName() + "_" + browserVersion);
                userLoginHistoryVO.setOs(os.getName());
                userLoginHistoryVO.setUserAgent(userAgentString);
                loginHistoryService.addLoginHistory(userLoginHistoryVO);

                //记录门店设备的MQTT关系
                PollingDeviceInfoDO pollingDeviceInfoDO = new PollingDeviceInfoDO();
                pollingDeviceInfoDO.setStoreId(userLoginV2VO.getStoreId());
                pollingDeviceInfoDO.setDeviceSn(deviceParam.getDeviceFp());
                pollingDeviceInfoDO.setAppType(userLoginHistoryVO.getAppType().name());
                pollingDeviceInfoDO.setLoginTime(new Date());
                pollingDeviceInfoDO.setDeviceMqttSn(deviceParam.getDeviceMqttFp());
                pollingDeviceInfoDO.setStoreSn(userLoginV2VO.getStoreCode());
                pollingDeviceInfoDAO.insertDeviceInfo(pollingDeviceInfoDO);

                //记录门店和设备关系数据
                StoreDeviceInfoDO storeDeviceInfoDO = storeDeviceInfoService.getByStoreIdAndTerminalSnAndType(userLoginV2VO.getStoreId(), deviceParam.getDeviceFp(), DeviceTypeEnum.CASHIER);
                String deviceFingerprint = userTerminalService.buildDeviceFp(userLoginV2VO.getStoreId(), deviceParam.getDeviceScene(), deviceParam.getDeviceFp());
                if(null != storeDeviceInfoDO){
                    storeDeviceInfoDO.setLastLoginTime(System.currentTimeMillis());
                    storeDeviceInfoDO.setVersion(deviceParam.getAppVersion());
                    storeDeviceInfoDO.setMqttClientId(rmqxConfigInitializer.getMqttClientId(deviceParam.getDeviceMqttFp(), deviceParam.getDeviceScene()));
                    storeDeviceInfoDO.setDeviceFingerprint(deviceFingerprint);
                    storeDeviceInfoDO.setOnline(YesNoEnum.Y);
                    storeDeviceInfoService.update(storeDeviceInfoDO);
                }else {
                    storeDeviceInfoDO = new StoreDeviceInfoDO()
                            .setMerchantId(userLoginV2VO.getMerchantId())
                            .setMerchantSn(userLoginV2VO.getMerchantCode())
                            .setStoreId(userLoginV2VO.getStoreId())
                            .setStoreSn(userLoginV2VO.getStoreCode())
                            .setTerminalId(StringUtils.isNotBlank(deviceParam.getDeviceId()) ? deviceParam.getDeviceId() : deviceParam.getDeviceFp())
                            .setTerminalSn(deviceParam.getDeviceFp())
                            .setMqttClientId(rmqxConfigInitializer.getMqttClientId(deviceParam.getDeviceMqttFp(), deviceParam.getDeviceScene()))
                            .setDeviceFingerprint(deviceFingerprint)
                            .setType(DeviceTypeEnum.CASHIER)
                            .setVersion(deviceParam.getAppVersion())
                            .setBrand(deviceParam.getTerminalBrand())
                            .setModel(deviceParam.getTerminalModel())
                            .setLastLoginTime(System.currentTimeMillis())
                            .setOnline(YesNoEnum.Y);
                    storeDeviceInfoService.create(storeDeviceInfoDO);
                }
            }
        } catch (Exception ex) {
            log.error("StoreCashierController.choiceStoreAfterProcess error, storeId:{}", userLoginV2VO.getStoreId(), ex);
        }
        return true;
    }

    /**
     * 解绑门店后处理逻辑
     * @param storeId
     * @param deviceParam
     * @param request
     * @return
     */
    private boolean unbindStoreAfterProcess(String storeId, DeviceParam deviceParam, HttpServletRequest request){
        try {
            storeDeviceInfoService.delete(storeId, deviceParam.getDeviceFp(), DeviceTypeEnum.CASHIER);
            // 解绑音箱和收银机
            SoundCashDO soundCashDOParam = new SoundCashDO();
            soundCashDOParam.setStoreId(storeId);
            soundCashDOParam.setCashSn(deviceParam.getDeviceFp());
            boolean result = soundCashService.unbind(soundCashDOParam);
            if(result){
                log.info("成功解绑收银机和其关联的音箱 storeId:{}, cashSn:{}", soundCashDOParam.getStoreId(), soundCashDOParam.getCashSn());
            }else{
                log.warn("失败解绑收银机和其关联的音箱 storeId:{}, cashSn:{}", soundCashDOParam.getStoreId(), soundCashDOParam.getCashSn());
            }
        } catch (Exception ex) {
            log.error("StoreCashierController.unbindStoreAfterProcess error, storeId:{}, deviceFp:{}", storeId, deviceParam.getDeviceFp(), ex);
        }
        return true;
    }
}
