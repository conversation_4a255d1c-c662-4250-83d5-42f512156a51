package com.wosai.smartbiz.controller.rmqx;

import com.wosai.smartbiz.message.MqttConfig;
import com.wosai.smartbiz.message.MqttPushClient;
import com.wosai.smartbiz.utils.RmqxUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * RMQX 消息服务器鉴权接口
 */
@RestController
@RequestMapping(value = "/rmqx/v1")
@Slf4j
public class RmqxAuthController {

    @Autowired
    private MqttConfig mqttConfig;
    @Autowired
    private MqttPushClient mqttPushClient;
    @Autowired
    private RmqxUtil rmqxUtil;

    @ResponseBody
    @RequestMapping(value = "/auth", method = RequestMethod.POST)
    public ResponseEntity<Map<String, Object>> auth(@RequestParam(value = "username", required = false) String username, @RequestParam(value = "password", required = false) String password){
        if(StringUtils.isBlank(username) || StringUtils.isBlank(password)){
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(null);
        }
        if(!Objects.equals(mqttConfig.getUser(), username) || !Objects.equals(mqttConfig.getPwd(), password)){
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(null);
        }
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("is_superuser", mqttConfig.getSuperuser());
        bodyMap.put("pubs", rmqxUtil.getAuthPubs());
        bodyMap.put("subs", rmqxUtil.getAuthSubs());

        return ResponseEntity.ok().body(bodyMap);
    }

    @ResponseBody
    @RequestMapping(value = "/disconnect", method = RequestMethod.POST)
    public void disconnect(){
        mqttPushClient.disconnect();
    }


}
