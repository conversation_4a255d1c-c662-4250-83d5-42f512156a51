package com.wosai.smartbiz.controller.uc;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.wosai.market.user.dto.UserDTO;
import com.wosai.market.user.dto.UserInfoDTO;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.gds.enums.ErrorCodeEnum;
import com.wosai.smartbiz.vo.CustomerUserVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/customer/user")
public class CustomerUserController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomerUserController.class);

    @Autowired
    private com.wosai.market.user.service.UserService customerUserService;

    /**
     * 获取消费者用户信息
     */
    @PostMapping("/detail")
    public Result<CustomerUserVO> detail(@RequestParam(value = "customerId") String customerId){
        return detail4Sentinel(customerId);
    }

    /**
     * 获取消费者用户信息
     */
    @SentinelResource(value = "smart-biz/goods-center/customer/user/detail")
    private Result<CustomerUserVO> detail4Sentinel(String customerId){
        CustomerUserVO customerUserVO = new CustomerUserVO();
        try {
            UserInfoDTO userInfoDTO = customerUserService.findUserInfoByUserId(customerId);
            if(null == userInfoDTO){
                return Result.error(ErrorCodeEnum.WX_MALL_CUSTOMER_NOT_EXISTS);
            }

            //收银机接入外卖订单之前版本customerId 使用的long类型，所以这里不能传值，传值后会对历史版本出现转换错误
            //customerUserVO.setCustomerId(userInfoDTO.getRefUserId());
            customerUserVO.setNickName(userInfoDTO.getNickName());
            customerUserVO.setGender(userInfoDTO.getGender());
            customerUserVO.setAvatarUrl(userInfoDTO.getAvatarUrl());

            UserDTO userDTO = customerUserService.getByUserId(customerId);
            if(null != userDTO){
                customerUserVO.setCellphone(userDTO.getCellphone());
            }
        } catch (Exception ex){
            LOGGER.error("CustomerUserController.detail error,customerId:{}", customerId, ex);
        }
        return Result.success(customerUserVO);
    }
}
