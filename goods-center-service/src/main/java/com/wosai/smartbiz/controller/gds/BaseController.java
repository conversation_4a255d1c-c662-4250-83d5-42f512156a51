package com.wosai.smartbiz.controller.gds;


import com.alibaba.fastjson.JSONObject;
import com.wosai.smartbiz.base.enums.CodeDescEnum;
import com.wosai.smartbiz.base.enums.CommonErrorCodeEnum;
import com.wosai.smartbiz.base.exceptions.BusinessException;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.base.exceptions.UnauthException;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.gds.enums.ErrorCodeEnum;
import com.wosai.smartbiz.goods.pojo.AuthLoginUser;
import com.wosai.smartbiz.uc.constants.Constants;
import com.wosai.smartbiz.base.utils.CashierUtil;
import com.wosai.smartbiz.user.user.constants.UcConstants;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Supplier;

import static net.logstash.logback.argument.StructuredArguments.keyValue;

public class BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(BaseController.class);

    private static final String TYPE_MISMATCH = "typeMismatch";

    private static final String TYPE_MISMATCH_ERROR_MSG = "%s格式不正确";

    protected <T> Result<T> processErrorResult(BindingResult bindingResult) {
        List<String> errorMsgs = new ArrayList<>();
        for (FieldError fieldError : bindingResult.getFieldErrors()) {
            String errorCode = fieldError.getCode();
            if (Objects.equals(TYPE_MISMATCH, errorCode)) {
                String fieldName = fieldError.getField();
                String message = String.format(TYPE_MISMATCH_ERROR_MSG, fieldName);
                errorMsgs.add(message);
            } else {
                String fieldMsg = fieldError.getDefaultMessage();
                errorMsgs.add(fieldMsg);
            }

        }
        return Result.error(CommonErrorCodeEnum.PARAMETER_ERROR.getCode(),
                StringUtils.join(errorMsgs, Constants.ERROR_SEPARATOR));
    }

    protected AuthLoginUser getRequestedUser(HttpServletRequest request) {
        return AuthLoginUser.builder()
                .userId(getRequestedUserId(request))
                .merchantUserId(getRequestedMerchantUserId(request))
                .merchantId(getRequestedMerchantId(request))
                .merchantCode(getRequestedMerchantCode(request))
                .storeId(getRequestedStoreId(request))
                .storeCode(getRequestedStoreCode(request))
                .build();
    }

    public Long getRequestedUserId(HttpServletRequest request) {
        String requestedBy = request.getHeader(Constants.AuthConstants.X_REQUESTED_BY);

        Long userId = null;
        if (StringUtils.isNotBlank(requestedBy)) {
            try {
                userId = Long.valueOf(requestedBy);
            } catch (NumberFormatException e) {
                throw new UnauthException("Failed to parse " + Constants.AuthConstants.X_REQUESTED_BY + ": " + requestedBy);
            }
        }
        return userId;
    }

    protected String getRequestedSqbUserId(HttpServletRequest request) {
        return request.getHeader(Constants.AuthConstants.X_SQB_USERID);
    }

    protected String getRequestedMerchantUserId(HttpServletRequest request) {
        return request.getHeader(Constants.AuthConstants.X_MERCHANT_USERID);
    }

    protected String getRequestedMerchantId(HttpServletRequest request) {
        return request.getHeader(Constants.AuthConstants.X_MERCHANT_ID);
    }

    protected String getRequestedMerchantCode(HttpServletRequest request) {
        return request.getHeader(Constants.AuthConstants.X_MERCHANT_CODE);
    }

    protected String getRequestedStoreId(HttpServletRequest request) {
        String requestedBy = request.getHeader(Constants.AuthConstants.X_STORE_ID);
        return (StringUtils.isNotBlank(requestedBy))?requestedBy:null;
    }

    protected String getRequestedStoreCode(HttpServletRequest request) {
        String requestedBy = request.getHeader(Constants.AuthConstants.X_STORE_CODE);
        return (StringUtils.isNotBlank(requestedBy))?requestedBy:null;
    }

    /**
     * 获取请求终端消息SN
     * @param request
     * @return
     */
    protected String getRequestTerminalMqttSn(HttpServletRequest request){
        return request.getHeader(Constants.TerminalConstants.X_TERMINAL_MQTT_SN);
    }

    private <R>R getResult(Supplier<R> func) throws ParamException, BusinessException, Exception {
        return func.get();
    }
    protected <R>Result<R> tryCatch(Supplier<R> func){

        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = Optional.ofNullable(servletRequestAttributes)
                .map(ServletRequestAttributes::getRequest)
                .orElse(null);
        String uri = "";
        String arguments = "";
        if(Objects.nonNull(request)) {
            uri = request.getRequestURI();
            arguments = JSONObject.toJSONString(request.getParameterMap());
        }

        try {
            return Result.success(getResult(func));
        } catch (ParamException e) {
            LOGGER.warn("caught controller param error",
                    keyValue("uri", uri),
                    keyValue("arguments", arguments),
                    e);
            return Result.error(CommonErrorCodeEnum.PARAMETER_ERROR.getCode(), e.getMessage());
        } catch (BusinessException e) {
            String errorCode = Optional.ofNullable(e.getErrorCode())
                    .map(CodeDescEnum::getCode)
                    .orElse(ErrorCodeEnum.BUSINESS_ERROR.getErrCode());
            if(null != e.getCause()){
                LOGGER.error("caught controller business error",
                        keyValue("uri", uri),
                        keyValue("arguments", arguments),
                        keyValue("errorCode", errorCode),
                        e);
            }else {
                LOGGER.warn("caught controller business error",
                        keyValue("uri", uri),
                        keyValue("arguments", arguments),
                        keyValue("errorCode", errorCode));
            }
            return Result.error(errorCode, e.getMessage());
        } catch (Exception e) {
            LOGGER.error("caught controller error",
                    keyValue("uri", uri),
                    keyValue("arguments", arguments),
                    e);
            return Result.error(CommonErrorCodeEnum.SYSTEM_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 获取cashier/retail客户端版本号
     * @param request
     * @return
     */
    protected String getRequestedAppVersion(HttpServletRequest request) {
        return CashierUtil.getRequestedAppVersion(this.getRequestUserAgent(request));
    }

    /**
     * 获取TV客户端版本号
     * @param request
     * @return
     */
    protected String getRequestedTvAppVersion(HttpServletRequest request) {
        String appVersion = "1.0.0";
        String appVersionStr = getRequestTvAppVersion(request);
        if (StringUtils.isBlank(appVersionStr)){
            return appVersion;
        }
        return appVersionStr;
    }


    protected String getRequestUserAgent(HttpServletRequest request){
        return request.getHeader(UcConstants.RequestConstants.USER_AGENT_HEADER);
    }

    protected String getRequestTvAppVersion(HttpServletRequest request){
        return request.getHeader(UcConstants.RequestConstants.APP_VERSION_HEADER);
    }

}
