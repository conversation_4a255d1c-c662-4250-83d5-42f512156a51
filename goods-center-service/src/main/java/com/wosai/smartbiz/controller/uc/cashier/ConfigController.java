package com.wosai.smartbiz.controller.uc.cashier;

import com.shouqianba.external.platform.adapter.ExternalCouponV2Adapter;
import com.shouqianba.external.platform.enums.ExternalCouponPlatform;
import com.wosai.market.mcc.api.dto.response.ConfigResponse;
import com.wosai.smartbiz.Constants;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.controller.uc.BaseController;
import com.wosai.smartbiz.dto.req.ConfigRemoteReq;
import com.wosai.smartbiz.enums.config.CommonConfigNameEnum;
import com.wosai.smartbiz.gds.enums.ErrorCodeEnum;
import com.wosai.smartbiz.gds.enums.config.ConfigOwnerTypeEnum;
import com.wosai.smartbiz.gds.service.CashierConfigService;
import com.wosai.smartbiz.gds.service.CommonConfigService;
import com.wosai.smartbiz.gds.service.SecondaryScreenRpcService;
import com.wosai.smartbiz.gds.vo.SecondaryScreenVO;
import com.wosai.smartbiz.strategy.config.OrderConfigStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: KangYiJun
 * @date: 2019-12-23
 */
@Slf4j
@RestController
@RequestMapping("/cashier")
public class ConfigController extends BaseController {

    @Autowired
    private CommonConfigService commonConfigService;

    @Autowired
    private CashierConfigService cashierConfigService;

    @Resource
    private ExternalCouponV2Adapter externalCouponV2Adapter;

    @Autowired
    private SecondaryScreenRpcService secondaryScreenRpcService;

    /**
     * 收银机配置
     * 2021-12-08备注：此j接口为老版本接口2.14.0y以下版本收银机使用，新版本收银机使用/v2/config接口
     * @param configRemoteReq
     * @return
     */
    @PostMapping("/config")
    public Result<Map<String,String>> config(ConfigRemoteReq configRemoteReq) {
        if (StringUtils.isEmpty(configRemoteReq.getOwnerId())) {
            return Result.error(ErrorCodeEnum.BUSINESS_ERROR);
        }

        List<CommonConfigNameEnum> nameList = new OrderConfigStrategy().getConfigNameList();

        Result<List<ConfigResponse>> result = commonConfigService.getConfigList(configRemoteReq.getOwnerId(), nameList);
        if (!result.isSuccess()) {
            return Result.error(result.getResult());
        }

        List<ConfigResponse> configResponses = result.getData();

        Map<String, String> map = configResponses.stream().collect(Collectors.toMap(ConfigResponse::getName, ConfigResponse::getValue));

        for (CommonConfigNameEnum nameEnum : nameList) {
            if (!map.containsKey(nameEnum.getCode())) {
                map.put(nameEnum.getCode(), nameEnum.getCommonConfigStrategy().getDefaultValue());
            }
        }

        Map<String, String> resultMap = new HashMap<>();
        if(null != map && !map.isEmpty()){
            Set<String> keySet = map.keySet();
            for (String key : keySet) {
                resultMap.put(key.toLowerCase(), map.get(key));
            }
        }
        //存储收银机是否展示外卖订单标记: 如果开通了自营外卖或者接入了第三方外卖授权
        boolean showTakeout = cashierConfigService.authTripartiteTakeout(configRemoteReq.getOwnerId()) || cashierConfigService.takeoutActivated(configRemoteReq.getOwnerId());
        resultMap.put(Constants.CashierConfigNameConstants.IS_CASHIER_TAKEOUT_ORDER_KEY, showTakeout?"Y":"N");

        boolean isMeiTuanStoreAuthorized = false;
        boolean isDouYinStoreAuthorized = false;
        try {
            // 查询美团抖音券核销的店铺授权状态
            List<Map<String, Object>> authInfo = externalCouponV2Adapter.getAuthInfo(Collections.singletonMap(ConfigOwnerTypeEnum.STORE_ID.getCode(), configRemoteReq.getOwnerId()));
            // 对 coupon_auth_infos 进行包装,分别检查美团和抖音的授权
            isMeiTuanStoreAuthorized = authInfo.stream().anyMatch(info -> info.get("status").equals(1) && info.get("platform").equals(ExternalCouponPlatform.MEI_TUAN.name()));
            isDouYinStoreAuthorized = authInfo.stream().anyMatch(info -> info.get("status").equals(1) && info.get("platform").equals(ExternalCouponPlatform.DOU_YIN.name()));
        } catch (Exception e) {
            log.error("查询美团抖音券核销的店铺授权状态失败", e);
        }
        resultMap.put(Constants.CashierConfigNameConstants.IS_MEITUAN_STORE_COUPON_AUTHORIZED_KEY, isMeiTuanStoreAuthorized ? "Y" : "N");
        resultMap.put(Constants.CashierConfigNameConstants.IS_DOUYIN_STORE_COUPON_AUTHORIZED_KEY, isDouYinStoreAuthorized ? "Y" : "N");
        resultMap.put(Constants.CashierConfigNameConstants.IS_INVOICE_STORE, cashierConfigService.isInvoiceStore(configRemoteReq.getOwnerId()) ? "Y" : "N");

        // 查询商户是否需要储值核销验证
        if (null != configRemoteReq.getMerchantId()) {
            boolean isNeedRedeemVerify = cashierConfigService.isNeedRedeemVerify(configRemoteReq.getMerchantId());
            resultMap.put(Constants.CashierConfigNameConstants.IS_NEED_REDEEM_VERIFY, isNeedRedeemVerify ? "Y" : "N");
        }

        // 是否开启使用支付宝、微信付款码核销储值余额开关
        boolean enableUseBalancePayment = cashierConfigService.enableUseBalancePayment(configRemoteReq.getOwnerId());
        resultMap.put(Constants.CashierConfigNameConstants.ENABLE_USE_BALANCE_PAYMENT, enableUseBalancePayment ? "Y" : "N");

        return Result.success(resultMap);
    }

    /**
     * 新版收银机配置接口，去除mcc配置读取，mcc配置获取接口已经迁移到gateway-node，收银机V2.14.0以上版本使用
     * @param configRemoteReq
     * @return
     */
    @PostMapping("/config/v2")
    public Result<Map<String,String>> configV2(ConfigRemoteReq configRemoteReq) {
        if (StringUtils.isEmpty(configRemoteReq.getOwnerId())) {
            return Result.error(ErrorCodeEnum.BUSINESS_ERROR);
        }
        Map<String, String> resultMap = new HashMap<>();
        //存储收银机是否展示外卖订单标记: 如果开通了自营外卖或者接入了第三方外卖授权
        boolean showTakeout = cashierConfigService.authTripartiteTakeout(configRemoteReq.getOwnerId()) || cashierConfigService.takeoutActivated(configRemoteReq.getOwnerId());
        resultMap.put(Constants.CashierConfigNameConstants.IS_CASHIER_TAKEOUT_ORDER_KEY, showTakeout?"Y":"N");

        // 查询美团抖音券核销的店铺授权状态
        List<Map<String, Object>> authInfo = externalCouponV2Adapter.getAuthInfo(Collections.singletonMap(ConfigOwnerTypeEnum.STORE_ID.getCode(), configRemoteReq.getOwnerId()));
        // 对 coupon_auth_infos 进行包装,分别检查美团和抖音的授权
        boolean isMeiTuanStoreAuthorized = authInfo.stream().anyMatch(info -> info.get("status").equals(1) && info.get("platform").equals("MEI_TUAN"));
        boolean isDouYinStoreAuthorized = authInfo.stream().anyMatch(info -> info.get("status").equals(1) && info.get("platform").equals("DOU_YIN"));

        resultMap.put(Constants.CashierConfigNameConstants.IS_MEITUAN_STORE_COUPON_AUTHORIZED_KEY, isMeiTuanStoreAuthorized ? "Y" : "N");
        resultMap.put(Constants.CashierConfigNameConstants.IS_DOUYIN_STORE_COUPON_AUTHORIZED_KEY, isDouYinStoreAuthorized ? "Y" : "N");
        resultMap.put(Constants.CashierConfigNameConstants.IS_INVOICE_STORE, cashierConfigService.isInvoiceStore(configRemoteReq.getOwnerId()) ? "Y" : "N");

        // 查询商户是否需要储值核销验证
        if (null != configRemoteReq.getMerchantId()) {
            boolean isNeedRedeemVerify = cashierConfigService.isNeedRedeemVerify(configRemoteReq.getMerchantId());
            resultMap.put(Constants.CashierConfigNameConstants.IS_NEED_REDEEM_VERIFY, isNeedRedeemVerify ? "Y" : "N");
        }

        // 是否开启使用支付宝、微信付款码核销储值余额开关
        boolean enableUseBalancePayment = cashierConfigService.enableUseBalancePayment(configRemoteReq.getOwnerId());
        resultMap.put(Constants.CashierConfigNameConstants.ENABLE_USE_BALANCE_PAYMENT, enableUseBalancePayment ? "Y" : "N");

        return Result.success(resultMap);
    }

    /**
     * 获取收银机副屏配置信息
     * @param storeId
     * @return
     */
    @PostMapping("/config/secondary/screen")
    public Result<SecondaryScreenVO> secondaryScreenConfig(@RequestParam(value = "storeId") String storeId) {
        if (StringUtils.isBlank(storeId)) {
            return Result.error(ErrorCodeEnum.BUSINESS_ERROR);
        }
        return Result.success(secondaryScreenRpcService.secondaryScreenDetail(storeId));
    }
}
