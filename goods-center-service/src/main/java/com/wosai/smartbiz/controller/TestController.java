package com.wosai.smartbiz.controller;

import com.alibaba.fastjson.JSON;
import com.wosai.market.awesome.message.enums.NoticeType;
import com.wosai.smartbiz.apollo.ApolloConfigHelper;
import com.wosai.smartbiz.base.exceptions.BusinessException;
import com.wosai.smartbiz.controller.uc.BaseController;
import com.wosai.smartbiz.gds.service.NotifyService;
import com.wosai.smartbiz.goods.util.OkHttpUtils;
import com.wosai.smartbiz.uc.manager.DeviceControlService;
import com.wosai.smartbiz.uc.manager.StoreV2Service;
import com.wosai.smartbiz.user.user.enums.UcErrorCodeEnum;
import com.wosai.smartbiz.user.user.vo.CashierStoreVO;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/test")
@Slf4j
public class TestController extends BaseController {

    @Autowired
    private NotifyService notifyService;
    @Autowired
    private DeviceControlService deviceControlService;
    @Autowired
    private StoreV2Service storeV2Service;

    @Autowired
    private ApolloConfigHelper apolloConfigHelper;

    @Value("${something_secret}")
    private String apolloTestKey;

    @PostMapping(value = "/websocket")
    public void testWebsocket(@RequestParam(value = "storeId", required = false) String storeId) {
        Map<String, Object> detailMap = new HashMap<>();
        detailMap.put("storeId", storeId);
        // 构造请求体
        OkHttpUtils.RequestBodyCreator requestBodyCreator = () -> new FormBody.Builder()
                .add("type", NoticeType.SYSTEM_MESSAGE.name())
                .add("storeId", storeId)
                .build();
        notifyService.notifyToGw(storeId, requestBodyCreator);
    }

    @PostMapping(value = "/device/check")
    public boolean deviceCheck(@RequestParam(value = "storeId", required = true) String storeId,
                               HttpServletRequest request) {
        CashierStoreVO cashierStoreVO = storeV2Service.getCashierStoreByStoreId(storeId);
        if (null == cashierStoreVO) {
            throw new BusinessException(UcErrorCodeEnum.STORE_NOT_EXISTS);
        }
        return deviceControlService.cashierActiveControl(cashierStoreVO, getRequestDeviceParam(request));
    }

    @GetMapping(value = "/apollo1")
    public String apollo1(HttpServletRequest request) {
        log.info("apollo info, apolloTestKey:{}", apolloTestKey);
        return apolloTestKey;
    }

    @GetMapping(value = "/apollo2")
    public String apollo2(HttpServletRequest request) {
        String apolloConfigStr = JSON.toJSONString(apolloConfigHelper.getApolloConfig());
        log.info("apollo info, apolloConfigStr:{}", apolloConfigStr);
        return apolloConfigStr;
    }

}
