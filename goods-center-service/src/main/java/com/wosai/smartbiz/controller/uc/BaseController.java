package com.wosai.smartbiz.controller.uc;

import com.wosai.smartbiz.base.enums.SceneEnum;
import com.wosai.smartbiz.enums.TerminalTypeEnum;
import com.wosai.smartbiz.retail.util.CashierUtil;
import com.wosai.smartbiz.uc.constants.Constants;
import com.wosai.smartbiz.user.user.constants.UcConstants;
import com.wosai.smartbiz.user.user.enums.AppTypeEnum;
import com.wosai.smartbiz.user.user.enums.OsEnum;
import com.wosai.smartbiz.user.user.enums.ScanChannelEnum;
import com.wosai.smartbiz.user.user.login.DeviceParam;
import eu.bitwalker.useragentutils.UserAgent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Objects;

@Slf4j
public class BaseController {

    /**
     * 获取用户UA,判断扫码请求渠道
     * @return
     */
    protected ScanChannelEnum getScanChannel(HttpServletRequest request) {
        String userAgentString = getRequestUserAgent(request);
        if (StringUtils.isBlank(userAgentString)) {
            return null;
        }
        //QQ浏览器:"userAgentString":"Mozilla/5.0 (Linux; U; Android 5.0.2; zh-sg; PLK-AL10 Build/HONORPLK-AL10) AppleWebKit/537.36 (KHTML, like Gecko)Version/4.0 MQQBrowser/5.3 Mobile Safari/537.36"
        //支付宝:  "userAgentString":"Mozilla/5.0 (Linux; U; Android 5.0.2; zh-sg; PLK-AL10 Build/HONORPLK-AL10) AppleWebKit/537.36 (KHTML, like Gecko)Version/4.0 MQQBrowser/5.3 Mobile Safari/537.36"
        //微信:    "userAgentString":"Mozilla/5.0 (Linux; Android 5.0.2; PLK-AL10 Build/HONORPLK-AL10) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/******** Mobile Safari/537.36 MicroMessenger/6.3.15.49_r8aff805.760 NetType/WIFI Language/zh_CN"
        //百度钱包: "userAgentString":"Mozilla/5.0 (Linux; Android 5.0.2; PLK-AL10 Build/HONORPLK-AL10) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/******** Mobile Safari/537.36 BaiduWallet-********-Android-walletapp_1080_1794_PLK-AL10-HWPLK_21_5.0.2_1.6.0_160"
        //翼支付:   "userAgentString":"Mozilla/5.0 (Linux; Android 5.0.2; PLK-AL10 Build/HONORPLK-AL10) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/******** Mobile Safari/537.36 Bestpay 1.0"

        if (userAgentString.indexOf("MicroMessenger") != -1) {
            return ScanChannelEnum.WECHAT;
        }  else if (userAgentString.indexOf("AlipayClient") != -1) {
            return ScanChannelEnum.ALIPAY;
        } else {
            return ScanChannelEnum.ERROR;
        }
    }

    /**
     * 获取请求User-Agent
     * @param request
     * @return
     */
    protected String getRequestUserAgent(HttpServletRequest request){
        return request.getHeader(UcConstants.RequestConstants.USER_AGENT_HEADER);
    }

    /**
     * 获取请求Authorization
     * @param request
     * @return
     */
    protected String getRequestAuthorization(HttpServletRequest request){
        return request.getHeader(UcConstants.RequestConstants.ID_TOKEN_HEADER);
    }
    /**
     * 获取请求终端ID
     * @param request
     * @return
     */
    protected String getRequestTerminalId(HttpServletRequest request){
        return request.getHeader(Constants.TerminalConstants.X_TERMINAL_ID);
    }

    /**
     * 获取请求终端SN
     * @param request
     * @return
     */
    protected String getRequestTerminalSn(HttpServletRequest request){
        return request.getHeader(Constants.TerminalConstants.X_TERMINAL_SN);
    }

    /**
     * 获取请求设备使用场景,默认标记为餐饮
     * @param request
     * @return
     */
    protected SceneEnum getRequestDeviceScene(HttpServletRequest request){
        String userAgentStr = getRequestUserAgent(request);
        return CashierUtil.getCashierScene(userAgentStr);
    }

    /**
     * 获取请求终端消息SN
     * @param request
     * @return
     */
    protected String getRequestTerminalMqttSn(HttpServletRequest request){
        return request.getHeader(Constants.TerminalConstants.X_TERMINAL_MQTT_SN);
    }

    /**
     * 获取终端型号
     * @param request
     * @return
     */
    protected String getRequestTerminalModel(HttpServletRequest request){
        return request.getHeader(Constants.TerminalConstants.X_TERMINAL_MODEL);
    }

    /**
     * 获权限编码
     * @param request
     * @return
     */
    protected String getRequestPermissionCode(HttpServletRequest request){
        return request.getHeader(Constants.TerminalConstants.X_PERMISSION_CODE);
    }

    /**
     * 获权限授权令牌
     * @param request
     * @return
     */
    protected String getRequestPermissionToken(HttpServletRequest request){
        return request.getHeader(Constants.TerminalConstants.X_PERMISSION_TOKEN);
    }

    /**
     * 判断设备是否是商米设备
     * @param request
     * @return
     */
    protected boolean isSunmiDevice(HttpServletRequest request){
        String userAgentStr = getRequestUserAgent(request);
        if (StringUtils.isBlank(userAgentStr) || !userAgentStr.startsWith("sqb")){
            return false;
        }
        String [] agentsArray = userAgentStr.split(";");
        if(null != agentsArray && agentsArray.length > 0){
            for(String str : Arrays.asList(agentsArray)) {
                boolean sunmiDevice = StringUtils.containsIgnoreCase(str, Constants.UserAgentConstants.SUNMI_DEVICE_FLAG);
                if(sunmiDevice){
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 获取设备屏幕尺寸数据
     * @param request
     * @return
     */
    protected BigDecimal getRequestTerminalScreenSize(HttpServletRequest request){
        try {
            String userAgentStr = getRequestUserAgent(request);
            if (StringUtils.isBlank(userAgentStr) || !userAgentStr.startsWith("sqb")){
                return null;
            }
            String [] agentsArray = userAgentStr.split(";");
            if(null != agentsArray && agentsArray.length > 0){
                for(String str : Arrays.asList(agentsArray)) {
                    boolean deviceScreenExists = StringUtils.containsIgnoreCase(str, Constants.UserAgentConstants.DEVICE_SCREEN_FLAG);
                    if(deviceScreenExists){
                        BigDecimal screenSize = null;
                        String screenString = str.replaceFirst(Constants.UserAgentConstants.DEVICE_SCREEN_FLAG, "");
                        if(StringUtils.isNotBlank(screenString)){
                            screenSize = new BigDecimal(screenString);
                        }
                        return screenSize;
                    }
                }
            }
            return null;
        }catch (Exception ex) {
            return null;
        }
    }

    /**
     * 获取终端类型
     * @param request
     * @return
     */
    protected TerminalTypeEnum getRequestTerminalType(HttpServletRequest request){
        String terminalType = request.getHeader(Constants.TerminalConstants.X_TERMINAL_TYPE);
        if(StringUtils.isBlank(terminalType)){
            return TerminalTypeEnum.CASHIER;
        } else if(Objects.equals(terminalType, TerminalTypeEnum.CASHIER.name())){
            return TerminalTypeEnum.CASHIER;
        }else if(Objects.equals(terminalType, TerminalTypeEnum.MOBILE.name())){
            return TerminalTypeEnum.MOBILE;
        }else {
            return TerminalTypeEnum.CASHIER;
        }
    }

    /**
     * 获取请求终端经度
     * @param request
     * @return
     */
    protected String getRequestTerminalLongitude(HttpServletRequest request){
        return request.getHeader(Constants.TerminalConstants.X_TERMINAL_LONGITUDE);
    }

    /**
     * 获取请求终端纬度
     * @param request
     * @return
     */
    protected String getRequestTerminalLatitude(HttpServletRequest request){
        return request.getHeader(Constants.TerminalConstants.X_TERMINAL_LATITUDE);
    }

    protected String getRequestedSqbUserId(HttpServletRequest request) {
        return request.getHeader(Constants.AuthConstants.X_SQB_USERID);
    }

    /**
     * 获取访问信息
     * @param request
     * @return
     */
    protected DeviceParam getRequestDeviceParam(HttpServletRequest request){
        String userAgentStr = getRequestUserAgent(request);
        if (StringUtils.isBlank(userAgentStr) || !userAgentStr.startsWith("sqb")){
            return DeviceParam.unknownDevice();
        }
        //sqb_cashier_dinner android/2.12.0;brand/Android;model/MuMu;Android/6.0.1;
        DeviceParam deviceParam = new DeviceParam();
        //这是我们自定义的user-agent
        String [] agentsArray = userAgentStr.split(";");

        if(null != agentsArray){
            if (agentsArray.length > 0){
                String [] appTypeArray = agentsArray[0].split(" ");
                if (appTypeArray[0].contains(Constants.UserAgentConstants.CASHIER_FLAG)){
                    deviceParam.setAppType(AppTypeEnum.CASHIER);
                }
            }
            if (agentsArray.length > 1){
                String [] deviceTypeArray = agentsArray[0].split(" ");
                deviceParam.setDeviceType(deviceTypeArray[0]);
            }
            if (agentsArray.length > 2){
                String [] osTypeArray = agentsArray[0].split(" ");

                OsEnum osType = EnumUtils.getEnum(OsEnum.class,osTypeArray[0]);
                if (osType == null){
                    osType = OsEnum.UNKNOWN;
                }
                deviceParam.setOs(osType);
            }
        }

        deviceParam.setUserAgent(userAgentStr);
        deviceParam.setSunmiDevice(isSunmiDevice(request));
        deviceParam.setDeviceScene(CashierUtil.getCashierScene(userAgentStr));

        String appVersion = getRequestedAppVersion(request);
        String deviceId = getRequestTerminalId(request);
        String deviceFp = getRequestTerminalSn(request);
        String deviceMqttFp = getRequestTerminalMqttSn(request);
        String terminalModel = getRequestTerminalModel(request);
        String terminalBrand = getRequestTerminalBrand(request);
        //BigDecimal terminalScreen = getRequestTerminalScreenSize(request);

        deviceParam.setDeviceId(deviceId);
        deviceParam.setDeviceFp(deviceFp);
        deviceParam.setDeviceMqttFp(deviceMqttFp);
        deviceParam.setTerminalModel(terminalModel);
        deviceParam.setTerminalBrand(terminalBrand);
        //deviceParam.setTerminalScreen(terminalScreen);
        deviceParam.setAppVersion(appVersion);
        deviceParam.setIp(getIpAddress(request));

        return deviceParam;
    }

    /**
     * 获取浏览器信息
     * @param request
     * @return
     */
    protected UserAgent getUserAgent(HttpServletRequest request) {
        String userAgentStr = request.getHeader("User-Agent");
        return UserAgent.parseUserAgentString(userAgentStr);
    }

    /**
     * 获取请求的IP地址
     *
     * @return
     */
    protected String getIpAddr(HttpServletRequest request) {
        return request.getHeader(Constants.AuthConstants.X_REQUESTED_IP);
    }

    /**
     * 获取收银机版本号
     * @param request
     * @return
     */
    protected String getRequestedAppVersion(HttpServletRequest request) {
        String appVersion = "1.0.0";
        String userAgentStr = getRequestUserAgent(request);
        if (StringUtils.isBlank(userAgentStr)){
            return appVersion;
        }
        //sqb_cashier_dinner android/2.12.0;brand/Android;model/MuMu;Android/6.0.1;
        if (userAgentStr.startsWith("sqb")){
            String [] agentsArray = userAgentStr.split(";");
            if (null != agentsArray && agentsArray.length > 0) {
                String[] appInfoArray = agentsArray[0].split(" ");
                if(appInfoArray.length > 1){
                    String[] versionArray = appInfoArray[1].split("/");
                    if(versionArray.length > 1){
                        appVersion = versionArray[1];
                    }
                }
            }
        }
        return appVersion;
    }

    /**
     * 获取收银机品牌
     * @param request
     * @return
     */
    protected String getRequestTerminalBrand(HttpServletRequest request) {
        String terminalBrand = null;
        String userAgentStr = getRequestUserAgent(request);
        if (StringUtils.isBlank(userAgentStr)){
            return terminalBrand;
        }
        //sqb_cashier_dinner android/2.12.0;brand/Android;model/MuMu;Android/6.0.1;
        if (userAgentStr.startsWith("sqb")){
            String [] agentsArray = userAgentStr.split(";");
            if (null != agentsArray && agentsArray.length >= 1 && StringUtils.isNotBlank(agentsArray[1])) {
                terminalBrand = agentsArray[1].replaceFirst("brand/", "");
            }
        }
        return terminalBrand;
    }

    /**
     * 获取请求的IP地址
     *
     * @return
     */
    protected String getIpAddress(HttpServletRequest request) {
        String ip = null;
        try {
            if (null == request) {
                return null;
            }
            String x_forwarded_for = request.getHeader("X-Forwarded-For");
            if (StringUtils.isNotBlank(x_forwarded_for)) {
                if (x_forwarded_for.length() > 15) {
                    String[] ipArray = StringUtils.split(x_forwarded_for, ",");
                    for (String ipStr : ipArray) {
                        ip = ipStr;
                        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
                            continue;
                        } else {
                            break;
                        }
                    }
                }else {
                    ip = x_forwarded_for;
                }
                if (!isValidAddress(ip)) {
                    ip = null;
                }
            }

            if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("Proxy-Client-IP");
                if (!isValidAddress(ip)) {
                    ip = null;
                }
            }

            if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("WL-Proxy-Client-IP");
                if (!isValidAddress(ip)) {
                    ip = null;
                }
            }

            if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("HTTP_CLIENT_IP");
                if (!isValidAddress(ip)) {
                    ip = null;
                }
            }

            if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("X-Real-IP");
                if (!isValidAddress(ip)) {
                    ip = null;
                }
            }

            if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getRemoteAddr();
                if (!isValidAddress(ip)) {
                    ip = null;
                }
            }
        } catch (Exception ex) {
            log.error("get user ip address error", ex);
        }
        return ip;
    }

    private boolean isValidAddress(String ip) {
        if (ip == null) {
            return false;
        } else {
            for(int i = 0; i < ip.length(); ++i) {
                char ch = ip.charAt(i);
                if ((ch < '0' || ch > '9') && (ch < 'A' || ch > 'F') && (ch < 'a' || ch > 'f') && ch != '.' && ch != ':') {
                    return false;
                }
            }

            return true;
        }
    }
}
