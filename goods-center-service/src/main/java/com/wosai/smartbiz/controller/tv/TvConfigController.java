package com.wosai.smartbiz.controller.tv;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.wosai.market.mcc.api.dto.request.BatchFindConfigByNameRequest;
import com.wosai.market.mcc.api.dto.response.ConfigResponse;
import com.wosai.market.mcc.api.enums.AppId;
import com.wosai.market.mcc.api.enums.OwnerType;
import com.wosai.market.mcc.api.service.ConfigRemoteService;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.service.StoreService;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.gds.dto.tv.TvBaseDTO;
import com.wosai.smartbiz.gds.dto.tv.TvCallConfigDTO;
import com.wosai.smartbiz.gds.enums.ErrorCodeEnum;
import com.wosai.smartbiz.gds.service.StoreTvRpcService;
import com.wosai.smartbiz.gds.vo.StoreTvDeviceVO;
import com.wosai.smartbiz.goods.constants.MccConfigConstants;
import com.wosai.smartbiz.message.MqttConfig;
import com.wosai.smartbiz.user.user.enums.UcErrorCodeEnum;
import com.wosai.smartbiz.utils.RmqxUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: redhat
 * @date: 2022-01-19
 */
@RestController
@RequestMapping("/tv/config")
public class TvConfigController{

    @Autowired
    private ConfigRemoteService sqbConfigRemoteService;

    @Autowired
    private StoreTvRpcService storeTvRpcService;

    @Autowired
    private StoreService sqbStoreService;

    @Autowired
    private MqttConfig mqttConfig;

    @Autowired
    private RmqxUtil rmqxUtil;

    /**
     * 获取门店叫号配置信息
     * @param body
     * @return
     */
    @PostMapping("/store/call")
    public Result<TvCallConfigDTO> storeCallConfig(TvBaseDTO body) {
        if (null == body) {
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR);
        }
        if(StringUtils.isBlank(body.getStoreId())){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), "门店ID不能为空");
        }

        String storeId = body.getStoreId();
        StoreInfo storeInfo = sqbStoreService.getStoreById(storeId, null);
        if(null == storeInfo){
            return Result.error(UcErrorCodeEnum.STORE_NOT_EXISTS);
        }

        BatchFindConfigByNameRequest batchFindRequest = new BatchFindConfigByNameRequest();
        batchFindRequest.setAppId(AppId.UFOOD.getAppId());
        batchFindRequest.setOwnerType(OwnerType.STORE_ID.getOwnerType());
        batchFindRequest.setOwnerId(storeId);

        List<String> names = Lists.newArrayList(MccConfigConstants.ORDER_CALL_FINISH_DURATION,
                MccConfigConstants.ORDER_CALL_COUNT,
                MccConfigConstants.ORDER_CALL_DISPLAY_DURATION,
                MccConfigConstants.ORDER_CALL_DISPLAY_TYPE
        );

        batchFindRequest.setNames(names);
        List<ConfigResponse> responses = sqbConfigRemoteService.batchFindByNames(batchFindRequest);

        TvCallConfigDTO configDTO = new TvCallConfigDTO();
        if(CollectionUtils.isEmpty(responses)){
            return Result.success(configDTO);
        }
        responses.stream().forEach(response -> {
            if(StringUtils.isNotBlank(response.getValue())){
                switch (response.getName()) {
                    case MccConfigConstants.ORDER_CALL_FINISH_DURATION:
                        configDTO.setCallFinishDuration(Integer.parseInt(response.getValue()));
                        break;
                    case MccConfigConstants.ORDER_CALL_COUNT:
                        configDTO.setCallCount(Integer.parseInt(response.getValue()));
                        break;
                    case MccConfigConstants.ORDER_CALL_DISPLAY_DURATION:
                        configDTO.setCallDisplayDuration(Integer.parseInt(response.getValue()));
                        break;
                    case MccConfigConstants.ORDER_CALL_DISPLAY_TYPE:
                        configDTO.setCallDisplayType(JSONObject.parseArray(response.getValue(), String.class));
                        break;
                }
            }
        });
        return Result.success(configDTO);
    }

    /**
     * 获取设备叫号配置信息
     * @param body
     * @return
     */
    @PostMapping("/device/call")
    public Result<StoreTvDeviceVO> deviceCallConfig(TvBaseDTO body) {
        if(null == body){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR);
        }
        if(StringUtils.isBlank(body.getStoreId())){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), "门店ID不能为空");
        }
        if(StringUtils.isBlank(body.getSn())){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), "设备SN不能为空");
        }

        String storeId = body.getStoreId();
        StoreInfo storeInfo = sqbStoreService.getStoreById(storeId, null);
        if(null == storeInfo){
            return Result.error(UcErrorCodeEnum.STORE_NOT_EXISTS);
        }

        StoreTvDeviceVO storeTvDeviceVO = storeTvRpcService.tvUseDeviceAndConfigDetail(body.getMerchantId(), body.getStoreId(), body.getSn());
        if(null == storeTvDeviceVO){
            return Result.error(ErrorCodeEnum.SYSTEM_ERROR);
        }
        return Result.success(storeTvDeviceVO);
    }

    /**
     * 获取设备详细信息
     * @param body
     * @return
     */
    @PostMapping("/device/detail")
    public Result<StoreTvDeviceVO> deviceDetail(TvBaseDTO body) {
        if(null == body){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR);
        }
        if(StringUtils.isBlank(body.getSn())){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), "设备SN不能为空");
        }

        StoreTvDeviceVO storeTvDeviceVO = storeTvRpcService.tvUseDeviceDetail(body.getSn());
        if(null == storeTvDeviceVO){
            return Result.error(ErrorCodeEnum.TV_DEVICE_NOT_EXISTS);
        }
        //公钥不返回到前端
        storeTvDeviceVO.setPublicKey(null);
        return Result.success(storeTvDeviceVO);
    }
}
