package com.wosai.smartbiz.controller.gds.repast.cashier;

import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.controller.gds.BaseController;
import com.wosai.smartbiz.goods.pojo.AuthLoginUser;
import com.wosai.smartbiz.gds.service.ProductRepastStockService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;

@RestController
@RequestMapping("/gds/repast/cashier/stock")
@Api("收银台商品库存管理相关接口")
public class CashierProductStockController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(CashierProductStockController.class);

    @Autowired
    private ProductRepastStockService productRepastStockService;

    @ApiOperation("商品沽清设置")
    @PostMapping("/setting")
    public Result<Boolean> setting(
            @RequestParam(value = "spuId") String spuId,
            @RequestParam(value = "quantity") BigDecimal quantity,
            HttpServletRequest request) {
        AuthLoginUser user = getRequestedUser(request);
       return productRepastStockService.setting(user, spuId, quantity);
    }

    @ApiOperation("删除商品沽清设置")
    @PostMapping("/delete")
    public Result<Boolean> delete(
            @RequestParam(value = "spuId") String spuId,
            HttpServletRequest request) {
        AuthLoginUser user = getRequestedUser(request);
        return productRepastStockService.delete(user, spuId);
    }

    @ApiOperation("清除门店沽清设置")
    @PostMapping("/clean")
    public Result<Boolean> clean(
            HttpServletRequest request) {
        AuthLoginUser user = getRequestedUser(request);
        return productRepastStockService.clean(user);
    }

}
