package com.wosai.smartbiz.controller.gds;

import com.wosai.smartbiz.Constants;
import com.wosai.smartbiz.apollo.ApolloConfigHelper;
import com.wosai.smartbiz.base.enums.CommonErrorCodeEnum;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.gds.service.EnvService;
import com.wosai.smartbiz.gds.service.VersionService;
import com.wosai.smartbiz.goods.direct.VersionDAO;
import com.wosai.smartbiz.goods.direct.VersionHuiduDAO;
import com.wosai.smartbiz.goods.domain.common.VersionDO;
import com.wosai.smartbiz.goods.domain.common.VersionHuiduDO;
import com.wosai.smartbiz.uc.manager.StoreV2Service;
import com.wosai.smartbiz.vo.gds.VersionVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by beiyu on 2018/11/14.
 */
@RestController
@RequestMapping("/version")
@Api("版本号管理接口")
public class VersionController extends BaseController{

    @Autowired
    VersionDAO versionDAO;

    @Autowired
    VersionHuiduDAO versionHuiduDAO;

    @Autowired
    EnvService envService;

    @Autowired
    StoreV2Service storeV2Service;

    @Autowired
    ApolloConfigHelper apolloConfigHelper;

    @Autowired
    private VersionService versionService;

    @ApiOperation("获取最新的版本信息")
    @PostMapping("/latest")
    public Result<VersionVO> latest(
            @RequestParam("app") String app,
            @RequestParam(value = "storeId",required = false) String storeId,
            HttpServletRequest request
    ) {
        String currentVersion = null;
        if (Objects.equals(app, Constants.AppMarkConstants.RDS)){
            currentVersion = getRequestTvAppVersion(request);
        }else {
            currentVersion = getRequestedAppVersion(request);
        }

        VersionVO versionVO = versionService.latest(app, storeId, currentVersion);
        return Result.success(versionVO);
    }


    @ApiOperation("获取版本列表")
    @PostMapping("/list")
    public Result<List<VersionVO>> findVersionList(
            @RequestParam("app") String app){
        List<VersionDO> versionDOS = versionDAO.findVersionListByApp(app);
        if (CollectionUtils.isEmpty(versionDOS)){
            return Result.success(Collections.emptyList());
        }

        List<VersionVO> versionVOS = versionDOS.stream().map(versionDO -> {
            VersionVO versionVO = new VersionVO();
            BeanUtils.copyProperties(versionDO, versionVO);
            return versionVO;
        }).collect(Collectors.toList());
        return Result.success(versionVOS);
    }

    //获取当前的灰度信息
    @ApiOperation("获取灰度列表")
    @PostMapping("/huidu/list")
    public Result<List<HuiduVersion>> huiduVersionList(
            @RequestParam(value = "app",required = false) String app){
        if (StringUtils.isBlank(app)){
            List<VersionHuiduDO> huiduDOS = versionHuiduDAO.findAllVersionList();
            List<HuiduVersion> huiduList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(huiduDOS)){
                Map<String,List<VersionHuiduDO>> appMap = huiduDOS.stream().collect(Collectors.groupingBy(VersionHuiduDO::getApp));

                appMap.entrySet().stream().forEach(entry -> {
                    List<VersionHuiduDO> versionList = entry.getValue();
                    //按照版本号在group一把
                    Map<String,List<VersionHuiduDO>> versionMap = versionList.stream().collect(Collectors.groupingBy(VersionHuiduDO::getVersionNo));
                    versionMap.entrySet().stream().forEach(versionEntry -> {
                        //每一个version，都是一个目标对象
                        HuiduVersion huiduVersion = new HuiduVersion();
                        huiduVersion.setApp(entry.getKey());
                        huiduVersion.setVersion(versionEntry.getKey());
                        huiduVersion.setStoreList(StringUtils.join(versionEntry.getValue().stream().map(VersionHuiduDO::getStoreId).collect(Collectors.toList()),","));
                        huiduList.add(huiduVersion);
                    });

                });
            }
            return Result.success(huiduList);
        }else {
            List<VersionHuiduDO> list = versionHuiduDAO.findVersionList(app);
            if (CollectionUtils.isEmpty(list)) {
                return Result.success(Collections.emptyList());
            }

            Map<String, List<VersionHuiduDO>> versionMap = list.stream().collect(Collectors.groupingBy(VersionHuiduDO::getVersionNo));

            List<HuiduVersion> huiduList = versionMap.entrySet().stream().map(entry -> {
                HuiduVersion huiduVersion = new HuiduVersion();
                huiduVersion.setApp(app);
                huiduVersion.setVersion(entry.getKey());
                huiduVersion.setStoreList(StringUtils.join(entry.getValue().stream().map(VersionHuiduDO::getStoreId).collect(Collectors.toList()), ","));
                return huiduVersion;
            }).collect(Collectors.toList());
            return Result.success(huiduList);
        }
    }


    @Getter
    @Setter
    public static class HuiduVersion{
        private String app;

        private String version;

        private String storeList;
    }


    //删除
    @ApiOperation("删除灰度信息")
    @PostMapping("/huidu/delete")
    public Result<Boolean> deleteHuiduVersion(
            @RequestParam("app") String app,
            @RequestParam("versionNo") String versionNo){
        if (StringUtils.isBlank(app)){
            return Result.error(CommonErrorCodeEnum.PARAMETER_ERROR.getCode(),"必须指定版本的类型（WINDOWS收银台，ANDROID收银台，采宝小贝PRO，自助收银机等");
        }
        if (StringUtils.isBlank(versionNo)){
            return Result.error(CommonErrorCodeEnum.PARAMETER_ERROR.getCode(),"必须指定灰度的版本号");
        }

        return Result.success(versionHuiduDAO.deleteByVersionNo(app,versionNo));
    }

    @ApiOperation("发布灰度信息")
    @PostMapping("/huidu/publish")
    public Result<Boolean> syncHuidu(){
        List<VersionHuiduDO> huiduDOS = versionHuiduDAO.findAllVersionList();
        List<HuiduVersion> huiduList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(huiduDOS)){
            Map<String,List<VersionHuiduDO>> appMap = huiduDOS.stream().collect(Collectors.groupingBy(VersionHuiduDO::getApp));

             appMap.entrySet().stream().forEach(entry -> {
                List<VersionHuiduDO> versionList = entry.getValue();
                //按照版本号在group一把
                Map<String,List<VersionHuiduDO>> versionMap = versionList.stream().collect(Collectors.groupingBy(VersionHuiduDO::getVersionNo));
                versionMap.entrySet().stream().forEach(versionEntry -> {
                    //每一个version，都是一个目标对象
                    HuiduVersion huiduVersion = new HuiduVersion();
                    huiduVersion.setApp(entry.getKey());
                    huiduVersion.setVersion(versionEntry.getKey());
                    huiduVersion.setStoreList(StringUtils.join(versionEntry.getValue().stream().map(VersionHuiduDO::getStoreId).collect(Collectors.toList()),","));
                    huiduList.add(huiduVersion);
                });

            });
        }
        return Result.success(true);
    }

    @ApiOperation("创建版本信息")
    @PostMapping("/create")
    public Result<Boolean> createVersion(
            VersionVO versionVO){

        if (StringUtils.isBlank(versionVO.getApp())){
            return Result.error(CommonErrorCodeEnum.PARAMETER_ERROR.getCode(),"必须指定版本的类型");
        }

        if (StringUtils.isBlank(versionVO.getDownloadUrl())){
            return Result.error(CommonErrorCodeEnum.PARAMETER_ERROR.getCode(),"当前版本的下载地址必填");
        }
        if (StringUtils.isBlank(versionVO.getDescription())){
            return Result.error(CommonErrorCodeEnum.PARAMETER_ERROR.getCode(),"版本描述必填");
        }

        if (StringUtils.isBlank(versionVO.getMd5())){
            return Result.error(CommonErrorCodeEnum.PARAMETER_ERROR.getCode(),"版本的MD5必填（生成方式如下：命令行下输入：md5 文件路径");
        }

        if (StringUtils.isBlank(versionVO.getVersion())){
            return Result.error(CommonErrorCodeEnum.PARAMETER_ERROR.getCode(),"当前的版本号必填");
        }

        //校验下当前本号是不是已经存在
        VersionDO versionDO = versionDAO.getByVersionNo(versionVO.getApp().trim(), versionVO.getVersion().trim());
        if (versionDO != null){
            return Result.error(CommonErrorCodeEnum.PARAMETER_ERROR.getCode(),"当前版本号已经存在， 请查证");
        }

        if (!versionVO.getDownloadUrl().startsWith("http")){
            String prefix = versionVO.getDownloadUrl().startsWith("/") ? "" : "/";
            if(envService.isOnline()){
                versionVO.setDownloadUrl("http://download.caibaopay.com"+ prefix + versionVO.getDownloadUrl());
            }else if (envService.isPre()){
                versionVO.setDownloadUrl("http://download.caibaopay.com"+ prefix + versionVO.getDownloadUrl());
            }else {
                versionVO.setDownloadUrl("http://a.ci.heyean.com"+ prefix + versionVO.getDownloadUrl());
            }
        }

        VersionDO version = new VersionDO();
        version.setApp(versionVO.getApp().trim());
        version.setClientAction("INSTALL");
        version.setDescription(versionVO.getDescription());
        version.setDownloadUrl(versionVO.getDownloadUrl());
        version.setMd5(versionVO.getMd5());
        version.setMinSupportVersion(versionVO.getMinSupportVersion());
        version.setMode("ONLINE");
        version.setStatus("ACTIVE");
        version.setVersion(versionVO.getVersion());

        return Result.success(versionDAO.createVersion(version));
    }

    @ApiOperation("删除版本信息")
    @PostMapping("/delete")
    public Result<Boolean> deleteVersion(Long id){

        VersionDO dbVersion = versionDAO.getById(id);
        if (dbVersion == null){
            return Result.error(CommonErrorCodeEnum.NOT_EXIST.getCode(),"当前版本号不存在");
        }
        //这里要判断，version是否在使用
        List<VersionHuiduDO> huiduStoreList = versionHuiduDAO.findVersionListByVersionNo(dbVersion.getApp(), dbVersion.getVersion());
        if (CollectionUtils.isNotEmpty(huiduStoreList)) {
            return Result.error(CommonErrorCodeEnum.PARAMETER_ERROR.getCode(), "当前版本号还在被使用，不能被删除");
        }
        return Result.success(versionDAO.deleteById(id));
    }
}
