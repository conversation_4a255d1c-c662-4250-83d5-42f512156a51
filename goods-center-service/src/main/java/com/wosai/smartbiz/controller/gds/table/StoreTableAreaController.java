package com.wosai.smartbiz.controller.gds.table;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.controller.gds.BaseController;
import com.wosai.smartbiz.converter.gds.repast.StoreTableAreaConverter;
import com.wosai.smartbiz.dto.business.area.StoreTableAreaDTO;
import com.wosai.smartbiz.dto.req.table.StoreTableAreaBaseReq;
import com.wosai.smartbiz.dto.req.table.area.StoreTableAreaDeleteReq;
import com.wosai.smartbiz.dto.req.table.area.StoreTableAreaSaveReq;
import com.wosai.smartbiz.dto.req.table.area.StoreTableAreaSortReq;
import com.wosai.smartbiz.dto.vo.table.AreaSortParam;
import com.wosai.smartbiz.dto.vo.table.StoreAreaTableGroupVO;
import com.wosai.smartbiz.dto.vo.table.StoreTableAreaVO;
import com.wosai.smartbiz.gds.dto.area.*;
import com.wosai.smartbiz.gds.service.StoreTableAreaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: KangYiJun
 * @date: 2020-09-01
 */
@RestController
@RequestMapping(value = "/store/area")
public class StoreTableAreaController extends BaseController {

    @Autowired
    private StoreTableAreaService storeTableAreaService;

    /**
     * 保存区域
     * @param req
     * @param bindingResult
     * @param request
     * @return
     */
    @PostMapping(value = "/save")
    public Result<Boolean> save(@Valid StoreTableAreaSaveReq req,
                                BindingResult bindingResult,
                                HttpServletRequest request)
    {
        if(bindingResult.hasErrors()){
            return processErrorResult(bindingResult);
        }

        StoreTableAreaSaveDTO saveDTO = new StoreTableAreaSaveDTO()
                .setMerchantId(req.getMerchantId())
                .setStoreId(req.getStoreId())
                .setUserId(req.getMerchantUserId())
                .setAreaId(req.getAreaId())
                .setAreaName(req.getAreaName());
        return tryCatch(() -> storeTableAreaService.save(saveDTO));
    }

    /**
     * 区域列表
     * @param req
     * @param bindingResult
     * @param request
     * @return
     */
    @PostMapping(value = "/list")
    public Result<List<StoreTableAreaVO>> getList(@Valid StoreTableAreaBaseReq req,
                                                  BindingResult bindingResult,
                                                  HttpServletRequest request)
    {
        if(bindingResult.hasErrors()){
            return processErrorResult(bindingResult);
        }

        return tryCatch(() -> {
            StoreTableAreaQueryDTO queryDTO = new StoreTableAreaQueryDTO()
                    .setMerchantId(req.getMerchantId())
                    .setStoreId(req.getStoreId())
                    .setNeedTableList(Boolean.FALSE)
                    .setNeedTableQrCode(Boolean.FALSE);
            List<StoreTableAreaDTO> areaDTOList = storeTableAreaService.listByQuery(queryDTO);

            return StoreTableAreaConverter.toVOList(areaDTOList);
        });
    }

    /**
     * 删除区域
     * @param req
     * @param bindingResult
     * @param request
     * @return
     */
    @PostMapping(value = "/delete")
    public Result<Boolean> delete(@Valid StoreTableAreaDeleteReq req,
                                  BindingResult bindingResult,
                                  HttpServletRequest request)
    {
        if(bindingResult.hasErrors()){
            return processErrorResult(bindingResult);
        }
        StoreTableAreaDeleteDTO deleteDTO = new StoreTableAreaDeleteDTO()
                .setMerchantId(req.getMerchantId())
                .setStoreId(req.getStoreId())
                .setUserId(req.getMerchantUserId())
                .setAreaId(req.getAreaId());
        return tryCatch(() -> storeTableAreaService.delete(deleteDTO));
    }

    /**
     * 区域排序
     * @param req
     * @param bindingResult
     * @param request
     * @return
     */
    @PostMapping(value = "/sort")
    public Result<Boolean> sort(@Valid StoreTableAreaSortReq req,
                                BindingResult bindingResult,
                                HttpServletRequest request)
    {
        if(bindingResult.hasErrors()){
            return processErrorResult(bindingResult);
        }
        StoreTableAreaSortDTO sortDTO = new StoreTableAreaSortDTO()
                .setMerchantId(req.getMerchantId())
                .setStoreId(req.getStoreId())
                .setUserId(req.getMerchantUserId())
                .setSortMap(req.getSortList().stream().collect(Collectors.toMap(AreaSortParam::getAreaId, AreaSortParam::getSort)));
        return tryCatch(() -> storeTableAreaService.sortBatch(sortDTO));
    }

    /**
     * 桌台按区域分组展示列表
     * @param req
     * @param bindingResult
     * @param request
     * @return
     */
    @PostMapping(value = "/table/group")
    public Result<List<StoreAreaTableGroupVO>> getGroup(@Valid StoreTableAreaBaseReq req,
                                                        BindingResult bindingResult,
                                                        HttpServletRequest request)
    {
        if(bindingResult.hasErrors()){
            return processErrorResult(bindingResult);
        }

        return tryCatch(() -> {
            StoreTableAreaQueryDTO queryDTO = new StoreTableAreaQueryDTO()
                    .setMerchantId(req.getMerchantId())
                    .setStoreId(req.getStoreId())
                    .setNeedTableList(Boolean.TRUE)
                    .setNeedTableQrCode(Boolean.TRUE);
            List<StoreTableAreaDTO> areaDTOList = storeTableAreaService.listByQuery(queryDTO);

            return StoreTableAreaConverter.toGroupVOList(areaDTOList);
        });
    }

    /**
     * 围餐收银台app获取桌台区域
     * @param req
     * @param bindingResult
     * @param request
     * @return
     */
    @PostMapping(value = "/cashier/table/group")
    public Result<List<StoreAreaTableGroupVO>> getGroupByCashier(@Valid StoreTableAreaBaseReq req,
                                                        BindingResult bindingResult,
                                                        HttpServletRequest request) {
        return getGroupByCashier4Sentinel(req, bindingResult, request);
    }

    @SentinelResource(value = "smart-biz/goods-center/store/area/cashier/table/group")
    private Result<List<StoreAreaTableGroupVO>> getGroupByCashier4Sentinel(StoreTableAreaBaseReq req,
                                                                 BindingResult bindingResult,
                                                                 HttpServletRequest request) {
        if(bindingResult.hasErrors()){
            return processErrorResult(bindingResult);
        }
        return tryCatch(() -> storeTableAreaService.getGroupByCashier(req));
    }
}
