package com.wosai.smartbiz.controller.uc;

import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.gds.enums.ErrorCodeEnum;
import com.wosai.smartbiz.uc.manager.UserV2Service;
import com.wosai.smartbiz.user.user.enums.UserRolePlatformEnum;
import com.wosai.smartbiz.user.user.vo.PermissionCheckVO;
import com.wosai.smartbiz.user.user.vo.PermissionVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


@Slf4j
@RestController
@RequestMapping("/permission")
public class PermissionController extends BaseController{

    @Autowired
    UserV2Service userV2Service;

    /**
     * 权限列表
     * @param merchantId
     * @param sqbUserId
     * @param request
     * @return
     */
    @RequestMapping(value="/list", method = RequestMethod.POST)
    public Result<List<PermissionVO>> list(@RequestParam(value = "merchantId", required = false) String merchantId,
                                      @RequestParam(value = "sqbUserId", required = false) String sqbUserId,
                                      HttpServletRequest request) {
        if(StringUtils.isBlank(merchantId)){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), "商户ID不能为空");
        }
        if(StringUtils.isBlank(sqbUserId)){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), "用户ID不能为空");
        }
        List<PermissionVO> permissionVOList = userV2Service.findPermissionList(merchantId, sqbUserId, UserRolePlatformEnum.CASH);
        return Result.success(permissionVOList);
    }

    /**
     * 权限用户验证
     * @param merchantId
     * @param cellphone
     * @param password
     * @param request
     * @return
     */
    @RequestMapping(value="/user/check", method = RequestMethod.POST)
    public Result<PermissionCheckVO> userCheck(@RequestParam(value = "merchantId", required = false) String merchantId,
                                               @RequestParam(value = "cellphone", required = false) String cellphone,
                                               @RequestParam(value = "password", required = false) String password,
                                               HttpServletRequest request){
        if(StringUtils.isBlank(merchantId)){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), "商户ID不能为空");
        }
        if(StringUtils.isBlank(cellphone)){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), "手机号不能空");
        }
        if(StringUtils.isBlank(password)){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), "密码不能为空");
        }

        String permissionCode = getRequestPermissionCode(request);
        if(StringUtils.isBlank(permissionCode)){
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), "权限编码不能为空");
        }

        PermissionCheckVO permissionCheckVO = userV2Service.permissionUserCheck(merchantId, cellphone, password, permissionCode);
        return Result.success(permissionCheckVO);
    }
}
