package com.wosai.smartbiz.controller.gds.repast.v2;

import com.google.common.collect.Lists;
import com.wosai.market.dto.OpenTableMustOrder;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.controller.gds.BaseController;
import com.wosai.smartbiz.dto.req.mustorder.MustOrderReq;
import com.wosai.smartbiz.enums.ChargeMethodEnum;
import com.wosai.smartbiz.gds.jsonrpc.OpenTableMustOrderV2Service;
import com.wosai.smartbiz.vo.gds.gds.v2.OpenTableMustOrderProductVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021/5/25
 */
@RestController
@RequestMapping(value = "/gds/repast/v2/openTable/mustOrder")
public class OpenTableMustOrderController extends BaseController {

    @Autowired
    private OpenTableMustOrderV2Service openTableMustOrderV2Service;

    @PostMapping(value = "/product")
    public Result<List<OpenTableMustOrderProductVO>> listProducts(@RequestParam(value = "storeId") String storeId) {

        return tryCatch(() -> {

            MustOrderReq mustOrderReq = new MustOrderReq();
            mustOrderReq.setBiz_store_id(storeId);
            List<OpenTableMustOrder> origins = openTableMustOrderV2Service.listByStore(mustOrderReq);
            if(CollectionUtils.isEmpty(origins)) {
                return Lists.newArrayList();
            }

            return origins.stream()
                    .map(p -> OpenTableMustOrderProductVO.builder()
                            .storeId(storeId)
                            .spuId(p.getSpuId())
                            .skuId(p.getSkuId())
                            .chargeMethod(ChargeMethodEnum.trans(p.getChargeMethod()))
                            .mustOrderNum(p.getMustOrderNum())
                            .sort(p.getSort())
                            .build())
                    .collect(Collectors.toList());
        });

    }

}
