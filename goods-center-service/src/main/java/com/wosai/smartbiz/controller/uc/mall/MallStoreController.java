package com.wosai.smartbiz.controller.uc.mall;

import com.wosai.market.mcc.api.dto.response.ConfigResponse;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.controller.uc.BaseController;
import com.wosai.smartbiz.dto.req.ConfigRemoteReq;
import com.wosai.smartbiz.enums.config.CommonConfigNameEnum;
import com.wosai.smartbiz.gds.enums.ErrorCodeEnum;
import com.wosai.smartbiz.gds.service.CommonConfigService;
import com.wosai.smartbiz.strategy.config.OrderConfigStrategy;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: KangYiJun
 * @date: 2019-12-23
 */
@RestController
@RequestMapping("/mall/store")
public class MallStoreController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(MallStoreController.class);

    @Autowired
    private CommonConfigService commonConfigService;

    /**
     * 门店查询点餐配置
     * @param configRemoteReq
     * @return
     */
    @PostMapping("/config/store/order/list")
    public Result<Map<String,String>> getOrderConfig(ConfigRemoteReq configRemoteReq){
        if(StringUtils.isEmpty(configRemoteReq.getOwnerId())){
            return Result.error(ErrorCodeEnum.BUSINESS_ERROR);
        }

        List<CommonConfigNameEnum> nameList = new OrderConfigStrategy().getConfigNameList();


        Result<List<ConfigResponse>> result = commonConfigService.getConfigList(configRemoteReq.getOwnerId(), nameList);
        if(!result.isSuccess()){
            return Result.error(result.getResult());
        }

        List<ConfigResponse> configResponses = result.getData();

        Map<String,String> map = configResponses.stream().
                collect(Collectors.toMap(ConfigResponse::getName, ConfigResponse::getValue));

        for(CommonConfigNameEnum nameEnum : nameList){
            if(!map.containsKey(nameEnum.getCode())){
                map.put(nameEnum.getCode(), nameEnum.getCommonConfigStrategy().getDefaultValue());
            }
        }

        return Result.success(map);
    }
}
