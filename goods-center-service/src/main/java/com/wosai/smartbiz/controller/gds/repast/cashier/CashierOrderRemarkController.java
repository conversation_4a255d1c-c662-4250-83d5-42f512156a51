package com.wosai.smartbiz.controller.gds.repast.cashier;

import com.google.common.collect.Lists;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.controller.gds.BaseController;
import com.wosai.smartbiz.dto.req.remark.RemarkRpcQueryReq;
import com.wosai.smartbiz.dto.vo.remark.CashierOrderRemarkVO;
import com.wosai.smartbiz.dto.vo.remark.RemarkVO;
import com.wosai.smartbiz.goods.enums.repast.AttributeTypeEnum;
import com.wosai.smartbiz.gds.jsonrpc.RemarkRpcService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: KangYiJun
 * @date: 2020-09-11
 */
@RestController
@RequestMapping(value = "/cashier/v1/order/remark")
public class CashierOrderRemarkController extends BaseController {

    @Autowired
    private RemarkRpcService remarkRpcService;

    /**
     * 备注列表
     * @param merchantId
     * @param storeId
     * @return
     */
    @PostMapping(value = "/list")
    public Result<List<CashierOrderRemarkVO>> list(@RequestParam(value = "merchantId") String merchantId,
                                                   @RequestParam(value = "storeId") String storeId,
                                                   @RequestParam(value = "type", required = false) String type,
                                                   HttpServletRequest request)
    {
        return tryCatch(() -> {
            RemarkRpcQueryReq queryReq = new RemarkRpcQueryReq();
            queryReq.setMerchant_id(merchantId);
            queryReq.setBiz_store_id(storeId);
            if(StringUtils.isNotBlank(type)){
                queryReq.setType(type.toLowerCase());
            }
            List<RemarkVO> remarkVOList = remarkRpcService.listByQuery(queryReq);
            if(CollectionUtils.isEmpty(remarkVOList)){
                return Lists.newArrayList();
            }

            return remarkVOList.stream()
                    .map(p -> new CashierOrderRemarkVO()
                            .setType(EnumUtils.getEnum(AttributeTypeEnum.class, p.getType().toUpperCase()))
                            .setName(p.getName())
                            .setList(p.getTextList()))
                    .collect(Collectors.toList());
        });

    }

}
