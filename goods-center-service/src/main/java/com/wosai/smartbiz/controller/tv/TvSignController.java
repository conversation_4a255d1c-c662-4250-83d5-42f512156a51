package com.wosai.smartbiz.controller.tv;

import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.base.utils.RsaUtil;
import com.wosai.smartbiz.gds.dto.tv.TvBaseDTO;
import com.wosai.smartbiz.gds.enums.ErrorCodeEnum;
import com.wosai.smartbiz.gds.service.StoreTvRpcService;
import com.wosai.smartbiz.gds.vo.StoreTvDeviceVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

/**
 * @author: redhat
 * @date: 2022-01-19
 */
@Slf4j
@RestController
@RequestMapping("/tv/sign")
public class TvSignController {

    @Autowired
    private StoreTvRpcService storeTvRpcService;

    /**
     * 获取门店叫号配置信息
     * @param body
     * @return
     */
    @PostMapping("/verify")
    public Result<Boolean> verify(TvBaseDTO body) {
        try {
            //验证公共参数
            checkPublicParam(body);

            //验证业务参数
            String storeId = body.getStoreId();
            String sn = body.getSn();
            if(StringUtils.isBlank(sn)){
                return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), "设备SN不能为空");
            }
            if(StringUtils.isBlank(storeId)){
                return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), "门店ID不能为空");
            }
            String sign = body.getSign();
            if (StringUtils.isBlank(sign)) {
                return Result.error(ErrorCodeEnum.TV_SIGN_IS_BLANK_ERROR);
            }
            StoreTvDeviceVO storeTvDeviceVO = storeTvRpcService.tvSignVerifyDeviceDetail(sn);
            if(null == storeTvDeviceVO || !Objects.equals(storeTvDeviceVO.getStoreId(), storeId)){
                return Result.error(ErrorCodeEnum.TV_DEVICE_STORE_NOT_MATCH);
            }
            if(StringUtils.isBlank(storeTvDeviceVO.getPublicKey())){
                return Result.error(ErrorCodeEnum.TV_DEVICE_PUBLIC_KEY_IS_NULL);
            }
            String publicKey = storeTvDeviceVO.getPublicKey();
            Map<String, String> params = getStringParameters(body);
            String signContent = RsaUtil.getSignContent(params);

            boolean verifyResult = RsaUtil.rsaCheck(signContent, sign, publicKey, "utf-8", "RSA2");
            if(!verifyResult){
                return Result.error(ErrorCodeEnum.TV_DEVICE_VERIFY_ERROR);
            }
        } catch (ParamException ex) {
            log.error("verify param error", ex);
            return Result.error(ErrorCodeEnum.PARAMETER_ERROR.getErrCode(), ex.getMessage());
        } catch (Exception ex){
            log.error("verify error", ex);
            return Result.error(ErrorCodeEnum.TV_DEVICE_VERIFY_ERROR);
        }
        return Result.success(true);
    }


    private Map<String, String> getStringParameters(TvBaseDTO body) throws ParamException {
        Map<String, String> params = new HashMap<>();
        Set<String> keySet = getFiledNameSet(body);

        for (String key : keySet) {
            if (Objects.equals(key, "key")) {
                throw new ParamException("Key为敏感字段，请删除后重试");
            }

            Object value = getFieldValueByName(key, body);
            if (value instanceof FileItem) {
                continue;
            }
            if (Objects.equals(key, "sign")) {
                continue;
            } else if (value != null) {
                params.put(key, String.valueOf(value));
            }
        }
        return params;
    }

    private Set<String> getFiledNameSet(Object o) {
        Field[] fields = o.getClass().getDeclaredFields();
        Set<String> fieldNameSet = new HashSet<>(fields.length);
        for (int i = 0; i < fields.length; i++) {
            fieldNameSet.add(fields[i].getName());
        }
        return fieldNameSet;
    }

    private Object getFieldValueByName(String fieldName, Object o) {
        try {
            String firstLetter = fieldName.substring(0, 1).toUpperCase();
            String getter = "get" + firstLetter + fieldName.substring(1);
            Method method = o.getClass().getMethod(getter, new Class[] {});
            Object value = method.invoke(o, new Object[] {});
            return value;
        } catch (Exception ex) {
            log.error("获取属性值失败, fieldName:{}", fieldName, ex);
        }
        return null;
    }


    private boolean checkPublicParam(TvBaseDTO body) throws ParamException {
        if(StringUtils.isBlank(body.getRequestId())){
            throw new ParamException("参数异常");
        }
        if(null == body.getRequestTime()){
            throw new ParamException("参数异常");
        }
        return true;
    }
}
