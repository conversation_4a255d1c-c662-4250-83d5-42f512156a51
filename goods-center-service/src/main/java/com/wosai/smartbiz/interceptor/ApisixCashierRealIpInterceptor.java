package com.wosai.smartbiz.interceptor;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.googlecode.jsonrpc4j.JsonRpcInterceptor;
import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.base.apisix.cashier.CashierClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.ObjectUtils;
import org.springframework.util.PathMatcher;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 收银机apisix请求真实IP地址获取拦截器
 */
@Service
@Slf4j
public class ApisixCashierRealIpInterceptor implements JsonRpcInterceptor {

    private PathMatcher pathMatcher = new AntPathMatcher();

    private final List<String> excludePatterns = new ArrayList();

    private final List<String> includePatterns = Lists.newArrayList("rpc/retail/cashier/login/choiceStore", "apisix/cashier/login/choiceStore");


    public boolean matches(String lookupPath) {
        PathMatcher pathMatcherToUse = pathMatcher;
        if (!ObjectUtils.isEmpty(this.excludePatterns)) {
            for (String pattern : this.excludePatterns) {
                if (pathMatcherToUse.match(pattern, lookupPath)) {
                    return false;
                }
            }
        }
        if (ObjectUtils.isEmpty(this.includePatterns)) {
            return true;
        }
        for (String pattern : this.includePatterns) {
            if (pathMatcherToUse.match(pattern, lookupPath)) {
                return true;
            }
        }
        return false;
    }


    public ApisixCashierRealIpInterceptor addPathPatterns(String... patterns) {
        this.includePatterns.addAll(Arrays.asList(patterns));
        return this;
    }

    public ApisixCashierRealIpInterceptor excludePathPatterns(String... patterns) {
        this.excludePatterns.addAll(Arrays.asList(patterns));
        return this;
    }

    public boolean matches(Method method){
        String uri = method.getDeclaringClass().getAnnotation(JsonRpcService.class).value() + "/" + method.getName();

        return matches(uri);
    }

    @Override
    public void preHandleJson(JsonNode json) {

    }

    @Override
    public void preHandle(Object target, Method method, List<JsonNode> params) {
    }

    @Override
    public void postHandle(Object target, Method method, List<JsonNode> params, JsonNode result) {

    }

    @Override
    public void postHandleJson(JsonNode json) {

    }

    @Override
    public void preCallMethod(Object target, Method method, Object... argument) {
        if (!matches(method)){
            return;
        }
        try {
            ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            String remoteIp = requestAttributes.getRequest().getHeader("remoteip");
            log.info("ApisixCashierRealIpInterceptor get real remoteIp:{}", remoteIp);
            for (Object arg:argument){
                if (arg instanceof CashierApiRequest){
                    CashierApiRequest request = (CashierApiRequest) arg;
                    CashierClient client = request.getClient();
                    if(null != client && StringUtils.isNotBlank(remoteIp)){
                        client.setIp(remoteIp);
                    }
                }
            }
        } catch (Exception ex){
            log.error("ApisixCashierRealIpInterceptor error", ex);
        }
    }

    @Override
    public void afterCallMethod(Object target, Method method, Object result, Object... argument) {

    }
}
