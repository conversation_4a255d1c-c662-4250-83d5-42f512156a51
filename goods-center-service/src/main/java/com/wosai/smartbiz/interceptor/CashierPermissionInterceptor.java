package com.wosai.smartbiz.interceptor;

import com.fasterxml.jackson.databind.JsonNode;
import com.googlecode.jsonrpc4j.JsonRpcInterceptor;
import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.smartbiz.base.apisix.cashier.CashierApiRequest;
import com.wosai.smartbiz.base.apisix.cashier.CashierClient;
import com.wosai.smartbiz.base.apisix.cashier.CashierUser;
import com.wosai.smartbiz.base.exceptions.BusinessException;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.gds.service.PermissionCheckService;
import com.wosai.smartbiz.goods.util.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.ObjectUtils;
import org.springframework.util.PathMatcher;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @author: redhat
 */
@Service
@Slf4j
public class CashierPermissionInterceptor implements JsonRpcInterceptor {

    private PathMatcher pathMatcher = new AntPathMatcher();

    private final List<String> excludePatterns = new ArrayList();

    private final List<String> includePatterns = new ArrayList<String>();

    @Autowired
    private PermissionCheckService permissionCheckService;

    public boolean matches(String lookupPath) {
        PathMatcher pathMatcherToUse = pathMatcher;
        if (!ObjectUtils.isEmpty(this.excludePatterns)) {
            for (String pattern : this.excludePatterns) {
                if (pathMatcherToUse.match(pattern, lookupPath)) {
                    return false;
                }
            }
        }
        if (ObjectUtils.isEmpty(this.includePatterns)) {
            return true;
        }
        for (String pattern : this.includePatterns) {
            if (pathMatcherToUse.match(pattern, lookupPath)) {
                return true;
            }
        }
        return false;
    }


    public CashierPermissionInterceptor addPathPatterns(String... patterns) {
        this.includePatterns.addAll(Arrays.asList(patterns));
        return this;
    }

    public CashierPermissionInterceptor excludePathPatterns(String... patterns) {
        this.excludePatterns.addAll(Arrays.asList(patterns));
        return this;
    }

    public boolean matches(Method method){
        String uri = method.getDeclaringClass().getAnnotation(JsonRpcService.class).value() + "/" + method.getName();

        return matches(uri);
    }

    @Override
    public void preHandleJson(JsonNode json) {
        if(null == permissionCheckService){
            permissionCheckService = SpringContextUtil.getBean(PermissionCheckService.class);
        }
    }

    @Override
    public void preHandle(Object target, Method method, List<JsonNode> params) {
    }

    @Override
    public void postHandle(Object target, Method method, List<JsonNode> params, JsonNode result) {

    }

    @Override
    public void postHandleJson(JsonNode json) {

    }

    @Override
    public void preCallMethod(Object target, Method method, Object... argument) {
        if (!matches(method)){
            return;
        }
        for (Object arg:argument){
            if (arg instanceof CashierApiRequest){
                CashierApiRequest request = (CashierApiRequest) arg;
                CashierUser user = request.getUser();
                CashierClient client = request.getClient();

                if(null != user && null != client && StringUtils.isNotBlank(client.getPermissionToken()) && StringUtils.isNotBlank(client.getPermissionCode())) {
                    Result<Boolean> result = permissionCheckService.check(client.getPermissionToken(), client.getPermissionCode(), user.getMerchantId());
                    if (!result.isSuccess()) {
                        throw new BusinessException(result.getErrorMsg());
                    }
                }
            }
        }
    }

    @Override
    public void afterCallMethod(Object target, Method method, Object result, Object... argument) {

    }
}
