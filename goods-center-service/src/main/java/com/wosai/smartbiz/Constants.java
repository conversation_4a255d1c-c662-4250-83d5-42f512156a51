package com.wosai.smartbiz;

import com.google.common.collect.Lists;

import java.util.List;

public class Constants {
    public static class MccNames{
        public static final String ORDER_REQUIRE_PHONE = "order_require_phone";
    }
    public static class AppMarkConstants{
        public static final String CASHIER = "cashier";
        public static final String RDS = "tv";
        public static final String RETAIL = "retail";
    }

    /**
     * 收银机配置名称常量
     */
    public static class CashierConfigNameConstants {
        public static final String IS_CASHIER_TAKEOUT_ORDER_KEY = "cashier_takeout_order";
        public static final String IS_SMART_TAKEOUT_KEY = "smart_takeout";
        public static final String IS_TRIPARTITE_TAKEOUT_KEY = "tripartite_takeout";
        public static final String IS_RETAIL_TAKEOUT_KEY = "retail_takeout";
        public static final String IS_MEITUAN_STORE_COUPON_AUTHORIZED_KEY = "is_meituan_store_coupon_authorized";
        public static final String IS_DOUYIN_STORE_COUPON_AUTHORIZED_KEY = "is_douyin_store_coupon_authorized";
        public static final String IS_INVOICE_STORE = "is_invoice_store";
        public static final String IS_NEED_REDEEM_VERIFY = "is_need_redeem_verify";
        public static final String ENABLE_USE_BALANCE_PAYMENT = "enable_use_balance_payment";
    }

    /**
     * RMQX系统配置信息
     */
    public static class RmqxConstants{
        public final static String MQTT_TOPIC_SCENE_RETAIL = "retail";
        public final static String MQTT_STORE_PREFIX = "store";
        public final static String MQTT_DEVICE_PREFIX = "device";

        /**
         * 说明: 处理连接报文
         * 执行时机: 服务端收到客户端的连接报文时
         */
        public final static String MQTT_WEB_HOOK_ACTION_CLIENT_CONNECT = "client_connect";
        /**
         * 说明: 下发连接应答
         * 执行时机: 服务端准备下发连接应答报文时
         */
        public final static String MQTT_WEB_HOOK_ACTION_CLIENT_CONNACK = "client_connack";
        /**
         * 说明: 成功接入
         * 执行时机: 客户端认证完成并成功接入系统后
         */
        public final static String MQTT_WEB_HOOK_ACTION_CLIENT_CONNECTED = "client_connected";
        /**
         * 说明: 连接断开
         * 执行时机: 客户端连接层在准备关闭时
         */
        public final static String MQTT_WEB_HOOK_ACTION_CLIENT_DISCONNECTED = "client_disconnected";
        /**
         * 说明: 订阅主题
         * 执行时机: 收到订阅报文后，执行 client.check_acl 鉴权前
         */
        public final static String MQTT_WEB_HOOK_ACTION_CLIENT_SUBSCRIBE = "client_subscribe";
        /**
         * 说明: 取消订阅
         * 执行时机: 收到取消订阅报文后
         */
        public final static String MQTT_WEB_HOOK_ACTION_CLIENT_UNSUBSCRIBE = "client_unsubscribe";

        /**
         * 会话订阅主题
         */
        public final static String MQTT_WEB_HOOK_ACTION_SESSION_SUBSCRIBED = "session_subscribed";
        /**
         * 会话取消订阅
         */
        public final static String MQTT_WEB_HOOK_ACTION_SESSION_UNSUBSCRIBED = "session_unsubscribed";
        /**
         * 说明: 消息发布
         * 执行时机: 服务端在发布（路由）消息前
         */
        public final static String MQTT_WEB_HOOK_ACTION_MESSAGE_PUBLISH = "message_publish";
        /**
         * 说明: 消息投递
         * 执行时机: 消息准备投递到客户端前
         */
        public final static String MQTT_WEB_HOOK_ACTION_MESSAGE_DELIVERED = "message_deliver";
        /**
         * 说明: 消息回执
         * 执行时机: 服务端在收到客户端发回的消息 ACK 后
         */
        public final static String MQTT_WEB_HOOK_ACTION_MESSAGE_ACKED = "message_acked";
        /**
         * 说明: 消息丢弃
         * 执行时机: 发布出的消息被丢弃后
         */
        public final static String MQTT_WEB_HOOK_ACTION_MESSAGE_DROPPED = "message_dropped";
    }

    /**
     * 副屏配置常量
     */
    public static class SecondaryScreenConstants {
        /**
         * 默认轮播时长
         */
        public final static int DEFAULT_ROTATION_DURATION = 5;
    }

    /**
     * Crm常量
     */
    public static class CrmConstants {
        /**
         * 组织机构4级
         */
        public final static int ORGANIZATION_LEVEL_4 = 4;
        /**
         * 服务商组织编码开头
         */
        public final static String AGENT_ORGANIZATION_CODE_START = "00001";

        /**
         * 直营组织编码开头
         */
        public final static String DIRECT_ORGANIZATION_CODE_START = "00003";
        /**
         * 线上业务组织编码开头
         */
        public final static String ONLINE_BUSINESS_ORGANIZATION_CODE_START = "02754";
        /**
         * 电饱饱直营组织编码开头
         */
        public final static String DBB_DIRECT_ORGANIZATION_CODE_START = "70002";
        /**
         * 大客户组织编码开头
         */
        public final static String BIG_CUSTOM_ORGANIZATION_CODE_START = "00069";

        /**
         * 直营组织编码列表
         */
        public final static List<String> DIRECT_ORGANIZATION_CODE_LIST = Lists.newArrayList(DIRECT_ORGANIZATION_CODE_START, ONLINE_BUSINESS_ORGANIZATION_CODE_START, DBB_DIRECT_ORGANIZATION_CODE_START, BIG_CUSTOM_ORGANIZATION_CODE_START);

        /**
         * 特殊用户职位名称
         */
        public final static String SPECIAL_USER_POSITION_NAME = "一级服务商";

        public final static String STORE_ACTIVE_TYPE_JUMP = "JUMP";
        public final static String STORE_ACTIVE_TYPE_JUMP_TITLE = "立即激活";
        public final static String STORE_ACTIVE_TYPE_DISPLAY = "DISPLAY";

        public final static String STORE_EXTERNAL_PRODUCT_BIND_TYPE_JUMP = "JUMP";
        public final static String STORE_EXTERNAL_PRODUCT_BIND_TYPE_JUMP_TITLE = "立即绑定";

    }


    /**
     * DataEvents常量
     */
    public static class DataEventsConstants {
        /**
         * 收银机激活来源-收银机激活
         */
        public final static String CASHIER_ACTIVE_SOURCE_CASHIER = "cashier";

        /**
         * 收银机激活来源-CRM
         */
        public final static String CASHIER_ACTIVE_SOURCE_CRM = "crm";

        /**
         * 收银机激活事件类型
         */
        public final static String CASHIER_ACTIVE_EVENT_TYPE = "SmCashActive";

    }

    /**
     * topic
     */
    public static class RedisConstants {

        /**
         * topic相关常量
         */
        public static final String MESSAGE_ACKED_COUNT_KEY = "message:acked:count:map";

        public static final String MESSAGE_PUBLISH_COUNT_KEY = "message:publish:count:map";

        public static final String MESSAGE_SUBSCRIBE_COUNT_KEY = "message:subscribe:count:map";

        public static final String TOPIC_UNSUBSCRIBE_COUNT_KEY= "topic:unsubscribe:count:map";

        public static final String TOPIC_UNSUBSCRIBE_CLIENT_KEY= "topic:unsubscribe:client:map";

    }

    /**
     * 外部产品常量
     */
    public static class ExternalProductConstants {
        public static final Byte QR_STATUS_EXPIRE = new Byte("5");
    }

    /**
     * 响应常量
     */
    public static class ExceptionConstants {
        public static final String SUCC = "10000";

        public static final String NOT_EXISTS = "10091";

        public static final String NOT_MATCH = "10092";
    }

    /**
     * 收银机类型常量
     */
    public static class CashierTypeConstants {

        public static final String RETAIL = "37";

        public static final String CASHIER = "36";

        public static final String TV = "38";
    }

    /**
     * 日志变量常量类
     */
    public static class LogConstants {
        public static final String SN = "sn";

        public static final String CLASS = "class";

        public static final String METHOD = "method";

        public static final String MESSAGE = "message";

        public static final String ARGUMENTS = "arguments";

        public static final String HEADERS = "headers";

        public static final String RESULT = "result";

        public static final String DURATION = "duration";

        public static final String STORE_ID = "storeId";

        public static final String MERCHANT_ID = "merchantId";

        public static final String STACK_TRACE = "stack_trace";

        public static final String URI = "uri";

        public static final String ERROR = "error";
    }

    /**
     * 请求参数变量
     */
    public static class RequestParamConstants {
        public static final String BIZ_STORE_ID = "biz_store_id";

        public static final String STORE_ID = "storeId";

        public static final String MERCHANT_ID = "merchantId";
    }

}
