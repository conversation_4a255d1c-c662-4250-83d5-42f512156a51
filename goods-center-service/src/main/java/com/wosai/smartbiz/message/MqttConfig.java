package com.wosai.smartbiz.message;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * @author: redhat
 */
@Component
@Configuration
@Data
public class MqttConfig implements Serializable {
    @Value("${rmqx.host}")
    private String host;

    @Value("${rmqx.server-client-id-prefix}")
    private String serverClientIdPrefix;

    @Value("${rmqx.tv-device-client-id-prefix}")
    private String tvClientIdPrefix;

    @Value("${rmqx.cashier-device-client-id-prefix}")
    private String cashierClientIdPrefix;

    @Value("${rmqx.user}")
    private String user;

    @Value("${rmqx.pwd}")
    private String pwd;

    @Value("${rmqx.superuser}")
    private Boolean superuser;

    @Value("${rmqx.qos}")
    private int qos;

    @Value("${rmqx.will-data}")
    private String willData;
}
