package com.wosai.smartbiz.message;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * @author: redhat
 */
@Slf4j
@Component
public class MqttPushClient {
    private static final boolean MQTT_AUTOMATIC_RECONNECT = true;
    private static final boolean MQTT_CLEAN_SESSION = true;
    private static final int MQTT_CONNECTION_TIMOUT = 10;
    private static final int MQTT_KEEP_ALIVE_INTERVAL = 60;
    private static final int MQTT_MAX_INFLIGHT = 20;

    @Autowired
    private MqttConfig mqttConfig;

    @Autowired
    private MqttPushCallback mqttPushCallback;

    private MqttClient client;

    private List<MqttSubTopic> defaultTopics;

    @PostConstruct
    public void init() {
        synchronized (MqttPushClient.class) {
            if (null == client) {
                connect();
            }
        }
    }

    /**
     * 创建client并且连接client
     */
    private void connect() {
        MqttConnectOptions options = null;
        try {
            String mqttServerClientId = mqttConfig.getServerClientIdPrefix() + UUID.randomUUID().toString();
            client = new MqttClient(mqttConfig.getHost(), mqttServerClientId, new MemoryPersistence());
            client.setCallback(mqttPushCallback);
            options = getMqttConnectOptions();
            client.connect(options);
            //subscribeDefaultTopic();
            log.info("MqttPushClient.connect.success,clientId:{},options:{}", client.getClientId(), JSON.toJSONString(options));
        } catch (MqttException ex) {
            log.error("MqttPushClient.connect.error,clientId:{},options:{}", client.getClientId(), JSON.toJSONString(options), ex);
        }
    }

    /**
     * 检查client连接状态
     *
     * @return
     */
    private boolean checkConnected() {
        if (null == client) {
            init();
            return true;
        } else {
            if (client.isConnected()) {
                return true;
            }
            try {
                client.reconnect();
                //subscribeDefaultTopic();
                log.info("MqttPushClient.checkConnected.reconnect.success,clientId:{}", client.getClientId());
                return true;
            } catch (MqttException ex) {
                log.error("MqttPushClient.checkConnected.reconnect.error,clientId:{}", client.getClientId(), ex);
                return false;
            }
        }
    }

    /**
     * 获取client连接所需要的参数
     *
     * @return
     */
    private MqttConnectOptions getMqttConnectOptions() {
        MqttConnectOptions options = new MqttConnectOptions();
        // 设置是否清空session,这里如果设置为false表示服务器会保留客户端的连接记录，
        // 这里设置为true表示每次连接到服务器都以新的身份连接
        options.setCleanSession(MQTT_CLEAN_SESSION);
        // 设置连接的用户名
        options.setUserName(mqttConfig.getUser());
        // 设置连接的密码
        options.setPassword(mqttConfig.getPwd().toCharArray());
        options.setServerURIs(StringUtils.split(mqttConfig.getHost(), ","));
        // 设置超时时间 单位为秒
        options.setConnectionTimeout(MQTT_CONNECTION_TIMOUT);
        // 设置会话心跳时间 单位为秒 服务器会每隔1.5*20秒的时间向客户端发送心跳判断客户端是否在线，但这个方法并没有重连的机制
        options.setKeepAliveInterval(MQTT_KEEP_ALIVE_INTERVAL);
        options.setAutomaticReconnect(MQTT_AUTOMATIC_RECONNECT);
        // 设置最多挂起的消息条数(系统默认10)
        options.setMaxInflight(MQTT_MAX_INFLIGHT);
        // 设置“遗嘱”消息的话题，若客户端与服务器之间的连接意外中断，服务器将发布客户端的“遗嘱”消息。
        options.setWill("willTopic", mqttConfig.getWillData().getBytes(), mqttConfig.getQos(), false);
        return options;
    }

    /**
     * 断开连接
     */
    public void disconnect() {
        try {
            if (client.isConnected()) {
                log.info("MqttPushClient.disconnect,clientId:{}", client.getClientId());
                client.disconnect();
            }
        } catch (Exception ex) {
            log.error("MqttPushClient.disconnect.error,clientId:{}", client.getClientId(), ex);
        }
    }

    /**
     * 重新连接
     */
    public void reconnect() {
        try {
            if (null == client) {
                return;
            }
            if (!client.isConnected()) {
                log.info("MqttPushClient.reconnect,clientId:{}", client.getClientId());
                client.reconnect();
            }
        } catch (Exception ex) {
            log.error("MqttPushClient.reconnect.error,clientId:{}", client.getClientId(), ex);
        }
    }

    /**
     * 发布消息
     *
     * @param topic
     * @param pushMessage
     */
    public void publish(String topic, String pushMessage) {
        publish(false, topic, pushMessage);
    }

    /**
     * 发布消息
     *
     * @param retained
     * @param topic
     * @param pushMessage
     */
    public void publish(boolean retained, String topic, String pushMessage) {
        if (!checkConnected()) {
            log.error("MqttPushClient.publish.checkConnected is false,clientId:{},topic:{},pushMessage:{}", client.getClientId(), topic, pushMessage);
            return;
        }
        MqttMessage message = new MqttMessage();
        message.setQos(mqttConfig.getQos());
        message.setRetained(retained);
        message.setPayload(pushMessage.getBytes());
        MqttTopic mqttTopic = client.getTopic(topic);
        if (null == mqttTopic) {
            log.error("MqttPushClient.publish.topic.not.exist,clientId:{},topic:{}", client.getClientId(), topic);
        }
        MqttDeliveryToken token;
        try {
            log.info("MqttPushClient.publish,clientId:{},topic:{},retained:{},pushMessage:{}", client.getClientId(), topic, retained, pushMessage);
            token = mqttTopic.publish(message);
            token.waitForCompletion();
        } catch (MqttPersistenceException ex) {
            log.error("MqttPushClient.publish.error,clientId:{},topic:{},message:{}", client.getClientId(), topic, JSON.toJSONString(message), ex);
        } catch (MqttException ex) {
            log.error("MqttPushClient.publish.error,clientId:{},topic:{},message:{}", client.getClientId(), topic, JSON.toJSONString(message), ex);
        }
    }

    /**
     * 订阅某个主题，qos默认为1
     *
     * @param topic
     */
    public void subscribe(String topic) {
        subscribe(topic, mqttConfig.getQos());
    }

    /**
     * 订阅某个主题
     *
     * @param topic
     * @param qos
     */
    public void subscribe(String topic, int qos) {
        try {
            client.subscribe(topic, qos);
        } catch (MqttException ex) {
            log.error("MqttPushClient.subscribe.error,clientId:{},topic:{}", client.getClientId(), topic, ex);
        }
    }

    /**
     * 订阅默认主题
     *
     * @throws MqttException
     */
    public void subscribeDefaultTopic(){
        try {
            if (CollectionUtils.isEmpty(defaultTopics)) {
                defaultTopics = new ArrayList<>();
                MqttSubTopic subTopic = new MqttSubTopic();
                subTopic.setTopic("/abc/def/12345678");
                subTopic.setQos(2);
                defaultTopics.add(subTopic);
            }
            for (MqttSubTopic subTopic : defaultTopics) {
                this.client.subscribe(subTopic.getTopic(), subTopic.getQos());
            }
        } catch (Exception ex){
            log.error("MqttPushClient.subscribeDefaultTopic.error", ex);
        }

    }


}