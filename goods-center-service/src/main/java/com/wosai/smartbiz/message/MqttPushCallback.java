package com.wosai.smartbiz.message;

import com.alibaba.fastjson.JSON;
import com.wosai.smartbiz.gds.service.CashierMessageService;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallbackExtended;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * 发布消息的回调类
 * <p>
 * 必须实现MqttCallback的接口并实现对应的相关接口方法CallBack 类将实现 MqttCallBack。
 * 每个客户机标识都需要一个回调实例。在此示例中，构造函数传递客户机标识以另存为实例数据。
 * 在回调中，将它用来标识已经启动了该回调的哪个实例。
 * 必须在回调类中实现三个方法：
 * public void messageArrived(MqttTopic topic, MqttMessage message)接收已经预订的发布。
 * public void connectionLost(Throwable cause)在断开连接时调用。
 * public void deliveryComplete(MqttDeliveryToken token))
 * 接收到已经发布的 QoS 1 或 QoS 2 消息的传递令牌时调用。
 * 由 MqttClient.connect 激活此回调。
 * </p>
 */
@Slf4j
@Component
public class MqttPushCallback implements MqttCallbackExtended {

    @Autowired
    private MqttPushClient mqttPushClient;
    @Lazy
    private CashierMessageService cashierMessageService;

    @Override
    public void connectionLost(Throwable cause) {
        //连接丢失后，一般在这里面进行重连
        log.info("MqttPushCallback--连接断开" + JSON.toJSONString(cause));
        mqttPushClient.reconnect();
    }

    @Override
    public void deliveryComplete(IMqttDeliveryToken token) {
        try {
            if (!token.isComplete()) {
                log.info("MqttPushCallback.deliveryComplete异常：" + JSON.toJSONString(token));
            }
        } catch (Exception ex) {
            log.error("MqttPushCallback.deliveryComplete error", ex);
        }

    }

    @Override
    public void messageArrived(String topic, MqttMessage message) {
        try {
            log.info("MqttPushCallback.messageArrived topic：{}, qos：{}, payload：{}", topic, message.getQos(), new String(message.getPayload()));

            //Map<String, String> jsonMap = JSON.parseObject(new String(message.getPayload()), new TypeReference<HashMap<String, String>>() {});
            //MapUtils.getString(jsonMap,"topic")
            //cashierMessageService.sendDeviceCashierMessage();
        } catch (Exception e) {
            log.error("MqttPushCallback.messageArrived, topic：{}, message：{}", topic, JSON.toJSONString(message));
        }
    }

    @Override
    public void connectComplete(boolean b, String s) {
        log.info("MqttPushCallback.connectComplete" + s);
        //mqttPushClient.subscribeDefaultTopic();
    }
}
