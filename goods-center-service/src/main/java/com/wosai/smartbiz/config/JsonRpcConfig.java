package com.wosai.smartbiz.config;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean;
import com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBeanTracerV153;
import com.shouqianba.external.platform.adapter.ExternalCouponV2Adapter;
import com.shouqianba.licence.manager.api.interfaces.LicenceRemoteService;
import com.shouqianba.licence.manager.api.interfaces.MigrationRemoteService;
import com.shouqianba.saas.customer.service.MemberAvailableStoreService;
import com.shouqianba.saas.customer.service.MemberMessageMerchantAccountService;
import com.shouqianba.saas.customer.service.MemberMessageSendRecordService;
import com.shouqianba.saas.customer.service.MemberMsgTaskService;
import com.shouqianba.workflow.service.AuditService;
import com.wosai.aop.gateway.service.ClientSideNoticeService;
import com.wosai.app.backend.api.service.AppUserService;
import com.wosai.app.backend.api.service.IAccountService;
import com.wosai.app.backend.api.service.IAppGatedRuleService;
import com.wosai.app.backend.api.service.LoginDeviceService;
import com.wosai.app.push.api.service.IPushMqttService;
import com.wosai.app.service.AccountService;
import com.wosai.app.service.MchUserLoginService;
import com.wosai.app.service.MerchantUserRoleService;
import com.wosai.app.service.MerchantUserService;
import com.wosai.app.service.app.AppBackendUserService;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.app.service.v2.UcUserAccountService;
import com.wosai.cua.mapping.api.CashRegisterService;
import com.wosai.data.crow.api.service.OnlineQueryService;
import com.wosai.data.jackson.RowDeserializerInstantiator;
import com.wosai.invoice.app.backend.api.service.InstantStoreService;
import com.wosai.iot.api.service.DeviceQrcodeService;
import com.wosai.it.oms.service.OrderService;
import com.wosai.it.oms.service.SkuService;
import com.wosai.it.renewal.center.service.QuotaService;
import com.wosai.marekting.prepaid.service.StoredOwnerService;
import com.wosai.market.awesome.message.service.LicenceMessageRpcService;
import com.wosai.market.awesome.message.service.PollingMessageRpcService;
import com.wosai.market.awesome.message.service.RmqxConfigRpcService;
import com.wosai.market.awesome.message.service.StoreMessageRpcService;
import com.wosai.market.boss.circle.user.api.UserFollowAndFansService;
import com.wosai.market.data.events.api.DataEventsService;
import com.wosai.market.mcc.api.service.ConfigRemoteService;
import com.wosai.market.mcc.api.service.DefaultConfigRemoteService;
import com.wosai.market.mcc.api.service.UidRemoteService;
import com.wosai.market.mcc.api.service.WhiteListV2RemoteService;
import com.wosai.market.merchant.api.*;
import com.wosai.market.outer.adapter.api.service.AlipayAiRemoteService;
import com.wosai.market.service.BatchCreateDataService;
import com.wosai.market.service.BatchSyncDataService;
import com.wosai.market.service.ProductMessageRpcService;
import com.wosai.market.service.ProductStockService;
import com.wosai.market.service.merchant.*;
import com.wosai.market.service.store.*;
import com.wosai.market.tethys.api.service.ActivityRemoteService;
import com.wosai.market.tethys.api.service.SecondActivityRemoteService;
import com.wosai.market.tethys.api.service.SingleActivityRemoteService;
import com.wosai.marketing.merchant.service.MerchantMemberOpenService;
import com.wosai.mc.service.MerchantService;
import com.wosai.mc.service.StoreService;
import com.wosai.pantheon.core.uitem.service.ItemService;
import com.wosai.print.service.AuthorizeService;
import com.wosai.sales.core.service.*;
import com.wosai.sales.profit.service.ProfitDirectSignService;
import com.wosai.sales.terminal.order.service.IOrderItemService;
import com.wosai.sales.terminal.order.service.IOrderService;
import com.wosai.sales.terminal.order.service.crm.OuterOrderEdgeService;
import com.wosai.smart.goods.product.IProductRpcService;
import com.wosai.smart.goods.product.IWaterlineRpcService;
import com.wosai.smart.goods.rpc.CategoryService;
import com.wosai.smart.goods.rpc.TemplateUseRpcService;
import com.wosai.smart.goods.search.IProductSearchRpcService;
import com.wosai.smart.goods.stock.IStockRpcService;
import com.wosai.smart.goods.task.IProductTaskRpcService;
import com.wosai.smart.goods.tool.IProductTagRpcService;
import com.wosai.smart.goods.unit.IUnitRpcService;
import com.wosai.smart.standard.api.category.ICategoryRpcService;
import com.wosai.smart.standard.api.goods.IRetailGoodsRpcService;
import com.wosai.smartbiz.oms.api.services.CashierPaymentTerminalService;
import com.wosai.smartbiz.oms.api.services.OrderCallRpcService;
import com.wosai.smartbiz.oms.api.services.TableRpcServiceV2;
import com.wosai.sp.business.logstash.service.BusinessOpLogService;
import com.wosai.trade.service.ServiceFeeEffectiveService;
import com.wosai.trade.service.ServiceFeePeriodService;
import com.wosai.uc.service.*;
import com.wosai.upay.activity.service.ActivityGoodsMapService;
import com.wosai.upay.bank.info.api.service.IndustryV2Service;
import com.wosai.upay.qrcode.service.QrcodeService;
import facade.ICustomerRelationFacade;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2020-03-11
 */
@Configuration
public class JsonRpcConfig extends AbstractJsonProxyFactoryBean {

    @Value("${jsonrpc.uc-token-service-url}")
    private String ucTokenServiceUrl;

    @Value("${jsonrpc.uc-user-service-url}")
    private String ucUserServiceUrl;

    @Value("${jsonrpc.merchant-user-service-url}")
    private String merchantUserServiceUrl;

    @Value("${jsonrpc.app-backend-service-url}")
    private String appBackendServiceUrl;

    @Value("${jsonrpc.merchant-service-url}")
    private String merchantServiceUrl;

    @Value("${jsonrpc.market-merchant-service-url}")
    private String marketMerchantServiceUrl;

    @Value("${jsonrpc.mcc-url}")
    private String mccUrl;

    @Value("${jsonrpc.market-awesome-goods-url}")
    private String marketAwesomeGoodsServiceUrl;

    @Value("${jsonrpc.uitem-core-service-url}")
    private String uitemCoreServiceUrl;

    @Value("${jsonrpc.core-business-url}")
    private String coreBusinessUrl;

    @Value("${jsonrpc.galaxy-service-url}")
    private String galaxyServiceUrl;

    @Value("${jsonrpc.customer-user-service-url}")
    private String customerUserServiceUrl;

    @Value("${jsonrpc.sales-terminal-order-service-url}")
    private String salesTerminalOrderServiceUrl;

    @Value("${jsonrpc.crow-server-service-url}")
    private String crowServerServiceUrl;

    @Value("${jsonrpc.customer-relation-service-url}")
    private String customerRelationServiceUrl;

    @Value("${jsonrpc.sales-system-service-url}")
    private String salesSystemServiceUrl;

    @Value("${jsonrpc.sp-workflow-service-url}")
    private String spWorkflowServiceUrl;

    @Value("${jsonrpc.boss-circle}")
    private String bossCircleUrl;

    @Value("${jsonrpc.tethys}")
    private String tethysUrl;

    @Value("${jsonrpc.upay-activity-url}")
    private String upayActivityUrl;

    @Value("${jsonrpc.data-events-url}")
    private String dataEventsUrl;

    @Value("${jsonrpc.awesome-message-service-url}")
    private String awesomeMessageServiceUrl;

    @Value("${jsonrpc.marketing-saas-external-platform}")
    private String marketingSaasExternalPlatformUrl;

    @Value("${jsonrpc.sales-system-profit-service-url}")
    private String salesSystemProfitServiceUrl;

    @Value("${jsonrpc.smart-goods-url}")
    private String smartGoodsUrl;

    @Value("${jsonrpc.smart-standard-goods-url}")
    private String smartStandardGoodsUrl;

    @Value("${jsonrpc.invoice-url}")
    private String invoiceUrl;

    @Value("${jsonrpc.oms-service-url}")
    private String omsServiceUrl;

    @Value("${jsonrpc.gated-server-url}")
    private String gatedServiceUrl;

    @Value("${jsonrpc.marketing-saas-merchant-url}")
    private String marketingSaasMerchantUrl;

    @Value("${jsonrpc.mkss-customer-url}")
    private String mkssCustomerUrl;

    @Value("${jsonrpc.trade-manage-service}")
    private String tradeManageService;

    @Value("${jsonrpc.aop-gateway-url}")
    private String aopGatewayUrl;

    @Value("${jsonrpc.licence-manager}")
    private String licenceManagerUrl;

    @Value("${jsonrpc.shouqianba-iot-service-url}")
    private String shouqianbaIotServiceUrl;

    @Value("${jsonrpc.voicebox-push-url}")
    private String voiceboxPushUrl;

    @Value("${jsonrpc.upay-qrcode}")
    private String upayQrcodeUrl;

    @Value("${jsonrpc.outer-service-adapter-url}")
    private String outerServiceAdapterUrl;

    @Value("${jsonrpc.marketing-saas-prepaid-card-url}")
    private String marketingSaasPrepaidCardUrl;

    @Value("${jsonrpc.renewal-center-url}")
    private String renewalCenterUrl;

    @Value("${jsonrpc.order-center-url}")
    private String orderCenterServiceUrl;

    @Value("${jsonrpc.bank-info-service-url}")
    private String bankInfoServiceUrl;

    @Value("${jsonrpc.business-logstash-url}")
    private String businessLogstashUrl;

    @Value("${jsonrpc.merchant-user-url}")
    private String merchantUserUrl;

    protected static final ObjectMapper lowerCamelCaseObjectMapper;

    static {
        lowerCamelCaseObjectMapper = new ObjectMapper();
        lowerCamelCaseObjectMapper.setPropertyNamingStrategy(PropertyNamingStrategy.LOWER_CAMEL_CASE);
        lowerCamelCaseObjectMapper.setHandlerInstantiator(new RowDeserializerInstantiator());
        lowerCamelCaseObjectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        lowerCamelCaseObjectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    @Value("${jsonrpc.third-mapping-manage-url}")
    private String thirdMappingManageUrl;

    @Bean("tableRpcServiceV2")
    public JsonProxyFactoryBean tableRpcServiceV2() {
        return getJsonProxyFactoryWareTracingBean(orderCenterServiceUrl + "/rpc/table/v2", TableRpcServiceV2.class,500,300);
    }

    @Bean("cashierPaymentTerminalService")
    public JsonProxyFactoryBean cashierPaymentTerminalService() {
        return getJsonProxyFactoryWareTracingBean(orderCenterServiceUrl + "/rpc/cashier/payment/terminal", CashierPaymentTerminalService.class,500,300);
    }

    @Bean("orderCallRpcService")
    public JsonProxyFactoryBean orderCallRpcService() {
        return getJsonProxyFactoryWareTracingBean(orderCenterServiceUrl + "/rpc/order/call", OrderCallRpcService.class);
    }

    @Bean("sqbUcTokenService")
    public JsonProxyFactoryBean sqbUcTokenService() {
        return getJsonProxyFactoryWareTracingBean(ucTokenServiceUrl + "/rpc/uc_token", UcTokenService.class);
    }

    @Bean("sqbUcTokenInnerService")
    public JsonProxyFactoryBean sqbUcTokenInnerService() {
        return getJsonProxyFactoryWareTracingBean(ucTokenServiceUrl + "/rpc/uc_token_inner", UcTokenInnerService.class);
    }

    @Bean("sqbLoginService")
    public JsonProxyFactoryBean sqbLoginService() {
        return getJsonProxyFactoryWareTracingBean(ucUserServiceUrl + "/rpc/login", LoginService.class);
    }

    @Bean("supportService")
    public JsonProxyFactoryBean supportService() {
        return getJsonProxyFactoryWareTracingBean(tethysUrl + "/rpc/support", com.wosai.market.tethys.api.service.SupportService.class);
    }

    @Bean("singleActivityRemoteService")
    public JsonProxyFactoryBean singleActivityRemoteService() {
        return getJsonProxyFactoryWareTracingBean(tethysUrl + "/rpc/singleActivity", SingleActivityRemoteService.class);
    }

    @Bean("secondActivityRemoteService")
    public JsonProxyFactoryBean secondActivityRemoteService() {
        return getJsonProxyFactoryWareTracingBean(tethysUrl + "/rpc/secondActivity", SecondActivityRemoteService.class);
    }

    @Bean("activityRemoteService")
    public JsonProxyFactoryBean activityRemoteService() {
        return getJsonProxyFactoryWareTracingBean(tethysUrl + "/rpc/activity", ActivityRemoteService.class);
    }

    @Bean("upayActivityGoodsMapService")
    public JsonProxyFactoryBean upayActivityGoodsMapService() {
        return getJsonProxyFactoryWareTracingBean(upayActivityUrl + "/rpc/activityGoodsMap", ActivityGoodsMapService.class);
    }

    @Bean("sqbUcUserService")
    public JsonProxyFactoryBean sqbUcUserService() {
        return getJsonProxyFactoryWareTracingBean(ucUserServiceUrl + "/rpc/ucUser", UcUserService.class);
    }

    @Bean("sqbLoginMethodService")
    public JsonProxyFactoryBean sqbLoginMethodService() {
        return getJsonProxyFactoryWareTracingBean(ucUserServiceUrl + "/rpc/loginMethod", LoginMethodService.class);
    }

    @Bean("sqbMchUserLoginService")
    public JsonProxyFactoryBean sqbMchUserLoginService() {
        return getJsonProxyFactoryWareTracingBean(merchantUserServiceUrl + "/rpc/login", MchUserLoginService.class);
    }

    @Bean("sqbMerchantUserServiceV2")
    public JsonProxyFactoryBean sqbMerchantUserServiceV2() {
        return getJsonProxyFactoryWareTracingBean(merchantUserServiceUrl + "/rpc/merchantuserV2", MerchantUserServiceV2.class);
    }

    @Bean("sqbMerchantUserRoleService")
    public JsonProxyFactoryBean sqbMerchantUserRoleService() {
        return getJsonProxyFactoryWareTracingBean(merchantUserServiceUrl + "/rpc/merchantUserRole", MerchantUserRoleService.class);
    }

    @Bean("sqbMerchantUserAccountService")
    public JsonProxyFactoryBean sqbMerchantUserAccountService() {
        return getJsonProxyFactoryWareTracingBean(merchantUserServiceUrl + "/rpc/account", AccountService.class);
    }

    @Bean("sqbMerchantUserUcUserAccountService")
    public JsonProxyFactoryBean sqbMerchantUserUcUserAccountService() {
        return getJsonProxyFactoryWareTracingBean(merchantUserServiceUrl + "/rpc/ucUser", UcUserAccountService.class);
    }

    @Bean("sqbAppBackendUserService")
    public JsonProxyFactoryBean sqbAppBackendUserService() {
        return getJsonProxyFactoryWareTracingBean(merchantUserServiceUrl + "/rpc/appUser", AppBackendUserService.class, 2000, connectionTimeOutMills);
    }

    @Bean("sqbAppUserService")
    public JsonProxyFactoryBean sqbAppUserService() {
        return getJsonProxyFactoryWareTracingBean(appBackendServiceUrl + "/rpc/appUser", AppUserService.class, 2000, connectionTimeOutMills);
    }

    @Bean("sqbLoginDeviceService")
    public JsonProxyFactoryBean sqbLoginDeviceService() {
        return getJsonProxyFactoryWareTracingBean(appBackendServiceUrl + "/rpc/loginDevice", LoginDeviceService.class);
    }

    @Bean("sqbIAccountService")
    public JsonProxyFactoryBean sqbIAccountService() {
        return getJsonProxyFactoryWareTracingBean(appBackendServiceUrl + "/rpc/account", IAccountService.class);
    }

    @Bean("sqbSmsService")
    public JsonProxyFactoryBean sqbSmsService() {
        return getJsonProxyFactoryWareTracingBean(merchantUserServiceUrl + "/rpc/sms", SmsService.class);
    }


    @Bean("sqbMerchantService")
    public JsonProxyFactoryBean sqbMerchantService(){
        return getJsonProxyFactoryWareTracingBean(merchantServiceUrl + "/rpc/merchant", MerchantService.class);
    }

    @Bean("sqbStoreService")
    public JsonProxyFactoryBean sqbStoreService(){
        return getJsonProxyFactoryWareTracingBean(merchantServiceUrl + "/rpc/store", StoreService.class);
    }

    @Bean("sqbQrCodeRemoteService")
    public JsonProxyFactoryBean sqbQrCodeRemoteService(){
        return getJsonProxyFactoryWareTracingBean(marketMerchantServiceUrl + "/rpc/qrcode", QRCodeRemoteService.class);
    }

    @Bean("sqbUfoodRemoteService")
    public JsonProxyFactoryBean sqbUfoodRemoteService(){
        return getJsonProxyFactoryWareTracingBean(marketMerchantServiceUrl + "/rpc/ufood", UfoodRemoteService.class);
    }

    @Bean("sqbMerchantContractRemoteService")
    public JsonProxyFactoryBean sqbMerchantContractRemoteService(){
        return getJsonProxyFactoryWareTracingBean(marketMerchantServiceUrl + "/rpc/contract", MerchantContractRemoteService.class, 3000, connectionTimeOutMills);
    }

    @Bean(value = "marketStoreRemoteService")
    public JsonProxyFactoryBean marketMerchantStoreRemoteService() {
        return getJsonProxyFactoryWareTracingBean(marketMerchantServiceUrl + "/rpc/merchant/store", StoreRemoteService.class);
    }

    @Bean(value = "marketMerchantRemoteService")
    public JsonProxyFactoryBean marketMerchantMerchantRemoteService() {
        return getJsonProxyFactoryWareTracingBean(marketMerchantServiceUrl + "/rpc/merchant", MerchantRemoteService.class);
    }

    @Bean(value = "merchantTypeRemoteService")
    public JsonProxyFactoryBean merchantTypeRemoteService() {
        return getJsonProxyFactoryWareTracingBean(marketMerchantServiceUrl + "/rpc/merchantType", MerchantTypeRemoteService.class);
    }

    @Bean("sqbConfigRemoteService")
    public JsonProxyFactoryBean sqbConfigRemoteService(){
        return getJsonProxyFactoryWareTracingBean(mccUrl + "/rpc/config", ConfigRemoteService.class);
    }

    @Bean("sqbDefaultConfigRemoteService")
    public JsonProxyFactoryBean sqbDefaultConfigRemoteService(){
        return getJsonProxyFactoryWareTracingBean(mccUrl + "/rpc/defaultConfig", DefaultConfigRemoteService.class);
    }

    @Bean("mccWhiteListV2RemoteService")
    public JsonProxyFactoryBean mccWhiteListV2RemoteService(){
        return getJsonProxyFactoryWareTracingBean(mccUrl + "/rpc/whiteListV2", WhiteListV2RemoteService.class);
    }

    @Bean("uidRemoteService")
    public JsonProxyFactoryBean uidRemoteService(){
        return getJsonProxyFactoryWareTracingBean( mccUrl + "/rpc/uid", UidRemoteService.class);
    }

    @Bean("productStockPpcService")
    public JsonProxyFactoryBean productStockPpcService(){
        return getJsonProxyFactoryWareTracingBean( marketAwesomeGoodsServiceUrl + "/rpc/stock", ProductStockService.class);
    }

    @Bean("batchCreateDataService")
    public JsonProxyFactoryBean batchCreateDataService(){
        return getJsonProxyFactoryWareTracingBean( marketAwesomeGoodsServiceUrl + "/rpc/batch/create", BatchCreateDataService.class);
    }

    @Bean("batchSyncDataService")
    public JsonProxyFactoryBean batchSyncDataService(){
        return getJsonProxyFactoryWareTracingBean( marketAwesomeGoodsServiceUrl + "/rpc/batch/sync", BatchSyncDataService.class);
    }

    @Bean("merchantAttributeService")
    public JsonProxyFactoryBean merchantAttributeService(){
        return getJsonProxyFactoryWareTracingBean( marketAwesomeGoodsServiceUrl + "/rpc/merchant/attribute", MerchantAttributeService.class);
    }

    @Bean("merchantCategoryService")
    public JsonProxyFactoryBean merchantCategoryService(){
        return getJsonProxyFactoryWareTracingBean( marketAwesomeGoodsServiceUrl + "/rpc/merchant/category", MerchantCategoryService.class);
    }

    @Bean("merchantMaterialService")
    public JsonProxyFactoryBean merchantMaterialService(){
        return getJsonProxyFactoryWareTracingBean( marketAwesomeGoodsServiceUrl + "/rpc/merchant/material", MerchantMaterialService.class);
    }

    @Bean("merchantProductService")
    public JsonProxyFactoryBean merchantProductService(){
        return getJsonProxyFactoryWareTracingBean( marketAwesomeGoodsServiceUrl + "/rpc/merchant/product", MerchantProductService.class);
    }

    @Bean("merchantSkuService")
    public JsonProxyFactoryBean merchantSkuService(){
        return getJsonProxyFactoryWareTracingBean( marketAwesomeGoodsServiceUrl + "/rpc/merchant/sku", MerchantSkuService.class);
    }

    @Bean("storeAttributeService")
    public JsonProxyFactoryBean storeAttributeService(){
        return getJsonProxyFactoryWareTracingBean( marketAwesomeGoodsServiceUrl + "/rpc/store/attribute", StoreAttributeService.class);
    }

    @Bean("storeCategoryService")
    public JsonProxyFactoryBean storeCategoryService(){
        return getJsonProxyFactoryWareTracingBean( marketAwesomeGoodsServiceUrl + "/rpc/store/category", StoreCategoryService.class);
    }

    @Bean("storeMaterialService")
    public JsonProxyFactoryBean storeMaterialService(){
        return getJsonProxyFactoryWareTracingBean( marketAwesomeGoodsServiceUrl + "/rpc/store/material", StoreMaterialService.class);
    }

    @Bean("storeProductService")
    public JsonProxyFactoryBean storeProductService(){
        return getJsonProxyFactoryWareTracingBean( marketAwesomeGoodsServiceUrl + "/rpc/store/product", StoreProductService.class, 2000, connectionTimeOutMills);
    }

    @Bean("storeProductWaterMarkService")
    public JsonProxyFactoryBean storeProductWaterMarkService(){
        return getJsonProxyFactoryWareTracingBean(marketAwesomeGoodsServiceUrl + "/rpc/store/product/water/mark", StoreProductWaterMarkService.class);
    }

    @Bean(value = "openTableMustOrderRpcService")
    public JsonProxyFactoryBean openTableMustOrderRpcService() {
        return getJsonProxyFactoryWareTracingBean(marketAwesomeGoodsServiceUrl + "/rpc/openTable/mustOrder", OpenTableMustOrderRpcService.class);
    }

    @Bean(value = "productMustOrderRpcService")
    public JsonProxyFactoryBean productMustOrderRpcService() {
        return getJsonProxyFactoryWareTracingBean(marketAwesomeGoodsServiceUrl + "/rpc/product/mustOrder", ProductMustOrderRpcService.class);
    }

    @Bean(name = "iPushMqttService")
    public JsonProxyFactoryBean IPushMqttService() {
        return getJsonProxyFactoryWareTracingBean(voiceboxPushUrl + "/rpc/pushMqtt", IPushMqttService.class);


    }

    @Bean
    public JsonProxyFactoryBean qrcodeService() {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(upayQrcodeUrl + "/rpc/qrcode");
        factory.setServiceInterface(QrcodeService.class);
        factory.setServerName("upay-qrcode-service");
        factory.setReadTimeoutMillis(readTimeOutMills);
        factory.setConnectionTimeoutMillis(connectionTimeOutMills);
        return factory;
    }

    @Bean(value = "businessOpLogService")
    public JsonProxyFactoryBean businessOpLogService() {
        return getJsonProxyFactoryWareTracingBean(businessLogstashUrl + "/rpc/businessOpLog", BusinessOpLogService.class);
    }

    @Bean(value = "productMessageRpcService")
    public JsonProxyFactoryBean productMessageRpcService() {
        return getJsonProxyFactoryWareTracingBean(marketAwesomeGoodsServiceUrl + "/rpc/message", ProductMessageRpcService.class);
    }

    @Bean("itemService")
    public JsonProxyFactoryBean itemService() {
        return getJsonProxyFactoryWareTracingBean(uitemCoreServiceUrl + "/rpc/item", ItemService.class, "uitem-core", 5000, connectionTimeOutMills);
    }

    @Bean("sqbCoreBusinessStoreService")
    public JsonProxyFactoryBean sqbCoreBusinessStoreService() {
        return getJsonProxyFactoryWareTracingBean(coreBusinessUrl + "/rpc/store", com.wosai.upay.core.service.StoreService.class);
    }

    @Bean("sqbCoreBusinessTerminalService")
    public JsonProxyFactoryBean sqbCoreBusinessTerminalService() {
        return getJsonProxyFactoryWareTracingBean(coreBusinessUrl + "/rpc/terminal", com.wosai.upay.core.service.TerminalService.class);
    }

    @Bean("sqbCoreBusinessMerchantService")
    public JsonProxyFactoryBean sqbCoreBusinessMerchantService() {
        return getJsonProxyFactoryWareTracingBean(coreBusinessUrl + "/rpc/merchant", com.wosai.upay.core.service.MerchantService.class);
    }

    @Bean("customerUserService")
    public JsonProxyFactoryBean customerUserService() {
        return getJsonProxyFactoryWareTracingBean(customerUserServiceUrl + "/rpc/user", com.wosai.market.user.service.UserService.class);
    }

    @Bean("authorizeService")
    public JsonProxyFactoryBeanTracerV153 authorizeService() {
        return getJsonProxyFactoryWareTracingV153Bean(galaxyServiceUrl + "/rpc/authorize", AuthorizeService.class);
    }

    @Bean("outerOrderEdgeService")
    public JsonProxyFactoryBean outerOrderEdgeService() {
        return getJsonProxyFactoryWareTracingBean(salesTerminalOrderServiceUrl + "/rpc/outerOrderEdge", OuterOrderEdgeService.class);
    }

    @Bean("salesOrderItemService")
    public JsonProxyFactoryBean salesOrderItemService() {
        return getJsonProxyFactoryWareTracingBean(salesTerminalOrderServiceUrl + "/rpc/orderItem", IOrderItemService.class);
    }

    @Bean("iOrderService")
    public JsonProxyFactoryBean iOrderService() {
        return getJsonProxyFactoryWareTracingBean(salesTerminalOrderServiceUrl + "/rpc/order", IOrderService.class);
    }

    @Bean("onlineQueryService")
    public JsonProxyFactoryBean onlineQueryService() {
        return getJsonProxyFactoryWareTracingBean(crowServerServiceUrl + "/rpc/online_queries", OnlineQueryService.class);
    }

    @Bean("customerRelationFacade")
    public JsonProxyFactoryBean customerRelationFacade() {
        return getJsonProxyFactoryWareTracingBean(customerRelationServiceUrl + "/rpc/relation", ICustomerRelationFacade.class);
    }

    @Bean("organizationService")
    public JsonProxyFactoryBean organizationService() {
        return getJsonProxyFactoryWareTracingBean(salesSystemServiceUrl + "/rpc/organization", OrganizationService.class);
    }

    @Bean("salesUserService")
    public JsonProxyFactoryBean salesUserService() {
        return getJsonProxyFactoryWareTracingBean(salesSystemServiceUrl + "/rpc/user", UserService.class);
    }

    @Bean("salesUserOrganizationService")
    public JsonProxyFactoryBean salesUserOrganizationService() {
        return getJsonProxyFactoryWareTracingBean(salesSystemServiceUrl + "/rpc/userOrganization", UserOrganizationService.class);
    }

    @Bean("salesEsUserService")
    public JsonProxyFactoryBean salesEsUserService() {
        return getJsonProxyFactoryWareTracingBean(salesSystemServiceUrl + "/rpc/userv2", EsUserService.class);
    }

    @Bean("salesPositionService")
    public JsonProxyFactoryBean salesPositionService() {
        return getJsonProxyFactoryWareTracingBean(salesSystemServiceUrl + "/rpc/position", PositionService.class);
    }

    @Bean("spWorkflowAuditService")
    public JsonProxyFactoryBean spWorkflowAuditService() {
        return getJsonProxyFactoryWareTracingBean(spWorkflowServiceUrl + "/rpc/audit", AuditService.class);
    }

    @Bean("userFollowAndFansService")
    public JsonProxyFactoryBean userFollowAndFansService() {
        return getJsonProxyFactoryWareTracingBean(bossCircleUrl + "/rpc/follow", UserFollowAndFansService.class);
    }

    @Bean("storeMessageRpcService")
    public JsonProxyFactoryBean storeMessageRpcService() {
        return getJsonProxyFactoryWareTracingBean(awesomeMessageServiceUrl + "/rpc/store/message", StoreMessageRpcService.class);
    }

    @Bean("rmqxConfigRpcService")
    public JsonProxyFactoryBean rmqxConfigRpcService() {
        return getJsonProxyFactoryWareTracingBean(awesomeMessageServiceUrl + "/rpc/rmqx/config", RmqxConfigRpcService.class);
    }

    @Bean("awmRmqxMessageRpcService")
    public JsonProxyFactoryBean awmRmqxMessageRpcService() {
        return getJsonProxyFactoryWareTracingBean(awesomeMessageServiceUrl + "/rpc/rmqx/message", com.wosai.market.awesome.message.service.RmqxMessageRpcService.class);
    }

    @Bean("awmRdsMessageRpcService")
    public JsonProxyFactoryBean awmRdsMessageRpcService() {
        return getJsonProxyFactoryWareTracingBean(awesomeMessageServiceUrl + "/rpc/rds/message", com.wosai.market.awesome.message.service.RdsMessageRpcService.class);
    }

    @Bean("awmAggregateMessageRpcService")
    public JsonProxyFactoryBean awmAggregateMessageRpcService() {
        return getJsonProxyFactoryWareTracingBean(awesomeMessageServiceUrl + "/rpc/aggregate/message", com.wosai.market.awesome.message.service.AggregateMessageRpcService.class);
    }

    @Bean("awmLicenceMessageRpcService")
    public JsonProxyFactoryBean awmLicenceMessageRpcService() {
        return getJsonProxyFactoryWareTracingBean(awesomeMessageServiceUrl + "/rpc/licence/message", LicenceMessageRpcService.class);
    }

    @Bean("awmPollingMessageRpcService")
    public JsonProxyFactoryBean awmPollingMessageRpcService() {
        return getJsonProxyFactoryWareTracingBean(awesomeMessageServiceUrl + "/rpc/polling/message", PollingMessageRpcService.class);
    }

    @Bean("productTagRpcService")
    public JsonProxyFactoryBean productTagRpcService() {
        return getJsonProxyFactoryWareTracingBean(smartGoodsUrl + "/rpc/smart/product/tag", IProductTagRpcService.class);
    }

    @Bean("marketingSaasExternalPlatformRemoteService")
    public JsonProxyFactoryBean marketingSaasExternalPlatformRemoteService() {
        return getJsonProxyFactoryWareTracingBean(marketingSaasExternalPlatformUrl + "/rpc/external-coupon-v2-adapter", ExternalCouponV2Adapter.class);
    }

    @Bean("profitDirectSignService")
    public JsonProxyFactoryBean profitDirectSignService() {
        return getJsonProxyFactoryWareTracingBean( salesSystemProfitServiceUrl + "/rpc/profit/direct_sign", ProfitDirectSignService.class);
    }

    @Bean("cashRegisterService")
    public JsonProxyFactoryBean cashRegisterService() {
        return getJsonProxyFactoryWareTracingBean(  thirdMappingManageUrl + "/rpc/cashRegister", CashRegisterService.class);
    }

    @Bean("IAppGatedRuleService")
    public JsonProxyFactoryBean IAppGatedRuleService() {
        return getJsonProxyFactoryWareTracingBean( gatedServiceUrl + "/rpc/appGatedRule", IAppGatedRuleService.class);
    }

    @Bean("ServiceFeePeriodService")
    public JsonProxyFactoryBean serviceFeePeriodService() {
        return getJsonProxyFactoryWareTracingBean( tradeManageService + "/rpc/service_fee_period", ServiceFeePeriodService.class);
    }

    @Bean("ServiceFeeEffectiveService")
    public JsonProxyFactoryBean serviceFeeEffectiveService() {
        return getJsonProxyFactoryWareTracingBean( tradeManageService + "/rpc/service_fee_effective", ServiceFeeEffectiveService.class);
    }

    @Bean("clientSideNoticeService")
    public JsonProxyFactoryBean clientSideNoticeService() {
        return getJsonProxyFactoryWareTracingBean( aopGatewayUrl + "/rpc/clientSide/notice", ClientSideNoticeService.class);
    }

    @Bean("alipayAiRemoteService")
    public JsonProxyFactoryBean alipayAiRemoteService() {
        return getJsonProxyFactoryWareTracingBean( outerServiceAdapterUrl + "/rpc/alipay/ai", AlipayAiRemoteService.class);
    }

    @Bean
    public JsonProxyFactoryBean dataEventsService() {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(dataEventsUrl + "/rpc/data-events");
        factory.setServiceInterface(DataEventsService.class);
        factory.setServerName("data-events");
        factory.setReadTimeoutMillis(readTimeOutMills);
        factory.setConnectionTimeoutMillis(connectionTimeOutMills);
        return factory;
    }

    @Bean(value = "smartGoodsCategoryRpcService")
    public JsonProxyFactoryBean smartGoodsCategoryRpcService() {
        return getJsonProxyFactoryWareTracingBean(smartGoodsUrl + "/rpc/category", "smart-goods", CategoryService.class);
    }

    @Bean(value = "smartGoodsIWaterlineRpcService")
    public JsonProxyFactoryBean smartGoodsIWaterlineRpcService() {
        return getJsonProxyFactoryWareTracingBean(smartGoodsUrl + "/rpc/waterline", "smart-goods", IWaterlineRpcService.class);
    }

    @Bean(value = "smartGoodsIProductRpcService")
    public JsonProxyFactoryBean smartGoodsIProductRpcService() {
        return getJsonProxyFactoryWareTracingBean(smartGoodsUrl + "/rpc/product", "smart-goods", IProductRpcService.class);
    }

    @Bean(value = "smartGoodsIProductSearchRpcService")
    public JsonProxyFactoryBean smartGoodsIProductSearchRpcService() {
        return getJsonProxyFactoryWareTracingBean(smartGoodsUrl + "/rpc/product/search", "smart-goods", IProductSearchRpcService.class);
    }

    @Bean(value = "smartGoodsTemplateUseRpcService")
    public JsonProxyFactoryBean smartGoodsTemplateUseRpcService() {
        return getJsonProxyFactoryWareTracingBean(smartGoodsUrl + "/rpc/templateUse", "smart-goods", TemplateUseRpcService.class);
    }

    @Bean(value = "smartGoodsIStockRpcService")
    public JsonProxyFactoryBean smartGoodsIStockRpcService() {
        return getJsonProxyFactoryWareTracingBean(smartGoodsUrl + "/rpc/smart/stock", IStockRpcService.class, 2000, connectionTimeOutMills);
    }

    @Bean(value = "productTaskRpcService")
    public JsonProxyFactoryBean productTaskRpcService() {
        return getJsonProxyFactoryWareTracingBean(smartGoodsUrl + "/rpc/product/task", "smart-goods", IProductTaskRpcService.class);
    }

    @Bean(value = "storedOwnerService")
    public JsonProxyFactoryBean storedOwnerService() {
        return getJsonProxyFactoryWareTracingBean(marketingSaasPrepaidCardUrl + "/rpc/storedOwner", StoredOwnerService.class);
    }

    @Bean(value = "industryV2Service")
    public JsonProxyFactoryBean industryV2Service() {
        return getJsonProxyFactoryWareTracingBean(bankInfoServiceUrl + "/rpc/industry_v2", IndustryV2Service.class);
    }

    @Bean
    public JsonProxyFactoryBean smartStandardGoodsCategoryRpcService() {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(smartStandardGoodsUrl + "/rpc/smart/standard/category");
        factory.setServiceInterface(ICategoryRpcService.class);
        factory.setServerName("smart-standard-goods");
        factory.setReadTimeoutMillis(readTimeOutMills);
        factory.setConnectionTimeoutMillis(connectionTimeOutMills);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean smartStandardGoodsRpcService() {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(smartStandardGoodsUrl + "/rpc/smart/standard/goods");
        factory.setServiceInterface(IRetailGoodsRpcService.class);
        factory.setServerName("smart-standard-goods");
        factory.setReadTimeoutMillis(readTimeOutMills);
        factory.setConnectionTimeoutMillis(connectionTimeOutMills);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean instantStoreService() {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(invoiceUrl + "/einvoice/mine/stores");
        factory.setServiceInterface(InstantStoreService.class);
        factory.setServerName("invoice-app");
        factory.setReadTimeoutMillis(readTimeOutMills);
        factory.setConnectionTimeoutMillis(connectionTimeOutMills);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean omsSkuService() {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(omsServiceUrl + "/rpc/sku");
        factory.setServiceInterface(SkuService.class);
        factory.setServerName("oms-service");
        factory.setObjectMapper(lowerCamelCaseObjectMapper);
        factory.setReadTimeoutMillis(readTimeOutMills);
        factory.setConnectionTimeoutMillis(connectionTimeOutMills);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean omsOrderService() {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(omsServiceUrl + "/rpc/order");
        factory.setServiceInterface(OrderService.class);
        factory.setServerName("oms-service");
        factory.setObjectMapper(lowerCamelCaseObjectMapper);
        factory.setReadTimeoutMillis(readTimeOutMills);
        factory.setConnectionTimeoutMillis(connectionTimeOutMills);
        return factory;
    }

    @Bean
    public JsonProxyFactoryBean marketingSaasMerchantMemberOpenService() {
        return getJsonProxyFactoryWareTracingBean(marketingSaasMerchantUrl + "/rpc/merchant-member-open", "marketing-saas-merchant", MerchantMemberOpenService.class);
    }

    @Bean
    public JsonProxyFactoryBean mkssCustomerMemberAvailableStoreService() {
        return getJsonProxyFactoryWareTracingBean(mkssCustomerUrl + "/rpc/member-available-store", "mkss-customer", MemberAvailableStoreService.class);
    }

    @Bean(value = "memberMessageMerchantAccountService")
    public JsonProxyFactoryBean mkssMemberMessageMerchantAccountService() {
        return getJsonProxyFactoryWareTracingBean(mkssCustomerUrl + "/rpc/message-merchant-account", "mkss-customer", MemberMessageMerchantAccountService.class);
    }

    @Bean(value = "memberMsgTaskService")
    public JsonProxyFactoryBean mkssMemberMsgTaskService() {
        return getJsonProxyFactoryWareTracingBean(mkssCustomerUrl + "/rpc/member-msg-task", "mkss-customer", MemberMsgTaskService.class);
    }

    @Bean(value = "memberMessageSendRecordService")
    public JsonProxyFactoryBean mkssMemberMessageSendRecordService() {
        return getJsonProxyFactoryWareTracingBean(mkssCustomerUrl + "/rpc/message-send-record", "mkss-customer", MemberMessageSendRecordService.class);
    }

    @Bean("licenceRemoteService")
    public JsonProxyFactoryBean licenceRemoteService() {
        return getJsonProxyFactoryWareTracingBean(licenceManagerUrl + "/rpc/licence", LicenceRemoteService.class);
    }

    @Bean("migrationRemoteService")
    public JsonProxyFactoryBean migrationRemoteService() {
        return getJsonProxyFactoryWareTracingBean(licenceManagerUrl + "/rpc/migration", MigrationRemoteService.class);
    }
    @Bean
    public JsonProxyFactoryBean shouqianbaIotServiceDeviceQrcodeService() {
        return getJsonProxyFactoryWareTracingBean(shouqianbaIotServiceUrl + "/rpc/deviceQrcode", "shouqianba-iot", DeviceQrcodeService.class);
    }

    @Bean("quotaService")
    public JsonProxyFactoryBean quotaService() {
        return getJsonProxyFactoryWareTracingBean(renewalCenterUrl + "/rpc/quota", QuotaService.class);
    }


    @Bean("unitRpcService")
    public JsonProxyFactoryBean unitRpcService() {
        return getJsonProxyFactoryWareTracingBean(smartGoodsUrl + "/rpc/unit", IUnitRpcService.class);
    }

    @Bean("merchantUserService")
    public JsonProxyFactoryBean merchantUserService() {
        return getJsonProxyFactoryWareTracingBean(merchantUserUrl + "/rpc/merchantuser", MerchantUserService.class);
    }
}
