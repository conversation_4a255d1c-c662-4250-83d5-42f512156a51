package com.wosai.smartbiz.config;

import com.fasterxml.jackson.databind.JsonNode;
import com.googlecode.jsonrpc4j.InvocationListener;
import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.pantheon.order.exception.InvalidParamsException;
import com.wosai.smartbiz.Constants;
import com.wosai.smartbiz.base.exceptions.BusinessException;
import com.wosai.smartbiz.base.exceptions.ConcurrentException;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.base.exceptions.TokenValidateException;
import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.marker.Markers;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.validation.ConstraintViolationException;
import java.lang.reflect.Method;
import java.util.*;


/**
 * <AUTHOR>
 * @date 2020-03-11
 */
@Slf4j
@Component
public class JsonRpcLogAspect implements InvocationListener {

    private static final String RPC_PATH_METHOD_CONNECT_SYMBOL = "/";
    private static final String EMPTY_RPC_PATH = "";


    @Override
    public void willInvoke(Method method, List<JsonNode> arguments) {
    }

    @Override
    public void didInvoke(Method method, List<JsonNode> arguments, Object result, Throwable t, long duration) {
        try {
            // 通过注解获取RPC路径，和方法名组成完整路径
            String rpcPath = Optional.ofNullable(method.getDeclaringClass().getAnnotation(JsonRpcService.class))
                    .filter(p -> StringUtils.isNotBlank(p.value()))
                    .map(p -> p.value() + RPC_PATH_METHOD_CONNECT_SYMBOL)
                    .orElse(EMPTY_RPC_PATH);
            String methodPath = rpcPath + method.getName();

            Map<String, Object> logMap = new HashMap<>();
            logMap.put(Constants.LogConstants.CLASS, method.getDeclaringClass().getName());
            logMap.put(Constants.LogConstants.METHOD, method.getName());
            logMap.put(Constants.LogConstants.URI, methodPath);
            //logMap.put(Constants.LogConstants.ARGUMENTS, arguments);
            logMap.put(Constants.LogConstants.RESULT, result);
            logMap.put(Constants.LogConstants.DURATION, duration);
            logMap.put(Constants.LogConstants.MESSAGE, arguments);
            if (Objects.nonNull(t)) {
                Throwable throwable = (Objects.nonNull(t.getCause())) ? t.getCause() : t;
                String error = WosaiStringUtils.defaultIfEmpty(t.getMessage(), throwable.getMessage());
                logMap.put(Constants.LogConstants.ERROR, error);

                if (isInfoLevelException(throwable)) {
                    log.info(Markers.appendEntries(logMap), "");
                }else if (isWarningLevelException(throwable)) {
                    log.warn(Markers.appendEntries(logMap), "");
                } else {
                    log.error(Markers.appendEntries(logMap), "", t);
                }
            } else {
                log.info(Markers.appendEntries(logMap), "");
            }
        } catch (Exception e) {
        }
    }

    /**
     * 提前可预建的业务不合规异常
     * 如：参数不合规等
     * @param throwable
     * @return
     */
    private boolean isInfoLevelException(Throwable throwable) {
        return throwable instanceof ParamException
                || throwable instanceof IllegalArgumentException
                || throwable instanceof InvalidParamsException
                || throwable instanceof CommonInvalidParameterException
                || throwable instanceof ConstraintViolationException;
    }

    /**
     * 业务不合规异常
     * 如：并发异常、业务异常、Token过期等
     * @param throwable
     * @return
     */
    private boolean isWarningLevelException(Throwable throwable) {
        return throwable instanceof BusinessException
                || throwable instanceof ConcurrentException
                || throwable instanceof TokenValidateException;
    }
}
