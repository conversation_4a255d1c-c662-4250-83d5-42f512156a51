package com.wosai.smartbiz.catering.adapter;

import com.google.common.collect.ImmutableSet;
import com.wosai.market.dto.ProductMustOrder;
import com.wosai.market.enums.SaleStatusEnum;
import com.wosai.market.enums.UnitTypeEnum;
import com.wosai.smartbiz.base.utils.VersionUtil;
import com.wosai.smartbiz.vo.gds.gds.v2.StoreProductVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.List;

@Component(value = "cashierCateringCustomUnitAdapter")
public class CashierCateringCustomUnitAdapter implements ICashierCateringProductAdapter {

    public final static ImmutableSet<String> OLD_SYSTEM_WEIGHT_UNITS = ImmutableSet.of("斤", "kg");

    @Override
    public int order() {
        return 0;
    }

    @Override
    public boolean isLessThanDestAppVersion(String version) {
        return VersionUtil.isLessThanDestAppVersion(version, "3.9.0");
    }

    @Override
    public void process(StoreProductVO product) {
        if (!UnitTypeEnum.WEIGHT.equals(product.getUnitType())) {
            return;
        }
        String unit = product.getSaleUnit();
        // 历史版本同步非系统单位称重商品时，默认为下架
        if (!OLD_SYSTEM_WEIGHT_UNITS.contains(unit)) {
            product.setSaleStatus(SaleStatusEnum.OFF_SALE);
        }
    }

    @Override
    public void processMustOrder(List<ProductMustOrder> productMustOrders) {
        if (CollectionUtils.isEmpty(productMustOrders)) {
            return;
        }
        // 用迭代器遍历判断单位是否可用，如果单位不可用，直接从列表移除
        Iterator<ProductMustOrder> iterator = productMustOrders.iterator();
        while (iterator.hasNext()) {
            ProductMustOrder productMustOrder = iterator.next();
            if (!UnitTypeEnum.WEIGHT.equals(productMustOrder.getUnitType())) {
                continue;
            }
            String unit = productMustOrder.getUnit();
            if (!OLD_SYSTEM_WEIGHT_UNITS.contains(unit)) {
                iterator.remove();
            }
        }
    }
}
