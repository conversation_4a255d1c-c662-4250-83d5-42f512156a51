package com.wosai.smartbiz.catering.adapter;

import com.wosai.market.dto.ProductMustOrder;
import com.wosai.smartbiz.vo.gds.gds.v2.StoreProductVO;

import java.util.List;

public interface ICashierCateringProductAdapter extends ICashierCateringVersionAdapter {

    /**
     * 处理商品返回结果
     */
    void process(StoreProductVO product);

    /**
     * 处理必点商品返回结果
     */
    void processMustOrder(List<ProductMustOrder> productMustOrders);

}
