package com.wosai.smartbiz.assembler;

import com.google.common.collect.Lists;
import com.wosai.smartbiz.dto.business.area.StoreTableAreaDTO;
import com.wosai.smartbiz.dto.business.table.StoreTableDTO;
import com.wosai.smartbiz.goods.domain.repast.table.StoreTableAreaDO;
import com.wosai.smartbiz.goods.domain.repast.table.StoreTableDO;
import com.wosai.smartbiz.uc.domain.QrCodeDO;
import com.wosai.smartbiz.user.user.enums.QrCodeStatus;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021/2/18
 */
public class StoreTableAreaAssembler {

    /**
     * 组装区域-桌台-二维码 DTO List
     * @param areaList 区域
     * @param tableList 桌台
     * @param qrCodeList 二维码
     * @return
     */
    public static List<StoreTableAreaDTO> toDTOList(List<StoreTableAreaDO> areaList, List<StoreTableDO> tableList, List<QrCodeDO> qrCodeList) {
        if(CollectionUtils.isEmpty(areaList)){
            return Lists.newArrayList();
        }

        Map<Long, List<StoreTableDO>> areaIdToTableListMap = ListUtils.defaultIfNull(tableList, Lists.newArrayList()).stream()
                .collect(Collectors.groupingBy(StoreTableDO::getAreaId));

        Map<String, List<QrCodeDO>> businessIdToQrCodeListMap = ListUtils.defaultIfNull(qrCodeList, Lists.newArrayList()).stream()
                .filter(p -> Objects.equals(p.getStatus(), QrCodeStatus.BIND))
                .collect(Collectors.groupingBy(QrCodeDO::getBusinessId));

        return areaList.stream()
                .map(area -> {
                    StoreTableAreaDTO areaDTO = new StoreTableAreaDTO().setAreaDO(area);
                    List<StoreTableDO> tmpTableList = areaIdToTableListMap.get(area.getId());
                    if(CollectionUtils.isNotEmpty(tmpTableList)){
                        List<StoreTableDTO> tableDTOList = tmpTableList.stream()
                                .map(table -> new StoreTableDTO()
                                        .setTableDO(table)
                                        .setQrCodeDOList(businessIdToQrCodeListMap.getOrDefault(table.getId().toString(), Lists.newArrayList()))
                                )
                                .collect(Collectors.toList());
                        areaDTO.setTableDTOList(tableDTOList);
                    } else {
                        areaDTO.setTableDTOList(Lists.newArrayList());
                    }
                    return areaDTO;
                })
                .collect(Collectors.toList());
    }

}
