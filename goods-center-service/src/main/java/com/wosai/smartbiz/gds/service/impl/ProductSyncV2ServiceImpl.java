package com.wosai.smartbiz.gds.service.impl;

import com.google.common.collect.Lists;
import com.wosai.market.dto.category.CategoryDetail;
import com.wosai.market.dto.product.ProductDetail;
import com.wosai.market.dto.product.ProductWaterMark;
import com.wosai.market.enums.SaleSceneEnum;
import com.wosai.market.request.store.StoreCategoryQueryReq;
import com.wosai.market.request.store.StoreProductWaterMarkQueryReq;
import com.wosai.market.service.store.StoreCategoryService;
import com.wosai.market.service.store.StoreProductService;
import com.wosai.market.service.store.StoreProductWaterMarkService;
import com.wosai.smartbiz.base.exceptions.BusinessException;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.gds.service.ProductSyncV2Service;
import com.wosai.smartbiz.goods.query.repast.StoreProductCursorQuery;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2020/12/30
 */
@Service
public class ProductSyncV2ServiceImpl implements ProductSyncV2Service {

    @Autowired
    private StoreProductService storeProductService;

    @Autowired
    private StoreProductWaterMarkService storeProductWaterMarkService;

    @Autowired
    private StoreCategoryService storeCategoryService;

    @Override
    public List<ProductDetail> syncProductToCashier(StoreProductCursorQuery query) {
        if(query.getPageSize() == null || query.getPageSize() <= 0){
            return Lists.newArrayList();
        }
        StoreProductWaterMarkQueryReq queryReq = StoreProductWaterMarkQueryReq.builder()
                .merchantId(query.getMerchantId())
                .storeId(query.getStoreId())
                .deleteMark(query.getDeleteMark())
                .maxWaterMark(query.getMaxWaterMark())
                .minWaterMark(query.getMinWaterMark())
                .pageSize(query.getPageSize())
                .lastId(query.getLastId())
                .saleScenes(ListUtils.defaultIfNull(query.getSaleScenes(), Lists.newArrayList(com.wosai.smartbiz.goods.enums.repast.SaleSceneEnum.values()))
                        .stream()
                        .map(com.wosai.smartbiz.goods.enums.repast.SaleSceneEnum::toAwesome)
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList()))
                .build();
        Result<List<ProductDetail>> detailResult = storeProductService.listByCursor(queryReq);
        if(!detailResult.isSuccess()){
            throw new BusinessException("获取商品信息失败");
        }

        return detailResult.getData();
    }

    @Override
    public ProductWaterMark syncWaterMarkToCashier(String merchantId, String storeId) {

        Result<ProductWaterMark> result = storeProductWaterMarkService.getLatestWaterMark(storeId);
        if(!result.isSuccess()){
            throw new BusinessException("获取商品更新水位线失败");
        }

        return result.getData();
    }

    @Override
    public List<CategoryDetail> syncCategoryToCashier(String merchantId, String storeId) {

        StoreCategoryQueryReq queryReq = StoreCategoryQueryReq.builder().storeId(storeId).build();
        Result<List<CategoryDetail>> result = storeCategoryService.listByCondition(queryReq);
        if(!result.isSuccess()){
            throw new BusinessException("获取分类信息失败");
        }

        return result.getData();
    }

    @Override
    public ProductDetail queryProductBySpuId(String storeId, String spuId) {
        Result<ProductDetail> result = storeProductService.getDetail(storeId, spuId, SaleSceneEnum.ALL);
        if (Objects.isNull(result)) {
            throw new BusinessException("商品服务异常");
        }
        ProductDetail detail = result.getData();
        if (Objects.isNull(detail)) {
            throw new BusinessException("商品不存在，请检查入参");
        }
        return detail;
    }
}
