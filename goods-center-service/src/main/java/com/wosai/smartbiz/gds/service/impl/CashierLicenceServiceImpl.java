package com.wosai.smartbiz.gds.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shouqianba.licence.manager.api.dto.ItemRemainingCountDTO;
import com.shouqianba.licence.manager.api.dto.VersionTypeRemainingCountDTO;
import com.shouqianba.licence.manager.api.dto.res.CancelRenewCheckRes;
import com.shouqianba.licence.manager.api.dto.res.LicenceActiveRes;
import com.shouqianba.workflow.bean.dto.AuditPreStartDto;
import com.shouqianba.workflow.service.AuditService;
import com.wosai.data.crow.api.model.query.SingleTagEntityRecord;
import com.wosai.data.crow.api.service.OnlineQueryService;
import com.wosai.databus.event.bank.Pair;
import com.wosai.market.awesome.message.dto.LicenceDeliveryInfo;
import com.wosai.market.awesome.message.enums.BindStatusEnum;
import com.wosai.market.awesome.message.enums.NoticeType;
import com.wosai.market.awesome.message.service.LicenceMessageRpcService;
import com.wosai.market.data.events.api.DataEventsService;
import com.wosai.market.merchant.api.StoreRemoteService;
import com.wosai.market.merchant.dto.PageResponse;
import com.wosai.market.merchant.dto.merchant.request.StoreSaveRequest;
import com.wosai.sales.core.bean.QueryEsUserReq;
import com.wosai.sales.core.bean.QueryUserResponse;
import com.wosai.sales.core.common.PageListResult;
import com.wosai.sales.core.service.*;
import com.wosai.sales.profit.domain.ProfitDirectSign;
import com.wosai.sales.profit.service.ProfitDirectSignService;
import com.wosai.smart.goods.common.utils.LogUtils;
import com.wosai.smartbiz.Constants;
import com.wosai.smartbiz.apollo.ApolloConfigHelper;
import com.wosai.smartbiz.apollo.pojo.ApolloConfig;
import com.wosai.smartbiz.base.enums.SceneEnum;
import com.wosai.smartbiz.base.enums.YesNoEnum;
import com.wosai.smartbiz.base.exceptions.BusinessException;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.base.service.LockService;
import com.wosai.smartbiz.base.utils.CellphoneCheckUtils;
import com.wosai.smartbiz.base.utils.DateUtil;
import com.wosai.smartbiz.config.TagsConfig;
import com.wosai.smartbiz.gds.dto.SqbCashierUnbindCheckResult;
import com.wosai.smartbiz.gds.dto.licence.*;
import com.wosai.smartbiz.gds.enums.*;
import com.wosai.smartbiz.gds.request.*;
import com.wosai.smartbiz.gds.service.CashierLicenceService;
import com.wosai.smartbiz.goods.constants.GdsConstants;
import com.wosai.smartbiz.goods.direct.*;
import com.wosai.smartbiz.goods.domain.*;
import com.wosai.smartbiz.goods.util.CommonUtil;
import com.wosai.smartbiz.uc.manager.LicenceManagerService;
import com.wosai.smartbiz.uc.manager.MerchantV2Service;
import com.wosai.smartbiz.uc.manager.StoreDeviceActiveService;
import com.wosai.smartbiz.uc.manager.StoreV2Service;
import com.wosai.smartbiz.uc.utils.UcRedisUtil;
import com.wosai.smartbiz.uc.utils.common.DateUtils;
import com.wosai.smartbiz.uc.utils.converter.ConverterUtils;
import com.wosai.smartbiz.user.user.vo.CashierStoreVO;
import com.wosai.smartbiz.user.user.vo.MerchantV2VO;
import com.wosai.smartbiz.user.user.vo.StoreV2VO;
import com.wosai.smartbiz.utils.LicenceUtil;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.StoreService;
import entity.request.QueryCustomerRelationRequestDTO;
import entity.response.QueryCustomerRelationResponseDTO;
import facade.ICustomerRelationFacade;
import lombok.extern.slf4j.Slf4j;
import model.businessprivilege.response.BusinessPrivilegeResponseEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.wosai.smartbiz.gds.enums.DeviceTypeEnum.RETAIL_CASHIER;
import static net.logstash.logback.argument.StructuredArguments.keyValue;

/**
 * @author: redhat
 */
@Slf4j
@Service
public class CashierLicenceServiceImpl implements CashierLicenceService {
    @Value("${crm.customer-relation.cashier-keeper-relation-code}")
    private String cashierKeeperRelationCode;

    @Value("${crm.store-cashier-active.jump-url}")
    private String storeCashierActiveJumpUrl;
    @Value("${crm.store-cashier-unbind.jump-url}")
    private String storeCashierUnbindJumpUrl;

    @Autowired
    private LicenceMessageRpcService licenceMessageRpcService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private UserService salesUserService;
    @Autowired
    private PositionService salesPositionService;
    @Autowired
    private EsUserService salesEsUserService;
    @Autowired
    private UserOrganizationService salesUserOrganizationService;
    @Autowired
    private ProfitDirectSignService profitDirectSignService;

    @Autowired
    private LicenceOrganizationDAO licenceOrganizationDAO;
    @Autowired
    private LicenceUserDAO licenceUserDAO;
    @Autowired
    private LicenceBatchInfoDAO licenceBatchInfoDAO;
    @Autowired
    private LicenceInfoDAO licenceInfoDAO;
    @Autowired
    private LicenceClientInfoDAO licenceClientInfoDAO;
    @Autowired
    private LicenceDistributionDAO licenceDistributionDAO;
    @Autowired
    private LicenceDistributionRecordDAO licenceDistributionRecordDAO;
    @Autowired
    private LicenceOperationLogDAO licenceOperationLogDAO;

    @Autowired
    private StoreDeviceActiveService storeDeviceActiveService;
    @Autowired
    private StoreV2Service storeV2Service;
    @Autowired
    private MerchantV2Service merchantV2Service;

    @Autowired
    private ICustomerRelationFacade customerRelationFacade;
    @Autowired
    private AuditService spWorkflowAuditService;
    @Autowired
    private ApolloConfigHelper apolloConfigHelper;
    @Autowired
    private UcRedisUtil redisUtil;
    @Autowired
    private LockService lockService;
    @Autowired
    private StoreService storeService;
    @Autowired
    private UserService userService;
    @Autowired
    private ICustomerRelationFacade iCustomerRelationFacade;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private StoreDeviceActiveDAO storeDeviceActiveDAO;
    @Autowired
    private OnlineQueryService onlineQueryService;
    @Autowired
    private TagsConfig tagsConfig;
    @Autowired
    private DataEventsService dataEventsService;
    @Autowired
    private StoreRemoteService storeRemoteService;
    @Autowired
    private LicenceManagerService licenceManagerService;
    @Resource(name = "syncDataAsyncExecutor")
    private ThreadPoolTaskExecutor syncDataAsyncExecutor;

    private static final String whitelistRemark = "白名单：%s,%s,%s,%s";
    private static final String activateRemark = "激活：%s,%s,%s,%s";

    @Override
    public boolean syncCrmOrganization(String organizationId) {
        if(StringUtils.isEmpty(organizationId)){
            return false;
        }
        LicenceOrganizationDO organizationDO = licenceOrganizationDAO.getOrganizationById(organizationId);
        if(null == organizationDO){
            organizationDO = getOriginalOrganizationInfo(organizationId);
            if(null == organizationDO){
                return false;
            }
            organizationDO.setOperationTime(System.currentTimeMillis());
            licenceOrganizationDAO.insert(organizationDO);
        }
        return true;
    }

    @Override
    public boolean syncCrmUser(String userId) {
        return false;
    }

    @Override
    public boolean licenceBatchInfoExists(LicenceRequest request) {
        if(StringUtils.isBlank(request.getOrganizationId()) || StringUtils.isBlank(request.getBizOrderId())){
            return false;
        }
        List<LicenceBatchInfoDO> batchInfoDOList = licenceBatchInfoDAO.findBatchInfoListByBizOrderId(request.getOrganizationId(), request.getUserId(), request.getBizOrderId());
        return (CollectionUtils.isNotEmpty(batchInfoDOList));
    }

    @Override
    public boolean updateOrganizationInfo(LicenceOrganizationDO organizationDO) {
        LicenceOrganizationDO updateOrganizationDO = licenceOrganizationDAO.getOrganizationById(organizationDO.getId());
        if(null == updateOrganizationDO){
            return true;
        }
        updateOrganizationDO.setCode(organizationDO.getCode());
        updateOrganizationDO.setName(organizationDO.getName());
        updateOrganizationDO.setCodePath(organizationDO.getCodePath());
        updateOrganizationDO.setNamePath(organizationDO.getNamePath());
        updateOrganizationDO.setLevel(organizationDO.getLevel());
        updateOrganizationDO.setParentId(organizationDO.getParentId());
        return licenceOrganizationDAO.update(updateOrganizationDO);
    }

    @Override
    public boolean updateUser(LicenceUserDO licenceUserDO) {
        LicenceUserDO updateUserDO = licenceUserDAO.getLicenceUserByUserId(licenceUserDO.getUserId());
        if(null == updateUserDO){
            return true;
        }
        updateUserDO.setUserCode(licenceUserDO.getUserCode());
        updateUserDO.setUserName(licenceUserDO.getUserName());
        updateUserDO.setCellphone(licenceUserDO.getCellphone());
        updateUserDO.setStatus(licenceUserDO.getStatus());
        return licenceUserDAO.update(updateUserDO);
    }

    @Override
    public List<OrganizationDTO> organizationList(LicenceUserRequest request) {
        List<LicenceOrganizationDO> organizationDOList = licenceOrganizationDAO.organizationList(request);
        return ConverterUtils.convertToLicenceOrganizationDTO(organizationDOList);
    }

    @Override
    public List<LicenceUserSimpleDTO> userList(LicenceUserRequest request) {
        if(StringUtils.isNotBlank(request.getOrganizationId())){
            QueryEsUserReq esUserReq = QueryEsUserReq.builder()
                    .page(1)
                    .pageSize(100)
                    .minOrgLevel(Constants.CrmConstants.ORGANIZATION_LEVEL_4)
                    .status(1)
                    .build();
            esUserReq.setOrganizationId(request.getOrganizationId());

            if(StringUtils.isNotBlank(request.getCellphone())){
                esUserReq.setCellphoneLike(request.getCellphone());
            }else {
                esUserReq.setLinkmanLike(request.getUserName());
            }
            PageListResult<QueryUserResponse> esUserPageListResult = salesEsUserService.findEsUser(esUserReq);
            if(CollectionUtils.isEmpty(esUserPageListResult.getRecords())) {
                return Lists.newArrayList();
            }
            return esUserPageListResult.getRecords().stream()
                    .map(esUser -> {
                        LicenceUserSimpleDTO userSimpleDTO = LicenceUserSimpleDTO.builder()
                                .userId(esUser.getId())
                                .userCode(esUser.getCode())
                                .userName(esUser.getLinkman())
                                .cellphone(esUser.getCellphone())
                                .build();
                        return userSimpleDTO;
                    })
                    .collect(Collectors.toList());
        }else {
            List<LicenceUserDO> userDOList = licenceUserDAO.userList(request);
            return ConverterUtils.convertToLicenceUserDTO(userDOList);
        }
    }

    @Override
    public PageResponse<OrganizationSummaryDTO> organizationSummaryList(LicenceRequest request) {
        if(null == request.getPage()){
            request.setPage(1);
        }
        if(null == request.getPageSize()){
            request.setPageSize(20);
        }
        Integer totalCount = licenceOrganizationDAO.countOrganizationSummaryList(request);
        if(null == totalCount || totalCount == 0){
            return new PageResponse(request.getPage(), request.getPageSize(), 0, Lists.newArrayList());
        }
        int startRow = (request.getPage() - 1) * request.getPageSize();
        request.setStartRow(startRow);

        List<OrganizationSummaryDTO> organizationSummaryList = licenceOrganizationDAO.organizationSummaryList(request);
        if(CollectionUtils.isEmpty(organizationSummaryList)){
            return new PageResponse(request.getPage(), request.getPageSize(), totalCount, Lists.newArrayList());
        }

        List<String> organizationIds = organizationSummaryList.stream().map(OrganizationSummaryDTO::getOrganizationId).collect(Collectors.toList());

        List<LicenceSummaryDTO> licenceSummaryList = licenceInfoDAO.organizationSummaryList(organizationIds, request.getBrand(), request.getProductType());
        List<LicenceDistributionSummaryDTO> distOutSummaryDTOList = licenceDistributionDAO.distOutQuantitySummaryList(organizationIds, request.getBrand(), request.getProductType());
        Map<String, LicenceSummaryDTO> licenceSummaryMap = new HashMap<>();
        Map<String, LicenceDistributionSummaryDTO> distOutSummaryMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(licenceSummaryList)){
            licenceSummaryMap = licenceSummaryList.stream().collect(Collectors.toMap(LicenceSummaryDTO::getOrganizationId, Function.identity()));
        }
        if(CollectionUtils.isNotEmpty(distOutSummaryDTOList)){
            distOutSummaryMap = distOutSummaryDTOList.stream().collect(Collectors.toMap(LicenceDistributionSummaryDTO::getFromOrganizationId, Function.identity()));
        }
        for(OrganizationSummaryDTO organizationSummary : organizationSummaryList){
            LicenceSummaryDTO licenceSummaryDTO = MapUtils.getObject(licenceSummaryMap, organizationSummary.getOrganizationId());
            LicenceDistributionSummaryDTO distOutSummaryDTO =  MapUtils.getObject(distOutSummaryMap, organizationSummary.getOrganizationId());
            organizationSummary.setAvailableQuantity((null != licenceSummaryDTO) ? licenceSummaryDTO.getAvailableQuantity() : 0);
            organizationSummary.setActivatedQuantity((null != licenceSummaryDTO) ? licenceSummaryDTO.getActivatedQuantity() : 0);
            organizationSummary.setDistOutQuantity((null != distOutSummaryDTO) ? distOutSummaryDTO.getQuantity() : 0);
        }

        return new PageResponse(request.getPage(), request.getPageSize(), totalCount, organizationSummaryList);
    }

    @Override
    public AvailableQuantitySummaryDTO availableQuantityDetail(LicenceRequest request) {
        if(StringUtils.isBlank(request.getOrganizationId())){
            throw new ParamException("参数错误, 分配组织ID不能为空");
        }
        LicenceOrganizationDO organizationDO = licenceOrganizationDAO.getOrganizationById(request.getOrganizationId());
        if(null == organizationDO){
            throw new BusinessException("组织信息不存在");
        }
        LicenceSummaryDTO licenceSummaryDTO = licenceInfoDAO.organizationSummary(organizationDO.getId(), request.getBrand(), request.getProductType());

        AvailableQuantitySummaryDTO summaryDTO = new AvailableQuantitySummaryDTO();
        summaryDTO.setAvailableQuantity((null != licenceSummaryDTO) ? licenceSummaryDTO.getAvailableQuantity() : 0);

        List<AvailableQuantityDTO> quantityDTOList = licenceInfoDAO.availableQuantitySummary(organizationDO.getId(), request.getBrand(), request.getProductType());
        summaryDTO.setList(quantityDTOList);
        return summaryDTO;
    }

    @Override
    public PageResponse<LicenceDistributionSummaryDTO> distributionList(LicenceRequest request) {
        if(null == request.getPage()){
            request.setPage(1);
        }
        if(null == request.getPageSize()){
            request.setPageSize(20);
        }
        Integer totalCount = licenceDistributionDAO.countDistributionSummaryList(request);
        if(null == totalCount || totalCount == 0){
            return new PageResponse(request.getPage(), request.getPageSize(), 0, Lists.newArrayList());
        }
        int startRow = (request.getPage() - 1) * request.getPageSize();
        request.setStartRow(startRow);

        List<LicenceDistributionSummaryDTO> distributionSummaryList = licenceDistributionDAO.distributionSummaryList(request);
        if(CollectionUtils.isEmpty(distributionSummaryList)){
            return new PageResponse(request.getPage(), request.getPageSize(), totalCount, Lists.newArrayList());
        }

        List<Long> fromDistributionIds = distributionSummaryList.stream().filter(item -> item.getFromType() == LicenceTypeEnum.DISTRIBUTION).map(LicenceDistributionSummaryDTO::getFromDistributionId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(fromDistributionIds)){
            List<LicenceDistributionSummaryDTO> fromDistributionSummaryList = licenceDistributionDAO.distributionSummaryByIds(fromDistributionIds);
            Map<Long, LicenceDistributionSummaryDTO> fromDistributionSummaryMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(fromDistributionSummaryList)){
                fromDistributionSummaryMap = fromDistributionSummaryList.stream().collect(Collectors.toMap(LicenceDistributionSummaryDTO::getId, Function.identity()));
            }
            for(LicenceDistributionSummaryDTO distributionSummary : distributionSummaryList){
                if(distributionSummary.getFromType() == LicenceTypeEnum.DISTRIBUTION && null != distributionSummary.getFromDistributionId()){
                    LicenceDistributionSummaryDTO fromDistributionSummary = MapUtils.getObject(fromDistributionSummaryMap, distributionSummary.getFromDistributionId());
                    if(null != fromDistributionSummary){
                        distributionSummary.setFromName(fromDistributionSummary.getOrganizationNamePath());
                    }
                }
            }
        }
        return new PageResponse(request.getPage(), request.getPageSize(), totalCount, distributionSummaryList);
    }

    @Override
    public LicenceDistributionSummaryDTO distributionDetail(LicenceDistributionRequest request) {
        if(null == request.getDistributionId()){
            throw new ParamException("参数错误,许可证分配ID不能为空");
        }
        LicenceDistributionDO distributionDO = licenceDistributionDAO.getDistributionById(request.getDistributionId());
        if(null == distributionDO){
            throw new BusinessException("分配信息不存在");
        }
        LicenceOrganizationDO organizationDO = licenceOrganizationDAO.getOrganizationById(distributionDO.getOrganizationId());
        if(null == organizationDO){
            throw new BusinessException("组织信息不存在");
        }
        LicenceUserDO licenceUserDO = licenceUserDAO.getLicenceUserByUserId(distributionDO.getUserId());
        if(null == licenceUserDO){
            throw new BusinessException("用户信息不存在");
        }

        LicenceDistributionSummaryDTO distributionSummaryDTO = LicenceDistributionSummaryDTO.builder()
                .id(distributionDO.getId())
                .organizationId(distributionDO.getOrganizationId())
                .organizationName(organizationDO.getName())
                .organizationNamePath(organizationDO.getNamePath())
                .userId(distributionDO.getUserId())
                .userName(licenceUserDO.getUserName())
                .cellphone(licenceUserDO.getCellphone())
                .batchNo(distributionDO.getBatchNo())
                .fromType(distributionDO.getFromType())
                .quantity(distributionDO.getQuantity())
                .batchNo(distributionDO.getBatchNo())
                .fromDistributionId(distributionDO.getFromDistributionId())
                .fromName(distributionDO.getFromName())
                .operationTime(distributionDO.getOperationTime())
                .operator(distributionDO.getFromName())
                .remark(distributionDO.getRemark())
                .build();
        return distributionSummaryDTO;
    }

    @Override
    public PageResponse<LicenceOperationLogDTO> operationLogList(LicenceLogRequest request) {
        if(null == request.getPage()){
            request.setPage(1);
        }
        if(null == request.getPageSize()){
            request.setPageSize(20);
        }
        Integer totalCount = licenceOperationLogDAO.countOperationLogPageList(request);
        if(null == totalCount || totalCount == 0){
            return new PageResponse(request.getPage(), request.getPageSize(), 0, Lists.newArrayList());
        }
        int startRow = (request.getPage() - 1) * request.getPageSize();
        request.setStartRow(startRow);

        List<LicenceOperationLogDTO> operationLogDTOList = licenceOperationLogDAO.operationLogPageList(request);
        if(CollectionUtils.isEmpty(operationLogDTOList)){
            return new PageResponse(request.getPage(), request.getPageSize(), totalCount, Lists.newArrayList());
        }

        //获取直营商户的许可证流转记录中的商户ID(organizationId)
        operationLogDTOList.forEach(operationLogDTO -> {
            if(StringUtils.isNotBlank(operationLogDTO.getOrganizationId()) && StringUtils.isBlank(operationLogDTO.getUserId())){
                MerchantV2VO merchantV2VO = merchantV2Service.getMerchantByMerchantId(operationLogDTO.getOrganizationId());
                operationLogDTO.setOrganizationName((null != merchantV2VO) ? merchantV2VO.getMerchantName() : null);
                operationLogDTO.setOrganizationNamePath((null != merchantV2VO) ? merchantV2VO.getMerchantName() : null);
            }
        });
        return new PageResponse(request.getPage(), request.getPageSize(), totalCount, operationLogDTOList);
    }

    @Override
    public List<LicenceSummaryDTO> summaryVersionLicenceCount(LicenceRequest request) {
        return licenceInfoDAO.summaryVersionLicenceCount(request.getOrganizationId(), request.getUserId(), request.getBrand(), request.getProductType());
    }

    @Override
    public boolean giftLicence(LicenceGiftRequest request) {
        String lockKey = redisUtil.generateKey(com.wosai.smartbiz.uc.constants.Constants.RedisConstants.SPA_GIFT_LICENCE_CONFIG_PREFIX, request.getOperatorId());
        return lockService.performWithLock(GdsConstants.GDS_MODULE, lockKey,100L,()->{
            Long nowTime = System.currentTimeMillis();
            LicenceDistributionRequest distRequest = new LicenceDistributionRequest();
            distRequest.setUserType(LicenceUserTypeEnum.AGENT);
            distRequest.setType(LicenceTypeEnum.GIFT);
            distRequest.setPermanent(YesNoEnum.N);
            distRequest.setExpireTime(DateUtils.addYear(new Date(nowTime), 1).getTime());
            distRequest.setRemark("赠送");
            distRequest.setOperationTime(nowTime);
            distRequest.setOperatorId(request.getOperatorId());
            distRequest.setOperator(request.getOperator());
            distRequest.setFromName(request.getOperator());

            if(CollectionUtils.isEmpty(request.getList())){
                throw new ParamException("参数错误，赠送信息不能为空");
            }
            request.getList().forEach(licenceGiftData -> {
                if(StringUtils.isBlank(licenceGiftData.getCellphone())){
                    throw new ParamException("参数错误，用户手机号不能为空");
                }
                if(StringUtils.isBlank(licenceGiftData.getOrganizationCode())){
                    throw new ParamException("参数错误，组织编码不能为空");
                }
                if(null == licenceGiftData.getQuantity() || licenceGiftData.getQuantity() <= 0){
                    throw new ParamException("参数错误，分配数量必须大于0");
                }
                if(StringUtils.isNotBlank(licenceGiftData.getRemark()) && licenceGiftData.getRemark().length() > 256){
                    throw new ParamException("参数错误，" + licenceGiftData.getCellphone() + "备注不能超过256个字符");
                }

                LicenceOrganizationDO organizationDO = getOriginalOrganizationInfoByCode(licenceGiftData.getOrganizationCode());
                if(null == organizationDO){
                    throw new ParamException("参数错误，" + licenceGiftData.getOrganizationCode() + "组织不存在");
                }
                if(organizationDO.getLevel() != Constants.CrmConstants.ORGANIZATION_LEVEL_4){
                    throw new BusinessException("参数错误，" + licenceGiftData.getOrganizationCode() + "只允许给4级组织赠送");
                }
                LicenceUserDO licenceUserDO = getOriginalCrmUserInfoByCellphone(licenceGiftData.getCellphone());
                if(null == licenceUserDO){
                    throw new ParamException("参数错误，" + licenceGiftData.getCellphone() + "用户不存在");
                }
                boolean matchRes = userOrganizationMatch(licenceUserDO.getUserId(), organizationDO.getId());
                if(!matchRes){
                    throw new ParamException("参数错误，" + licenceGiftData.getCellphone() + "用户和组织" + licenceGiftData.getOrganizationCode() + "不匹配");
                }

                LicenceDistributionRequest.DistributionData distributionData = new LicenceDistributionRequest.DistributionData();
                distributionData.setOrganizationId(organizationDO.getId());
                distributionData.setUserId(licenceUserDO.getUserId());
                distributionData.setVersionType(LicenceVersionTypeEnum.LIFE);
                distributionData.setQuantity(licenceGiftData.getQuantity());
                distributionData.setRemark(licenceGiftData.getRemark());

                distRequest.setDistributionData(Lists.newArrayList(distributionData));
                //赠送许可证
                payOrGiftDistribution(distRequest);
            });
            return true;
        });
    }

    @Override
    public boolean giftMerchantLicence(LicenceGiftRequest request) {
        LicenceDistributionRequest distRequest = new LicenceDistributionRequest();
        distRequest.setUserType(LicenceUserTypeEnum.MERCHANT);
        distRequest.setType(LicenceTypeEnum.GIFT);
        distRequest.setPermanent(YesNoEnum.N);
        distRequest.setExpireTime(DateUtils.addYear(new Date(System.currentTimeMillis()), 1).getTime());
        distRequest.setRemark("赠送");
        distRequest.setOperationTime(System.currentTimeMillis());
        distRequest.setOperatorId(request.getOperatorId());
        distRequest.setOperator(request.getOperator());
        distRequest.setFromName(request.getOperator());
        if(CollectionUtils.isEmpty(request.getList())){
            throw new ParamException("参数错误，赠送信息不能为空");
        }

        request.getList().forEach(licenceGiftData -> {
                    LicenceDistributionRequest.DistributionData distributionData = new LicenceDistributionRequest.DistributionData();
                    Map merchant = merchantService.getMerchantBySn(licenceGiftData.getOrganizationCode());
                    distributionData.setOrganizationId(MapUtils.getString(merchant, "id"));
//                    distributionData.setUserId(request.getOperatorId());
                    distributionData.setVersionType(LicenceVersionTypeEnum.LIFE);
                    distributionData.setProductType(licenceGiftData.getProductType());
                    distributionData.setQuantity(licenceGiftData.getQuantity());
                    distributionData.setRemark(licenceGiftData.getRemark());
                    distRequest.setDistributionData(Lists.newArrayList(distributionData));
                    payOrGiftDistribution(distRequest);
        });
        return true;
    }

    @Override
    public boolean userOrganizationMatch(String userId, String organizationId){
        Map userOrganizationMap = new HashMap();
        userOrganizationMap.put("user_id", userId);
        List<Map> userOrganizationList = salesUserOrganizationService.findUserOrganizationById(userOrganizationMap);
        if(CollectionUtils.isEmpty(userOrganizationList)){
            return false;
        }
        for(Map map : userOrganizationList){
            String matchOrganizationId = MapUtils.getString(map, "organization_id", null);
            if(Objects.equals(organizationId, matchOrganizationId)){
                return true;
            }
        }
        return false;
    }


    @Override
    public boolean updateBatchInfoStatus(LicenceRequest request) {
        if(StringUtils.isBlank(request.getBatchNo())){
            throw new ParamException("参数错误,批次号不能为空");
        }
        if(null == request.getStatus()){
            throw new ParamException("参数错误,批次状态不能为空");
        }
        LicenceBatchInfoDO batchInfoDO = licenceBatchInfoDAO.getBatchInfoByBatchNo(request.getBatchNo());
        if(null == batchInfoDO){
            throw new BusinessException("许可证批次信息不存在");
        }

        //修改许可证状态
        licenceInfoDAO.updateStatusByBatchNo(batchInfoDO.getBatchNo(), request.getStatus());

        //修改许可证批次状态
        batchInfoDO.setStatus(request.getStatus());
        int row = licenceBatchInfoDAO.updateStatus(batchInfoDO);
        return true;
    }

    @Override
    public boolean agentPayDistribution(LicenceDistributionRequest request) {
        String lockKey = redisUtil.generateKey(com.wosai.smartbiz.uc.constants.Constants.RedisConstants.AGENT_PAY_LICENCE_CONFIG_PREFIX, request.getBizOrderId());
        return lockService.performWithLock(GdsConstants.GDS_MODULE, lockKey,100L,()->{
            if(null == request){
                throw new ParamException("参数错误,参数不能为空");
            }
            if(CollectionUtils.isEmpty(request.getDistributionData())){
                throw new ParamException("参数错误,分配数据不能为空");
            }
            if(StringUtils.isBlank(request.getRemark())){
                throw new ParamException("参数错误,备注不能为空");
            }
            if(request.getRemark().length() > 256){
                throw new ParamException("参数错误,备注不能超过256个字符");
            }
            for(LicenceDistributionRequest.DistributionData distributionData : request.getDistributionData()) {
                if(StringUtils.isBlank(distributionData.getUserId())){
                    throw new ParamException("参数错误,所选分配用户ID不能为空");
                }
                if(StringUtils.isBlank(distributionData.getOrganizationId())){
                    throw new ParamException("参数错误,分配组织ID不能为空");
                }
                LicenceUserDO licenceUserDO = getOriginalCrmUserInfo(distributionData.getUserId());
                if(null == licenceUserDO){
                    throw new ParamException("参数错误,所选分配用户不存在");
                }
                LicenceOrganizationDO organizationDO = getOriginalOrganizationInfo(distributionData.getOrganizationId());
                if(null == organizationDO){
                    throw new ParamException("参数错误,所选分配用户组织不存在");
                }
                boolean matchRes = userOrganizationMatch(licenceUserDO.getUserId(), organizationDO.getId());
                if(!matchRes){
                    throw new ParamException("参数错误,所选分配用户用户和组织不匹配");
                }
                if(organizationDO.getLevel() != Constants.CrmConstants.ORGANIZATION_LEVEL_4){
                    throw new ParamException("参数错误,所选组织只能是4级组织");
                }
                if (LicenceUtil.isDirectOrganization(organizationDO.getCodePath())) {
                    throw new ParamException("参数错误,所选组织不能是直营");
                }
                if(null == distributionData.getQuantity() || distributionData.getQuantity() <= 0){
                    throw new ParamException("参数错误, 分配数量必须大于0");
                }
                if(distributionData.getQuantity() > 1000){
                    throw new ParamException("参数错误, 一次最多分配1000个");
                }
            }

            //验证订单是否已经处理
            LicenceRequest checkExistsRequest = new LicenceRequest();
            checkExistsRequest.setOrganizationId(request.getOrganizationId());
            checkExistsRequest.setUserId(request.getUserId());
            checkExistsRequest.setBizOrderId(request.getBizOrderId());
            boolean batchInfoExists = licenceBatchInfoExists(checkExistsRequest);
            if(batchInfoExists){
                log.info("agentPayDistribution process licence bizOrderId exists, organizationId:{},userId:{},bizOrderId:{}", request.getOrganizationId(), request.getUserId(), request.getBizOrderId());
                throw new BusinessException("许可证数据数据已经存在,无需重复处理");
            }

            request.setUserType(LicenceUserTypeEnum.AGENT);
            request.setType(LicenceTypeEnum.PAY);
            return payOrGiftDistribution(request);
        });
    }

    @Override
    public boolean merchantPayDistribution(LicenceDistributionRequest request) {
        String lockKey = redisUtil.generateKey(com.wosai.smartbiz.uc.constants.Constants.RedisConstants.MERCHANT_PAY_LICENCE_CONFIG_PREFIX, request.getBizOrderId());
        return lockService.performWithLock(GdsConstants.GDS_MODULE, lockKey,100L,()->{
            if(null == request){
                throw new ParamException("参数错误,参数不能为空");
            }
            if(CollectionUtils.isEmpty(request.getDistributionData())){
                throw new ParamException("参数错误,分配数据不能为空");
            }
            if(StringUtils.isBlank(request.getRemark())){
                throw new ParamException("参数错误,备注不能为空");
            }
            if(request.getRemark().length() > 256){
                throw new ParamException("参数错误,备注不能超过256个字符");
            }
            for(LicenceDistributionRequest.DistributionData distributionData : request.getDistributionData()) {
                if(StringUtils.isBlank(distributionData.getOrganizationId())){
                    throw new ParamException("参数错误,分配组织ID不能为空");
                }
                MerchantV2VO merchantV2VO = merchantV2Service.getMerchantByMerchantId(distributionData.getOrganizationId());
                if(null == merchantV2VO){
                    throw new ParamException("参数错误,所选分配商户不存在");
                }
                if(null == distributionData.getQuantity() || distributionData.getQuantity() <= 0){
                    throw new ParamException("参数错误, 分配数量必须大于0");
                }
                if(distributionData.getQuantity() > 1000){
                    throw new ParamException("参数错误, 一次最多分配1000个");
                }
            }

            //验证订单是否已经处理
            LicenceRequest checkExistsRequest = new LicenceRequest();
            checkExistsRequest.setOrganizationId(request.getOrganizationId());
            checkExistsRequest.setUserId(request.getUserId());
            checkExistsRequest.setBizOrderId(request.getBizOrderId());
            boolean batchInfoExists = licenceBatchInfoExists(checkExistsRequest);
            if(batchInfoExists){
                log.info("merchantPayDistribution process licence bizOrderId exists, organizationId:{},userId:{},bizOrderId:{}", request.getOrganizationId(), request.getUserId(), request.getBizOrderId());
                throw new BusinessException("许可证数据数据已经存在,无需重复处理");
            }

            request.setUserType(LicenceUserTypeEnum.MERCHANT);
            request.setType(LicenceTypeEnum.PAY);
            return payOrGiftDistribution(request);
        });
    }

    @Override
    public boolean updateOrganizationIdByUserId(String userId, String sourceOrgId, String targetOrgId) {
        if(StringUtils.isBlank(userId)){
            throw new ParamException("参数错误,用户ID不能为空");
        }
        if(StringUtils.isBlank(sourceOrgId)){
            throw new ParamException("参数错误,原始组织ID不能为空");
        }
        if(StringUtils.isBlank(targetOrgId)){
            throw new ParamException("参数错误,目标组织ID不能为空");
        }

        LicenceDistributionRequest.DistributionData distributionData = new LicenceDistributionRequest.DistributionData();
        distributionData.setUserId(userId);
        distributionData.setOrganizationId(targetOrgId);

        //保存目标用户、目标组织信息
        getOriginalCrmUserInfoAndSave(distributionData);


        //保存流转记录
        batchSaveLicenceOperationLog(userId, sourceOrgId, userId, targetOrgId, "用户组织变更");

        //更新许可证批次组织信息
        licenceBatchInfoDAO.updateOrganizationIdByUserId(userId, sourceOrgId, targetOrgId);
        //更新许可证组织信息
        licenceInfoDAO.updateOrganizationIdByUserId(userId, sourceOrgId, targetOrgId);
        //更新许可证分配组织信息
        licenceDistributionDAO.updateOrganizationIdByUserId(userId, sourceOrgId, targetOrgId);

        return true;
    }

    @Override
    public boolean accountLicenceTransfer(String merchantId, String targetMerchantId, List<Pair> storeList) {
        if(StringUtils.isBlank(merchantId)){
            throw new ParamException("参数错误,商户ID不能为空");
        }
        if(StringUtils.isBlank(targetMerchantId)){
            throw new ParamException("参数错误,目标商户ID不能为空");
        }
        if(CollectionUtils.isEmpty(storeList)){
            throw new ParamException("参数错误,门店列表不能为空");
        }
        MerchantV2VO oldMmerchantV2VO = merchantV2Service.getMerchantByMerchantId(merchantId);
        MerchantV2VO targetMmerchantV2VO = merchantV2Service.getMerchantByMerchantId(targetMerchantId);
        if(null == targetMmerchantV2VO || null == oldMmerchantV2VO) {
            throw new BusinessException("商户不存在");
        }
        // 先迁移商户的剩余额度, 已经激活的额度会先取消激活
        licenceManagerService.moveMerchantRemainingLicence(merchantId, targetMerchantId, null);

        // 如果是用的服务商的额度激活的，需要迁移门店的激活信息到新的门店
        for(Pair pair : storeList){
            try{
                licenceManagerService.moveActiveInfo(pair.getSource(), pair.getTarget(), null);
            } catch (Exception ex) {
                log.error("licenceManagerAccountLicenceTransfer error,merchantId:{},targetMerchantId:{},sourceStoreId:{},targetStoreId:{}", merchantId, targetMerchantId, pair.getSource(), pair.getTarget());
            }
        }
        return true;
    }

    @Override
    public boolean updateUserIdByOrganizationId(String organizationId, String sourceUserId, String targetUserId) {
        if(StringUtils.isBlank(organizationId)){
            throw new ParamException("参数错误,组织ID不能为空");
        }
        if(StringUtils.isBlank(sourceUserId)){
            throw new ParamException("参数错误,原始用户ID不能为空");
        }
        if(StringUtils.isBlank(targetUserId)){
            throw new ParamException("参数错误,目标用户ID不能为空");
        }

        LicenceDistributionRequest.DistributionData distributionData = new LicenceDistributionRequest.DistributionData();
        distributionData.setUserId(targetUserId);
        distributionData.setOrganizationId(organizationId);

        //保存目标用户、目标组织信息
        getOriginalCrmUserInfoAndSave(distributionData);

        //保存流转记录
        batchSaveLicenceOperationLog(sourceUserId, organizationId, targetUserId, organizationId, "用户离职重新分配许可证");

        //更新许可证批次用户信息
        licenceBatchInfoDAO.updateUserIdByOrganizationId(organizationId, sourceUserId, targetUserId);
        //更新许可证组织信息
        licenceInfoDAO.updateUserIdByOrganizationId(organizationId, sourceUserId, targetUserId);
        //更新许可证分配组织信息
        licenceDistributionDAO.updateUserIdByOrganizationId(organizationId, sourceUserId, targetUserId);


        return true;
    }

    @Override
    public PageResponse<LicenceUserDTO> distributionUserList(LicenceRequest request) {
        if(StringUtils.isBlank(request.getUserId())){
            throw new ParamException("参数错误,用户ID不能为空");
        }
        if(StringUtils.isBlank(request.getOrganizationId())){
            throw new ParamException("参数错误,用户ID不能为空");
        }
        if(StringUtils.isBlank(request.getOrganizationCodePath())){
            throw new ParamException("参数错误,用户路径不能为空");
        }
        if(StringUtils.isBlank(request.getQuery())){
            throw new ParamException("参数错误,查询条件不能为空");
        }
        if(null == request.getPage()){
            request.setPage(1);
        }
        if(null == request.getPageSize()){
            request.setPageSize(20);
        }
        String query = request.getQuery();
        long totalCount = 0L;
        List<LicenceUserDTO> resultUserList = new ArrayList<>();

        //判断是否是"一级服务商"用户，一级服务商用户允许分配到 "直代"、"总代"的"一级服务商"用户名下
        boolean specialUser = specialUser(request.getOrganizationCodePath(), request.getPositionId());
        if(specialUser) {
            //查询总代服务商下关联的直代服务商的"一级服务商"用户
            List<ProfitDirectSign> profitList = profitDirectSignService.findDirectSignListV2(request.getOrganizationId());
            if(CollectionUtils.isNotEmpty(profitList)){
                for(ProfitDirectSign profit : profitList){
                    if(Objects.equals(profit.getStatus(), 0)){
                        continue;
                    }
                    List<LicenceUserDTO> buyerUserList = getOrgBuyerUserList(profit.getAssociateOrganizationId(), query);
                    resultUserList.addAll(buyerUserList);
                    totalCount += buyerUserList.size();
                }
            }
            //查询直代所属总代服务商的"一级服务商"用户
            ProfitDirectSign profitDirectSign = profitDirectSignService.findDirectSign(request.getOrganizationId());
            if(null != profitDirectSign && Objects.equals(profitDirectSign.getStatus(), 1)){
                List<LicenceUserDTO> buyerUserList = getOrgBuyerUserList(profitDirectSign.getOrganizationId(), query);
                resultUserList.addAll(buyerUserList);
                totalCount += buyerUserList.size();
            }
        }

        //查询组织下的用户列表
        QueryEsUserReq esUserReq = QueryEsUserReq.builder()
                .page(request.getPage())
                .pageSize(request.getPageSize())
                .minOrgLevel(Constants.CrmConstants.ORGANIZATION_LEVEL_4)
                .status(1)
                .build();
        String fourLevelOrganizationCodePath = get4LevelOrganizationCodePath(request.getOrganizationCodePath());
        esUserReq.setOrgPathLeftLike(fourLevelOrganizationCodePath);

        if(CellphoneCheckUtils.isNumeric(query)){
            esUserReq.setCellphoneLike(query);
        }else {
            esUserReq.setLinkmanLike(query);
        }
        PageListResult<QueryUserResponse> esUserPageListResult = salesEsUserService.findEsUser(esUserReq);
        if(CollectionUtils.isEmpty(esUserPageListResult.getRecords())){
            return new PageResponse(request.getPage(), request.getPageSize(), totalCount, resultUserList);
        }

        totalCount += esUserPageListResult.getTotal();
        if(totalCount == 0L){
            return new PageResponse(request.getPage(), request.getPageSize(), totalCount, resultUserList);
        }
        List<LicenceUserDTO> licenceUserDTOList = convertToLicenceUserDTO(esUserPageListResult.getRecords());
        resultUserList.addAll(licenceUserDTOList);

        return new PageResponse(request.getPage(), request.getPageSize(), totalCount, resultUserList);
    }


    @Override
    public boolean crmDistribution(LicenceDistributionRequest request) {
        if(null == request){
            throw new ParamException("参数错误, 请求参数不能为空");
        }
        if(StringUtils.isBlank(request.getUserId())){
            throw new ParamException("参数错误, 分配用户ID不能为空");
        }
        if(StringUtils.isBlank(request.getOrganizationId())){
            throw new ParamException("参数错误, 分配组织ID不能为空");
        }
        if(null == request.getDistributionId()){
            throw new ParamException("参数错误, 分配ID不能为空");
        }
        if(CollectionUtils.isEmpty(request.getDistributionData())){
            throw new ParamException("参数错误, 分配明细不能为空");
        }
        String lockKey = redisUtil.generateKey(com.wosai.smartbiz.uc.constants.Constants.RedisConstants.CRM_DISTRIBUTION_LICENCE_CONFIG_PREFIX, String.valueOf(request.getDistributionId()));
        return lockService.performWithLock(GdsConstants.GDS_MODULE, lockKey,100L,()->{
            LicenceUserDO licenceUserDO = getOriginalCrmUserInfo(request.getUserId());
            if(null == licenceUserDO){
                throw new BusinessException("用户信息不存在");
            }
            LicenceOrganizationDO organizationDO = getOriginalOrganizationInfo(request.getOrganizationId());
            if(null == organizationDO){
                throw new BusinessException("组织信息不存在");
            }
            for(LicenceDistributionRequest.DistributionData distributionData : request.getDistributionData()){
                if(StringUtils.isBlank(distributionData.getUserId())){
                    throw new ParamException("参数错误, 分配用户ID不能为空");
                }
                if(Objects.equals(licenceUserDO.getUserId(), distributionData.getUserId())){
                    throw new ParamException("参数错误, 无法给自己进行分配");
                }
                if(null == distributionData.getQuantity() || distributionData.getQuantity() <= 0){
                    throw new ParamException("参数错误, 分配数量必须大于0");
                }
                LicenceUserDO targetLicenceUserDO = getOriginalCrmUserInfo(distributionData.getUserId());
                if(null == targetLicenceUserDO){
                    throw new ParamException("参数错误, 分配用户信息不存在");
                }
                LicenceOrganizationDO targetOrganizationDO = getOriginalOrganizationInfo(distributionData.getOrganizationId());
                if(null == targetOrganizationDO){
                    throw new ParamException("参数错误, 分配用户组织信息不存在");
                }
                if(targetOrganizationDO.getLevel() < Constants.CrmConstants.ORGANIZATION_LEVEL_4){
                    throw new BusinessException("参数错误, 不允许向4级以上组织分配");
                }
                if(!checkOrgRelation(organizationDO, targetOrganizationDO)){
                    throw new BusinessException("参数错误, 分配组织关系错误");
                }
            }

            LicenceDistributionDO distributionDO = licenceDistributionDAO.getDistributionById(request.getDistributionId());
            if(null == distributionDO){
                throw new BusinessException("分配信息不存在");
            }
            if(!Objects.equals(distributionDO.getUserId(), request.getUserId())){
                throw new ParamException("该分配信息不属于您，暂时无法进行分配");
            }
            List<LicenceDistributionRequest.DistributionData> distributionDataList = request.getDistributionData();
            String organizationId = distributionDO.getOrganizationId();
            String userId = distributionDO.getUserId();
            String batchNo = distributionDO.getBatchNo();
            String remark = request.getRemark();
            LicenceBrandEnum brand = distributionDO.getBrand();
            LicenceProductTypeEnum productType = distributionDO.getProductType();


            for(LicenceDistributionRequest.DistributionData distributionData : distributionDataList){
                distributionData.setBrand(brand);
                distributionData.setProductType(productType);
                Integer quantity = distributionData.getQuantity();
                LicenceUserDO targetLicenceUserDO = getOriginalCrmUserInfoAndSave(distributionData);
                if(null == targetLicenceUserDO){
                    throw new BusinessException("分配用户信息不存在,请重新选择");
                }
                String targetOrganizationId = distributionData.getOrganizationId();
                String targetUserId = distributionData.getUserId();

                int unassignedQuantity = licenceDistributionRecordDAO.getUnassignedQuantity(distributionDO.getId());
                if(unassignedQuantity < quantity){
                    throw new BusinessException("剩余可分配数量少于分配数量，请重新选择分配数量");
                }
                List<String> licenceNoList = licenceDistributionRecordDAO.findDistributionLicenceNoList(distributionDO.getId(), quantity);
                if(CollectionUtils.isEmpty(licenceNoList)){
                    throw new BusinessException("可分配许可证信息不存在");
                }

                request.setType(LicenceTypeEnum.DISTRIBUTION);
                request.setFromName(licenceUserDO.getUserName());
                request.setFromDistributionId(distributionDO.getId());
                request.setFromOrganizationId(distributionDO.getOrganizationId());

                //获取待分配的许可证列表
                List<LicenceInfoDO> licenceInfoDOList = licenceInfoDAO.findLicenceInfoList(organizationId, userId, batchNo, licenceNoList);
                if(CollectionUtils.isEmpty(licenceInfoDOList)){
                    throw new BusinessException("可分配许可证信息不存在");
                }
                List<String> updateLicenceNoList = licenceInfoDOList.stream().map(LicenceInfoDO::getLicenceNo).collect(Collectors.toList());

                LicenceDistributionDO payDistributionDO = getPayOrGiftDistributionInfo(targetOrganizationId, targetUserId, batchNo);
                YesNoEnum retrieve = YesNoEnum.N;
                if(null != payDistributionDO){
                    //修改原始购买时分配记录分配状态
                    licenceDistributionRecordDAO.updateAssigned(payDistributionDO.getId(), licenceNoList, YesNoEnum.N);
                    retrieve = YesNoEnum.Y;
                }
                //保存许可证分配记录信息
                Long distributionId = saveDistribution(targetOrganizationId, targetUserId, batchNo, distributionData, retrieve, remark, licenceInfoDOList, request);

                //修改许可证所属机构信息
                licenceInfoDAO.updateOrganizationId(organizationId, userId, batchNo, updateLicenceNoList, targetOrganizationId, targetUserId);
                //修改原始分配记录分配状态
                licenceDistributionRecordDAO.updateAssigned(distributionDO.getId(), licenceNoList, YesNoEnum.Y);

                //保存流转记录
                batchSaveLicenceOperationLog(userId, organizationId, targetUserId, targetOrganizationId, batchNo, quantity, distributionId, remark, brand, productType);
            }
            return true;
        });
    }

    @Override
    public PageResponse<LicenceDistributionDTO> crmNotUseDistributionList(LicenceRequest request) {
        if(StringUtils.isBlank(request.getUserId())){
            throw new ParamException("参数错误,用户ID不能为空");
        }
        if(null == request.getPage()){
            request.setPage(1);
        }
        if(null == request.getPageSize()){
            request.setPageSize(20);
        }
        if (null == request.getBrand()) {
            request.setBrand(LicenceBrandEnum.SQB);
        }
        if (request.getBrand() == LicenceBrandEnum.SQB && null == request.getProductType()) {
            request.setProductType(LicenceProductTypeEnum.RESTAURANT);
        }
        //标记是否是特殊用户
        request.setSpecialUser(specialUser(request.getOrganizationCodePath(), request.getPositionId()));

        Integer totalCount = licenceDistributionDAO.countNotUseDistributionPageList(request);
        if(null == totalCount || totalCount == 0){
            return new PageResponse(request.getPage(), request.getPageSize(), 0, Lists.newArrayList());
        }
        int startRow = (request.getPage() - 1) * request.getPageSize();
        request.setStartRow(startRow);

        List<LicenceDistributionDTO> distributionDTOList = licenceDistributionDAO.findNotUseDistributionPageList(request);
        if(CollectionUtils.isEmpty(distributionDTOList)){
            return new PageResponse(request.getPage(), request.getPageSize(), totalCount, Lists.newArrayList());
        }
        distributionDTOList.forEach(it -> {
            it.setProductName(LicenceUtil.getLicenceProductName(it.getBrand(), it.getProductType()));
            it.setFromName(LicenceUtil.getDistributionFromName(it));
        });
        return new PageResponse(request.getPage(), request.getPageSize(), totalCount, distributionDTOList);
    }

    @Override
    public PageResponse<LicenceDistributionDTO> crmCompletedDistributionList(LicenceRequest request) {
        if(StringUtils.isBlank(request.getUserId())){
            throw new ParamException("参数错误,用户ID不能为空");
        }
        if(null == request.getPage()){
            request.setPage(1);
        }
        if(null == request.getPageSize()){
            request.setPageSize(20);
        }
        if (null == request.getBrand()) {
            request.setBrand(LicenceBrandEnum.SQB);
        }
        if (request.getBrand() == LicenceBrandEnum.SQB && null == request.getProductType()) {
            request.setProductType(LicenceProductTypeEnum.RESTAURANT);
        }
        Integer totalCount = licenceDistributionDAO.countCompletedDistributionPageList(request);
        if(null == totalCount || totalCount == 0){
            return new PageResponse(request.getPage(), request.getPageSize(), 0, Lists.newArrayList());
        }
        int startRow = (request.getPage() - 1) * request.getPageSize();
        request.setStartRow(startRow);

        List<LicenceDistributionDTO> distributionDTOList = licenceDistributionDAO.findCompletedDistributionPageList(request);
        if(CollectionUtils.isEmpty(distributionDTOList)){
            return new PageResponse(request.getPage(), request.getPageSize(), totalCount, Lists.newArrayList());
        }
        distributionDTOList.forEach(it -> {
            it.setProductName(LicenceUtil.getLicenceProductName(it.getBrand(), it.getProductType()));
            it.setFromName(LicenceUtil.getDistributionFromName(it));
        });
        return new PageResponse(request.getPage(), request.getPageSize(), totalCount, distributionDTOList);
    }

    @Override
    public PageResponse<LicenceInfoDTO> crmActivatedStoreList(LicenceRequest request) {
        if(StringUtils.isBlank(request.getUserId())){
            throw new ParamException("参数错误,用户ID不能为空");
        }
        if(StringUtils.isBlank(request.getOrganizationCode())){
            throw new ParamException("参数错误,机构编号不能为空");
        }
        if(StringUtils.isBlank(request.getOrganizationCodePath())){
            throw new ParamException("参数错误,机构编号路径不能为空");
        }
        if(null == request.getPage()){
            request.setPage(1);
        }
        if(null == request.getPageSize()){
            request.setPageSize(20);
        }
        if (null == request.getBrand()) {
            request.setBrand(LicenceBrandEnum.SQB);
        }
        boolean isExternalBrand = LicenceBrandEnum.isExternalBrand(request.getBrand());
        //标记是否是特殊用户
        request.setSpecialUser(specialUser(request.getOrganizationCodePath(), request.getPositionId()));

        Integer totalCount = 0;
        if(isExternalBrand){
            totalCount = licenceInfoDAO.countExternalProductActivatedStorePageList(request);
        }else {
            totalCount = licenceInfoDAO.countActivatedStorePageList(request);
        }
        if(null == totalCount || totalCount == 0){
            return new PageResponse(request.getPage(), request.getPageSize(), 0, Lists.newArrayList());
        }
        int startRow = (request.getPage() - 1) * request.getPageSize();
        request.setStartRow(startRow);

        List<LicenceInfoDTO> licenceInfoDTOList;
        if(isExternalBrand){
            licenceInfoDTOList = licenceInfoDAO.findExternalProductActivatedStorePageList(request);
        }else {
            licenceInfoDTOList = licenceInfoDAO.findActivatedStorePageList(request);
        }
        if(CollectionUtils.isEmpty(licenceInfoDTOList)){
            return new PageResponse(request.getPage(), request.getPageSize(), totalCount, Lists.newArrayList());
        }

        if(CollectionUtils.isNotEmpty(licenceInfoDTOList)){
            licenceInfoDTOList.forEach(licenceInfoDTO -> {
                licenceInfoDTO.setProductName(LicenceUtil.getLicenceProductName(licenceInfoDTO.getBrand(), licenceInfoDTO.getProductType()));
                licenceInfoDTO.setActiveName(LicenceUtil.getActiveName(licenceInfoDTO.getBrand(), licenceInfoDTO.getActiveType()));
                try {
                    CashierStoreVO cashierStoreVO = storeV2Service.getCashierStoreByStoreId(licenceInfoDTO.getStoreId());
                    if(null != cashierStoreVO){
                        licenceInfoDTO.setStoreSn(cashierStoreVO.getStoreCode());
                        licenceInfoDTO.setStoreName(cashierStoreVO.getStoreName());
                        licenceInfoDTO.setMerchantId(cashierStoreVO.getMerchantId());
                        licenceInfoDTO.setMerchantSn(cashierStoreVO.getMerchantCode());
                        licenceInfoDTO.setMerchantName(cashierStoreVO.getMerchantName());
                    }
                } catch (Exception ex){
                    log.error("getCashierStoreByStoreId store not exists, storeId:{}", licenceInfoDTO.getStoreId(), ex);
                }
            });
        }
        return new PageResponse(request.getPage(), request.getPageSize(), totalCount, licenceInfoDTOList);
    }

    @Override
    public Integer getUnassignedAvailableQuantity(LicenceRequest request) {
        if(StringUtils.isBlank(request.getUserId())){
            throw new ParamException("参数错误,用户ID不能为空");
        }
        if(StringUtils.isBlank(request.getOrganizationId())){
            throw new ParamException("参数错误,组织ID不能为空");
        }
        if(null == request.getBrand()){
            throw new ParamException("参数错误,产品品牌不能为空");
        }
        if(null == request.getProductType()){
            throw new ParamException("参数错误,产品类型不能为空");
        }
        if(LicenceUtil.isDirectOrganization(request.getOrganizationCodePath())){
            String merchantId = request.getMerchantId();
            if(StringUtils.isBlank(merchantId) && StringUtils.isNotBlank(request.getStoreId())){
                StoreV2VO storeV2VO = storeV2Service.getStoreByStoreId(request.getStoreId());
                if(null == storeV2VO){
                    throw new BusinessException("门店信息不存在");
                }
                merchantId = storeV2VO.getMerchantId();
            }
            //设置商户ID
            request.setMerchantId(merchantId);
            return licenceInfoDAO.getUnassignedAvailableQuantity4Merchant(request);
        }else {
            return licenceInfoDAO.getUnassignedAvailableQuantity4Agent(request);
        }
    }

    @Override
    public LicenceInfoDO getRecentlyExpiredLicenceInfo(LicenceRequest request) {
        if(StringUtils.isBlank(request.getUserId())){
            throw new ParamException("参数错误,用户ID不能为空");
        }
        if(StringUtils.isBlank(request.getOrganizationId())){
            throw new ParamException("参数错误,组织ID不能为空");
        }
        if(null == request.getBrand()){
            throw new ParamException("参数错误,产品品牌不能为空");
        }
        if(null == request.getProductType()){
            throw new ParamException("参数错误,产品类型不能为空");
        }
        if(LicenceUtil.isDirectOrganization(request.getOrganizationCodePath())){
            String merchantId = request.getMerchantId();
            if(StringUtils.isBlank(merchantId) && StringUtils.isNotBlank(request.getStoreId())){
                StoreV2VO storeV2VO = storeV2Service.getStoreByStoreId(request.getStoreId());
                if(null == storeV2VO){
                    throw new BusinessException("门店信息不存在");
                }
                merchantId = storeV2VO.getMerchantId();
            }
            //设置商户ID
            request.setMerchantId(merchantId);
            return licenceInfoDAO.getRecentlyExpiredLicenceInfo4Merchant(merchantId, null, request.getBrand(), request.getProductType());
        }else {
            return licenceInfoDAO.getRecentlyExpiredLicenceInfo4Agent(request);
        }
    }

    @Override
    public LicenceInfoDO getLicenceInfoByLicenceNo(String licenceNo) {
        return licenceInfoDAO.getLicenceInfoByLicenceNo(licenceNo);
    }

    @Override
    public List<LicenceSummaryDTO> availableQuantitySummary(LicenceRequest request) {
        if(StringUtils.isBlank(request.getUserId())){
            throw new ParamException("参数错误,用户ID不能为空");
        }
        if(StringUtils.isBlank(request.getOrganizationId())){
            throw new ParamException("参数错误,机构ID不能为空");
        }
        if(null == request.getBusinessType()){
            throw new ParamException("参数错误,业务类型不能为空");
        }
        List<LicenceBrandEnum> brandList = LicenceUtil.getLicenceBrandList(request.getBusinessType());
        if(LicenceUtil.isDirectOrganization(request.getOrganizationCodePath())){
            String merchantId = request.getMerchantId();
            if(StringUtils.isBlank(merchantId) && StringUtils.isNotBlank(request.getStoreId())){
                StoreV2VO storeV2VO = storeV2Service.getStoreByStoreId(request.getStoreId());
                if(null == storeV2VO){
                    throw new BusinessException("门店信息不存在");
                }
                merchantId = storeV2VO.getMerchantId();
            }
            //设置商户ID
            request.setMerchantId(merchantId);
            return licenceInfoDAO.availableQuantitySummary4Merchant(merchantId, brandList);
        }else {
            return licenceInfoDAO.availableQuantitySummary4Agent(request.getOrganizationId(), request.getUserId(), brandList);
        }
    }

    @Override
    public List<LicenceUnassignedInfoDTO> getUnassignedAvailableList(LicenceRequest request) {
        if(StringUtils.isBlank(request.getUserId())){
            throw new ParamException("参数错误,用户ID不能为空");
        }
        if(StringUtils.isBlank(request.getOrganizationId())){
            throw new ParamException("参数错误,组织ID不能为空");
        }
        LicenceRequest licenceRequest = new LicenceRequest();
        licenceRequest.setOrganizationId(request.getOrganizationId());
        licenceRequest.setUserId(request.getUserId());
        licenceRequest.setVersionType(request.getVersionType());
        licenceRequest.setBrand(LicenceBrandEnum.SQB);
        licenceRequest.setProductType(LicenceProductTypeEnum.GENERAL);

        List<VersionTypeRemainingCountDTO> versionTypeRemainingCount = licenceManagerService.agentGetVersionTypeRemainingCount(request.getOrganizationId(), request.getUserId());
        int cashierQuantity = versionTypeRemainingCount.stream().mapToInt(VersionTypeRemainingCountDTO::getRemainingCount).sum();

        LicenceUnassignedInfoDTO cashier = new LicenceUnassignedInfoDTO();
        cashier.setBrand(LicenceBrandEnum.SQB);
        cashier.setProductType(LicenceProductTypeEnum.GENERAL);
        cashier.setProductName(LicenceProductTypeEnum.GENERAL.getName());
        cashier.setQuantity(cashierQuantity);

        if (StringUtils.isNotBlank(request.getStoreId())) {
            try {
                StoreV2VO storeV2VO = storeV2Service.getSimpleStoreByStoreId(request.getStoreId());
                String storeName = storeV2VO.getStoreName();
                cashier.setStoreId(request.getStoreId());
                cashier.setStoreName(storeName);
            } catch (Exception e) {
                log.warn("查询店铺信息异常",
                        keyValue("method", "getUnassignedAvailableList.getSimpleStoreByStoreId"),
                        keyValue("storeId", request.getStoreId()),
                        e);
            }
        }
        return Collections.singletonList(cashier);
    }

    @Override
    public BusinessPrivilegeResponseEntity getStoreCashierActivatedPrivilege(BusinessPrivilegeRequestEntityAdapter request) {
        BusinessPrivilegeResponseEntity entity = new BusinessPrivilegeResponseEntity();
        entity.setSuccess(true);
        //无权限，不展示
        entity.setDisplay(true);
        StoreV2VO storeV2VO = storeV2Service.getSimpleStoreByStoreId(request.getStoreId());
        String storeName = "";
        try {
            storeName = URLEncoder.encode(storeV2VO.getStoreName(), "UTF-8");
        }catch (Exception ex){
        }

        StoreDeviceActiveDO primaryActive = getStoreDeviceActive(storeV2VO.getStoreId());
        if(null != primaryActive){
            int days = (null != apolloConfigHelper.getCrmAllowUnbindStoreCashierDays()) ? apolloConfigHelper.getCrmAllowUnbindStoreCashierDays().intValue() : 7;
            long activeTime = primaryActive.getActiveTime();
            long unbindLastTime = DateUtils.addDate(new Date(activeTime), days).getTime();
            LicenceProductTypeEnum productType = DeviceTypeEnum.getLicenceProductType(primaryActive.getType());
            if(System.currentTimeMillis() <= unbindLastTime){
                String activeTimeStr = DateUtils.getFormatString(DateUtils.date_format_yyyy_MM_dd, new Date(activeTime));
                entity.setComponentType(Constants.CrmConstants.STORE_ACTIVE_TYPE_JUMP);
//                String jumpTitle = new StringBuilder(activeTimeStr).append(" ").append(productType.getName()).append("已激活 取消激活").toString();
                String jumpTitle = new StringBuilder(activeTimeStr).append(" ").append("已激活 取消激活").toString();
                entity.setJumpTitle(jumpTitle);
                entity.setJumpUrl(String.format(storeCashierUnbindJumpUrl, storeV2VO.getStoreId(), storeName, activeTimeStr, LicenceBrandEnum.SQB.name(), productType.name(), productType.getName()));
            }else {
                entity.setComponentType(Constants.CrmConstants.STORE_ACTIVE_TYPE_DISPLAY);
//                String jumpTitle = new StringBuilder(DateUtils.getFormatString(DateUtils.date_format_yyyy_MM_dd, new Date(primaryActive.getActiveTime()))).append(" ").append(productType.getName()).append("已激活").toString();
                String jumpTitle = new StringBuilder(DateUtils.getFormatString(DateUtils.date_format_yyyy_MM_dd, new Date(primaryActive.getActiveTime()))).append(" ").append("已激活").toString();
                entity.setJumpTitle(jumpTitle);
            }
        }else {
            entity.setComponentType(Constants.CrmConstants.STORE_ACTIVE_TYPE_JUMP);
            entity.setJumpTitle(Constants.CrmConstants.STORE_ACTIVE_TYPE_JUMP_TITLE);
            entity.setJumpUrl(String.format(storeCashierActiveJumpUrl, storeV2VO.getStoreId(), storeName));
        }
        return entity;
    }

    @Override
    public BusinessPrivilegeResponseEntity getStoreCashierActiveTime(BusinessPrivilegeRequestEntityAdapter request) {
        BusinessPrivilegeResponseEntity businessPrivilegeResponseEntity = new BusinessPrivilegeResponseEntity();
        businessPrivilegeResponseEntity.setSuccess(true);
        businessPrivilegeResponseEntity.setDisplay(false);

        String storeId = request.getStoreId();
        if (StringUtils.isBlank(storeId)) {
            return businessPrivilegeResponseEntity;
        }
        StoreDeviceActiveDO storeDeviceActive = getStoreDeviceActive(request.getStoreId());
        if (null == storeDeviceActive) {
            return businessPrivilegeResponseEntity;
        }

        String activeTimeStr = DateUtils.getFormatString(DateUtils.data_format_yyyy_MM_dd_hh_mm_ss, new Date(storeDeviceActive.getActiveTime()));
        businessPrivilegeResponseEntity.setComponentType(Constants.CrmConstants.STORE_ACTIVE_TYPE_DISPLAY);
        businessPrivilegeResponseEntity.setJumpTitle(activeTimeStr);
        businessPrivilegeResponseEntity.setDisplay(true);
        return businessPrivilegeResponseEntity;
    }

    @Override
    public BusinessPrivilegeResponseEntity getStoreCashierActiveType(BusinessPrivilegeRequestEntityAdapter request) {
        BusinessPrivilegeResponseEntity businessPrivilegeResponseEntity = new BusinessPrivilegeResponseEntity();
        businessPrivilegeResponseEntity.setSuccess(true);
        businessPrivilegeResponseEntity.setDisplay(false);

        String storeId = request.getStoreId();
        if (StringUtils.isBlank(storeId)) {
            return businessPrivilegeResponseEntity;
        }
        StoreDeviceActiveDO storeDeviceActive = getStoreDeviceActive(request.getStoreId());
        if (null == storeDeviceActive) {
            return businessPrivilegeResponseEntity;
        }
        LicenceProductTypeEnum productType = DeviceTypeEnum.getLicenceProductType(storeDeviceActive.getType());
        businessPrivilegeResponseEntity.setComponentType(Constants.CrmConstants.STORE_ACTIVE_TYPE_DISPLAY);
        businessPrivilegeResponseEntity.setJumpTitle(productType.getName());
        businessPrivilegeResponseEntity.setDisplay(true);
        return businessPrivilegeResponseEntity;
    }

    @Override
    public BusinessPrivilegeResponseEntity getStoreCashierVersionType(BusinessPrivilegeRequestEntityAdapter request) {
        BusinessPrivilegeResponseEntity businessPrivilegeResponseEntity = new BusinessPrivilegeResponseEntity();
        businessPrivilegeResponseEntity.setSuccess(true);
        businessPrivilegeResponseEntity.setDisplay(false);

        String storeId = request.getStoreId();
        if (StringUtils.isBlank(storeId)) {
            return businessPrivilegeResponseEntity;
        }
        StoreDeviceActiveDO storeDeviceActive = getStoreDeviceActive(request.getStoreId());
        if (null == storeDeviceActive) {
            return businessPrivilegeResponseEntity;
        }
        businessPrivilegeResponseEntity.setComponentType(Constants.CrmConstants.STORE_ACTIVE_TYPE_DISPLAY);
        businessPrivilegeResponseEntity.setDisplay(true);

        String versionType = storeDeviceActive.getVersionType();
        if (StringUtils.isBlank(versionType)) {
            versionType = LicenceVersionTypeEnum.LIFE.getName();
        }
        businessPrivilegeResponseEntity.setJumpTitle(versionType);
        return businessPrivilegeResponseEntity;
    }

    private StoreDeviceActiveDO getStoreDeviceActive(String storeId) {
        StoreDeviceActiveDO cashierActiveDO = storeDeviceActiveService.getEffectiveStoreActivated(storeId, DeviceTypeEnum.CASHIER);
        StoreDeviceActiveDO retailCashierActiveDO = storeDeviceActiveService.getEffectiveStoreActivated(storeId, RETAIL_CASHIER);
        StoreDeviceActiveDO primaryActive = null;
        if (cashierActiveDO != null) {
            primaryActive = cashierActiveDO;
        } else if (retailCashierActiveDO != null) {
            primaryActive = retailCashierActiveDO;
        }
        return primaryActive;
    }

    @Override
    public boolean crmActivatedStore(LicenceRequest request) {
        if(StringUtils.isBlank(request.getUserId())){
            throw new ParamException("参数错误,用户ID不能为空");
        }
        if(StringUtils.isBlank(request.getOrganizationId())){
            throw new ParamException("参数错误,机构ID不能为空");
        }
        if(StringUtils.isBlank(request.getStoreId())){
            throw new ParamException("参数错误,门店ID不能为空");
        }
        if (null == request.getBrand()) {
            request.setBrand(LicenceBrandEnum.SQB);
        }
        if (request.getBrand() == LicenceBrandEnum.SQB && null == request.getProductType()) {
            request.setProductType(LicenceProductTypeEnum.RESTAURANT);
        }

        StoreV2VO storeV2VO = storeV2Service.getStoreByStoreId(request.getStoreId());
        if(null == storeV2VO){
            throw new ParamException("门店信息不存在");
        }

        // 餐饮、零售不能同时使用校验
        if (Objects.equals(LicenceProductTypeEnum.RETAIL, request.getProductType())) {
            if (merchantV2Service.cateringMerchant(storeV2VO.getMerchantId())) {
                throw new ParamException("原商户已开通餐饮（点单/外卖/收银saas）相关业务，无法激活零售收银系统，请去审批中心提交“点单外卖业务关闭”审批");
            }
        } else {
            if (merchantV2Service.retailMerchant(storeV2VO.getMerchantId())) {
                throw new ParamException("原商户已开通零售（外卖/收银saas）相关业务，无法激活餐饮收银系统，请去审批中心提交“点单外卖业务关闭”审批");
            }
        }


        String lockKeyPrefix = new StringBuilder(com.wosai.smartbiz.uc.constants.Constants.RedisConstants.CRM_ACTIVE_STORE_CASHIER_CONFIG_PREFIX)
                .append(":").append(request.getBrand().name())
                .append(":").append(request.getProductType().name())
                .toString();
        String lockKey = redisUtil.generateKey(lockKeyPrefix, request.getStoreId());
        return lockService.performWithLock(GdsConstants.GDS_MODULE, lockKey,100L,()->{
            LicenceProductTypeEnum requestProductType = request.getProductType();

            LicenceRequest generalLicenceRequest = new LicenceRequest();
            BeanUtils.copyProperties(request, generalLicenceRequest);
            if (Objects.equals(generalLicenceRequest.getBrand(), LicenceBrandEnum.SQB)) {
                generalLicenceRequest.setProductType(LicenceProductTypeEnum.GENERAL);
            }
            DeviceTypeEnum activatedDeviceType = DeviceTypeEnum.CASHIER;
            if (Objects.equals(requestProductType, LicenceProductTypeEnum.RETAIL)) {
                activatedDeviceType = RETAIL_CASHIER;
            }

            // getRecentlyExpiredLicenceInfo这个方法，自研收银软件和第三方的共用，不改动这个方法的逻辑
            // 此处确认是自研收银软件，在此处改造
            List<LicenceActiveRes> existActiveInfo = licenceManagerService.getActivatedLicenceListByOwner(request.getStoreId(), activatedDeviceType);
            existActiveInfo = existActiveInfo.stream().filter(LicenceActiveRes::isNowValid).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(existActiveInfo)) {
                throw new ParamException("门店已经激活，无需重复激活");
            }

            List<ItemRemainingCountDTO> itemRemainingCountDTOS = licenceManagerService.agentGetRemainingCount(request.getOrganizationId(), request.getUserId(), activatedDeviceType);
            ItemRemainingCountDTO itemRemainingCount = itemRemainingCountDTOS.stream().filter(it -> it.getRemainingCount() > 0).findFirst().orElse(null);
            if (null == itemRemainingCount) {
                throw new ParamException("您没有可用的许可证名额，暂时无法激活");
            }
            // 激活
            licenceManagerService.agentActiveByItem(request.getOrganizationId(), request.getUserId(), itemRemainingCount, request.getStoreId(), activatedDeviceType);
            //发送绑定消息
            SceneEnum scene = Objects.equals(requestProductType, LicenceProductTypeEnum.RETAIL) ? SceneEnum.RETAIL : SceneEnum.RESTAURANT;
            storeDeviceActiveService.sendCashierBindMessage(storeV2VO.getStoreId(), request.getOperatorId(), BindStatusEnum.BIND, scene);
            return true;
        });
    }

    @Override
    public ProductActiveDTO externalActivatedStore(LicenceClientInfoDTO deviceClientInfoDTO, StoreV2VO storeV2VO) {
        String lockKeyPrefix = new StringBuilder(com.wosai.smartbiz.uc.constants.Constants.RedisConstants.CRM_ACTIVE_STORE_CASHIER_CONFIG_PREFIX)
                .append(":").append(deviceClientInfoDTO.getBrand().name())
                .append(":").append(deviceClientInfoDTO.getProductType().name())
                .toString();
        String lockKey = redisUtil.generateKey(lockKeyPrefix, storeV2VO.getStoreId());
        return lockService.performWithLock(GdsConstants.GDS_MODULE, lockKey,100L,()->{
            LicenceInfoDO licenceInfoDO = licenceInfoDAO.getLicenceInfoByLicenceNo(deviceClientInfoDTO.getLicenceNo());
            if(null == licenceInfoDO){
                throw new ParamException("关联的许可证信息不存在");
            }

            Long activeTime = (null != licenceInfoDO.getActiveTime()) ? licenceInfoDO.getActiveTime() : System.currentTimeMillis();
            Long beginTime = (null == licenceInfoDO.getBeginTime()) ? activeTime : null;
            licenceInfoDAO.updateActivatedByLicenceNo(licenceInfoDO.getLicenceNo(), YesNoEnum.Y, activeTime, beginTime, null, null);
            licenceClientInfoDAO.updateActivatedById(deviceClientInfoDTO.getId(), YesNoEnum.Y, System.currentTimeMillis());

            //保存操作日志
            LicenceOperationLogDO operationLogDO = new LicenceOperationLogDO()
                    .setBatchNo(licenceInfoDO.getBatchNo())
                    .setLicenceNo(licenceInfoDO.getLicenceNo())
                    .setOperationTime(System.currentTimeMillis())
                    .setQuantity(1)
                    .setOperationType(LicenceOperationTypeEnum.ACTIVATION)
                    .setOrganizationId(licenceInfoDO.getOrganizationId())
                    .setUserId(licenceInfoDO.getUserId())
                    .setTargetName(storeV2VO.getStoreCode())
                    .setBrand(licenceInfoDO.getBrand())
                    .setProductType(licenceInfoDO.getProductType())
                    .setRemark("第三方收银机激活");
            licenceOperationLogDAO.insert(operationLogDO);

            ProductActiveDTO productActiveDTO = new ProductActiveDTO();
            productActiveDTO.setClientId(deviceClientInfoDTO.getClientId());
            productActiveDTO.setClientName(deviceClientInfoDTO.getClientName());
            productActiveDTO.setBrand(deviceClientInfoDTO.getBrand());
            productActiveDTO.setProductType(deviceClientInfoDTO.getProductType());
            productActiveDTO.setStoreId(storeV2VO.getStoreId());
            productActiveDTO.setStoreName(storeV2VO.getStoreName());
            productActiveDTO.setMerchantId(storeV2VO.getMerchantId());
            productActiveDTO.setMerchantSn(storeV2VO.getMerchantCode());
            productActiveDTO.setMerchantName(storeV2VO.getMerchantName());
            productActiveDTO.setProductStatus(ProductStatusEnum.PURCHASED);
            productActiveDTO.setLicenceStatus(LicenceStatusEnum.ACTIVATED);
            productActiveDTO.setActivated(YesNoEnum.Y);

            return productActiveDTO;
        });
    }

    @Override
    public boolean crmUnbindStore(LicenceRequest request) {
        if(StringUtils.isBlank(request.getUserId())){
            throw new ParamException("参数错误,用户ID不能为空");
        }
        if(StringUtils.isBlank(request.getOrganizationId())){
            throw new ParamException("参数错误,机构ID不能为空");
        }
        if(StringUtils.isBlank(request.getStoreId())){
            throw new ParamException("参数错误,门店ID不能为空");
        }
        if (null == request.getBrand()) {
            request.setBrand(LicenceBrandEnum.SQB);
        }
        if (request.getBrand() == LicenceBrandEnum.SQB && null == request.getProductType()) {
            request.setProductType(LicenceProductTypeEnum.RESTAURANT);
        }
        String lockKey = redisUtil.generateKey(com.wosai.smartbiz.uc.constants.Constants.RedisConstants.CRM_UNBIND_STORE_CASHIER_CONFIG_PREFIX, request.getStoreId());
        return lockService.performWithLock(GdsConstants.GDS_MODULE, lockKey,100L,()->{
            StoreV2VO storeV2VO = storeV2Service.getStoreByStoreId(request.getStoreId());
            if(null == storeV2VO){
                throw new ParamException("门店不存在");
            }
            DeviceTypeEnum unbindDeviceType = DeviceTypeEnum.CASHIER;
            if (Objects.equals(request.getProductType(), LicenceProductTypeEnum.RETAIL)) {
                unbindDeviceType = RETAIL_CASHIER;
            }
            StoreDeviceActiveDO storeDeviceActiveDO = storeDeviceActiveService.getEffectiveStoreActivated(request.getStoreId(), unbindDeviceType);
            LogUtils.logInfo("after2", "getStoreActivatedDOFromLicenceManager", storeDeviceActiveDO);
            if(null == storeDeviceActiveDO){
                throw new ParamException("门店已经解绑，无需重复解绑");
            }

            int days = (null != apolloConfigHelper.getCrmAllowUnbindStoreCashierDays()) ? apolloConfigHelper.getCrmAllowUnbindStoreCashierDays().intValue() : 7;
            long unbindLastTime = DateUtils.addDate(new Date(storeDeviceActiveDO.getActiveTime()), days).getTime();
            if(System.currentTimeMillis() > unbindLastTime){
                throw new ParamException("激活" + days + "天内的门店才允许解绑");
            }

            boolean unbind = storeDeviceActiveService.cancelBindByStoreId(request.getStoreId(), "CRM解绑", unbindDeviceType);
            if(!unbind){
                throw new ParamException("门店解绑失败");
            }
            return true;
        });
    }

    @Override
    public SqbCashierUnbindCheckResult crmUnbindStoreCheck(LicenceRequest request) {
        if(StringUtils.isBlank(request.getStoreId())){
            throw new ParamException("参数错误,门店ID不能为空");
        }
        DeviceTypeEnum unbindDeviceType = DeviceTypeEnum.CASHIER;
        if (Objects.equals(request.getProductType(), LicenceProductTypeEnum.RETAIL)) {
            unbindDeviceType = RETAIL_CASHIER;
        }
        CancelRenewCheckRes cancelRenewCheckRes = storeDeviceActiveService.cancelBindByStoreIdCheck(request.getStoreId(), unbindDeviceType);
        SqbCashierUnbindCheckResult result = new SqbCashierUnbindCheckResult();
        result.setSuccess(cancelRenewCheckRes.isSuccess());
        result.setMessage(cancelRenewCheckRes.getMessage());
        return result;
    }

    @Override
    public String getCrmAppPurchaseCashierLicenceUrl(LicenceRequest request) {
        ApolloConfig apolloConfig = apolloConfigHelper.getApolloConfig();
        if(null == apolloConfig || null == apolloConfig.getPurchaseTemplateConfig() || null == apolloConfig.getPurchaseTemplateConfig().getSqbCashierPurchaseAuditTemplateId()){
            return null;
        }
        Long auditTemplateId = apolloConfig.getPurchaseTemplateConfig().getSqbCashierPurchaseAuditTemplateId();
        //发起平台：app、crmapp、crmweb、msp、sp
        String platform = "crmapp";
        AuditPreStartDto auditPreStartDto = new AuditPreStartDto();
        auditPreStartDto.setOperator(request.getUserId());
        //审批发起人platform（CRM、MSP、SP、APP）
        auditPreStartDto.setPlatform("CRM");
        auditPreStartDto.setAuditTemplateId(auditTemplateId);
        return spWorkflowAuditService.preStartAuditForInner(platform, auditPreStartDto);
    }

    @Override
    public boolean expireLicence() {
        LicenceRequest request = new LicenceRequest();
        int page = 0;
        int pageSize = 10;
        request.setStartTime(DateUtil.floor(System.currentTimeMillis()));
        Integer totalCount = licenceInfoDAO.countExpireLicencePageList(request);
        if(null == totalCount || totalCount == 0){
            return true;
        }
        boolean nextPage = false;
        do {
            page++;
            request.setPage(page);
            request.setPageSize(pageSize);
            int startRow = (request.getPage() - 1) * request.getPageSize();
            request.setStartRow(startRow);
            List<LicenceInfoDO> licenceInfoDOList = licenceInfoDAO.findExpireLicencePageList(request);
            if(CollectionUtils.isNotEmpty(licenceInfoDOList)){
                for (LicenceInfoDO licenceInfoDO : licenceInfoDOList){
                    LicenceOperationLogDO operationLogDO = licenceOperationLogDAO.getExpireByBatchNoAndLicenceNo(licenceInfoDO.getBatchNo(), licenceInfoDO.getLicenceNo());
                    if(null == operationLogDO){

                        String expireTimeStr = DateUtils.getFormatString(DateUtils.data_format_yyyy_MM_dd_hh_mm_ss, new Date(licenceInfoDO.getExpireTime()));
                        operationLogDO = new LicenceOperationLogDO()
                                .setBatchNo(licenceInfoDO.getBatchNo())
                                .setLicenceNo(licenceInfoDO.getLicenceNo())
                                .setOperationTime(System.currentTimeMillis())
                                .setQuantity(1)
                                .setOperationType(LicenceOperationTypeEnum.EXPIRE)
                                .setOrganizationId(licenceInfoDO.getOrganizationId())
                                .setUserId(licenceInfoDO.getUserId())
                                .setRemark(expireTimeStr + "许可证到期")
                                .setTargetOrganizationId(null)
                                .setTargetUserId(null)
                                .setBrand(licenceInfoDO.getBrand())
                                .setProductType(licenceInfoDO.getProductType())
                                .setTargetName("系统");
                        licenceOperationLogDAO.insert(operationLogDO);

                        //已经绑定的许可证需需要发送消息
                        if(licenceInfoDO.getActivated() == YesNoEnum.Y){
                            boolean unbind = storeDeviceActiveService.licenceExpireCancelBind(licenceInfoDO.getLicenceNo(), licenceInfoDO.getExpireTime(), "许可证过期解绑");
                            if(!unbind){
                                log.error("licence expire cancel bind store error, licenceNo:{}", licenceInfoDO.getLicenceNo());
                            }
                        }
                    }
                }
            }
            int surplusCount = totalCount - (request.getPage() * request.getPageSize());
            nextPage = (surplusCount > 0) && CollectionUtils.isNotEmpty(licenceInfoDOList);
        }while (nextPage);
        return true;
    }

    @Override
    public void transferInvalidUserLicence(Map<String, Object> crmUserMap) {
        String status = MapUtils.getString(crmUserMap, "status", null);
        if(Objects.isNull(status) || Objects.equals(status, "1")){
            return;
        }
        String userId = MapUtils.getString(crmUserMap, "id", null);

        //查询用户组织列表
        QueryEsUserReq esUserReq = QueryEsUserReq.builder()
                .page(1)
                .pageSize(10)
                .id(userId)
                .build();
        PageListResult<QueryUserResponse> esUserPageListResult = salesEsUserService.findEsUser(esUserReq);
        if(CollectionUtils.isEmpty(esUserPageListResult.getRecords())){
            return;
        }
        List<QueryUserResponse> userResponseList = esUserPageListResult.getRecords();
        userResponseList.stream().forEach(userResponse -> {
            List<QueryUserResponse.MultiUserOrganization> multiUserOrganizationList = userResponse.getMultiUserOrganizationList();
            if(CollectionUtils.isNotEmpty(multiUserOrganizationList)){
                multiUserOrganizationList.stream().forEach(multiUserOrg -> {

                    String fourLevelOrganizationCodePath = get4LevelOrganizationCodePath(multiUserOrg.getPath());
                    if(StringUtils.isBlank(fourLevelOrganizationCodePath)){
                        return;
                    }
                    Map<String, Object> fourLevelOrganizationMap = organizationService.getOrganizationConfigByPath(fourLevelOrganizationCodePath);
                    if(MapUtils.isEmpty(fourLevelOrganizationMap)){
                        return;
                    }

                    List<LicenceUserDTO> buyerUserList = getOrgBuyerUserList(multiUserOrg.getOrganizationId(), null);
                    if(CollectionUtils.isEmpty(buyerUserList)){
                        return;
                    }
                    String targetUserId = buyerUserList.get(0).getUserId();

                    boolean updateResult = updateUserIdByOrganizationId(multiUserOrg.getOrganizationId(), userId, targetUserId);
                    if(!updateResult){
                        log.error("transferInvalidUserLicence error,organizationId:{},sourceUserId:{},targetUserId:{}", multiUserOrg.getOrganizationId(), userId, targetUserId);
                    }
                });
            }
        });
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public boolean payOrGiftDistribution(LicenceDistributionRequest request) {
        if(null == request){
            throw new ParamException("参数错误,参数不能为空");
        }
        if(CollectionUtils.isEmpty(request.getDistributionData())){
            throw new ParamException("参数错误,分配数据不能为空");
        }
        if(null == request.getPermanent()){
            throw new ParamException("参数错误,请选择有效期类型");
        }
        if(request.getPermanent() == YesNoEnum.N){
            if(null == request.getExpireTime()){
                throw new ParamException("参数错误,有效期不能为空");
            }
        }

        Date expireDate = null;
        if(null != request.getExpireTime()){
            expireDate = DateUtils.floor(new Date(request.getExpireTime()));
        }

        //生成许可证批次号
        String batchNo = generateBatchNo();
        LicenceBatchInfoDO batchInfoDO = licenceBatchInfoDAO.getBatchInfoByBatchNo(batchNo);
        if(null != batchInfoDO){
            log.warn("CashierLicenceServiceImpl.distribution error batchNo is exists, bizOrderId:{},batchNo:{}", request.getBizOrderId(), batchNo);
            throw new BusinessException("许可证批次号已经存在,需重新生成批次号");
        }
        for(LicenceDistributionRequest.DistributionData distributionData : request.getDistributionData()) {
            String remark = (request.getType() == LicenceTypeEnum.GIFT) ? distributionData.getRemark() : request.getRemark();
            //如果是服务商许可证处理，则需要处理服务商用户信息
            if(request.getUserType() == LicenceUserTypeEnum.AGENT){
                LicenceUserDO licenceUserDO = getOriginalCrmUserInfoAndSave(distributionData);
                if(null == licenceUserDO){
                    continue;
                }
            }
            if (distributionData.getBrand() == null) {
                distributionData.setBrand(LicenceBrandEnum.SQB);
            }
            if (distributionData.getProductType() == null) {
                distributionData.setProductType(LicenceProductTypeEnum.GENERAL);
            }
            String targetUserId = distributionData.getUserId();
            String targetOrganizationId = distributionData.getOrganizationId();
            long operationTime = (null != request.getOperationTime()) ? request.getOperationTime() : System.currentTimeMillis();

            if(null == batchInfoDO){
                batchInfoDO = new LicenceBatchInfoDO();
                batchInfoDO.setBatchNo(batchNo);
                batchInfoDO.setType(request.getType());
                batchInfoDO.setExpireTime((request.getPermanent() == YesNoEnum.N && expireDate != null) ? expireDate.getTime() : DateUtils.PERMANENT_TIME);
                batchInfoDO.setOrganizationId(targetOrganizationId);
                batchInfoDO.setUserId(targetUserId);
                batchInfoDO.setUserType(request.getUserType());
                batchInfoDO.setStatus(LicenceStatusEnum.NORMAL);
                batchInfoDO.setOperatorId(request.getOperatorId());
                batchInfoDO.setOperatorName(request.getOperator());
                batchInfoDO.setOperationTime(operationTime);
                batchInfoDO.setRemark(remark);
                if(request.getType() == LicenceTypeEnum.PAY){
                    batchInfoDO.setBizOrderId(request.getBizOrderId());
                    batchInfoDO.setSaleSource(request.getSaleSource());
                    if(StringUtils.isNotBlank(request.getGoodsDetail()) && request.getGoodsDetail().length() < 1000){
                        batchInfoDO.setGoodsDetail(request.getGoodsDetail());
                    }
                }else{
                    String goodsDetail = JSON.toJSONString(distributionData);
                    if(StringUtils.isNotBlank(goodsDetail) && goodsDetail.length() < 1000){
                        batchInfoDO.setGoodsDetail(goodsDetail);
                    }
                }
                //保存许可证批次信息
                licenceBatchInfoDAO.insert(batchInfoDO);
            }

            //生成许可证信息并保存
            List<LicenceInfoDO> licenceInfoDOList = generateLicence(batchInfoDO, distributionData);

            //保存许可证分配记录信息
            Long distributionId = saveDistribution(batchInfoDO.getOrganizationId(), batchInfoDO.getUserId(), batchInfoDO.getBatchNo(), distributionData, YesNoEnum.N, remark, licenceInfoDOList, request);

            //保存许可证批次日志记录
            LicenceOperationLogDO operationLogDO = new LicenceOperationLogDO()
                    .setBatchNo(batchInfoDO.getBatchNo())
                    .setOperationTime(operationTime)
                    .setQuantity(distributionData.getQuantity())
                    .setOperationType(getOperationType(request.getType()))
                    .setOrganizationId(batchInfoDO.getOrganizationId())
                    .setUserId(batchInfoDO.getUserId())
                    .setTargetOrganizationId(null)
                    .setTargetUserId(null)
                    .setTargetName("系统")
                    .setDistributionId(distributionId)
                    .setBrand(distributionData.getBrand())
                    .setProductType(distributionData.getProductType())
                    .setRemark(remark);
            licenceOperationLogDAO.insert(operationLogDO);

            //如果是购买的许可证,并且不是数据订正场景,发送发货消息等
            if(CollectionUtils.isNotEmpty(licenceInfoDOList)
                    && request.getType() == LicenceTypeEnum.PAY
                    && !request.isRevise()){
                //发送发货消息
                try {
                    LicenceDeliveryInfo deliveryInfo = new LicenceDeliveryInfo();
                    deliveryInfo.setOrganizationId(batchInfoDO.getOrganizationId());
                    deliveryInfo.setUserId(batchInfoDO.getUserId());
                    deliveryInfo.setBatchNo(batchInfoDO.getBatchNo());
                    deliveryInfo.setBizOrderId(batchInfoDO.getBizOrderId());

                    List<LicenceDeliveryInfo.LicenceDeliveryItem> list = new ArrayList<>();
                    licenceInfoDOList.forEach(licenceInfoDO -> {
                        if(null != licenceInfoDO.getSaleInfo()){
                            Optional<LicenceDeliveryInfo.LicenceDeliveryItem> first = list.stream().filter(item -> Objects.equals(item.getOrderItemSn(), licenceInfoDO.getSaleInfo().getOrderItemId())).findFirst();
                            if (first.isPresent() && request.getUserType() == LicenceUserTypeEnum.AGENT){
                                first.get().getLicenceNoSet().add(licenceInfoDO.getLicenceNo());
                            }else{
                                LicenceDeliveryInfo.LicenceDeliveryItem deliveryItem = new LicenceDeliveryInfo.LicenceDeliveryItem();
                                deliveryItem.setOrderItemSn(licenceInfoDO.getSaleInfo().getOrderItemId());
                                deliveryItem.setLicenceNoSet(Sets.newHashSet(licenceInfoDO.getLicenceNo()));
                                deliveryItem.setCrmItemCode(licenceInfoDO.getSaleInfo().getCrmItemCode());
                                deliveryItem.setCrmItemName(licenceInfoDO.getSaleInfo().getCrmItemName());
                                deliveryItem.setOmsItemCode(licenceInfoDO.getSaleInfo().getOmsItemCode());
                                deliveryItem.setOmsItemName(licenceInfoDO.getSaleInfo().getOmsItemName());
                                deliveryItem.setStandardSkuCode(licenceInfoDO.getSaleInfo().getSku());
                                list.add(deliveryItem);
                            }
                        }
                    });
                    deliveryInfo.setList(list);
                    if(CollectionUtils.isNotEmpty(list)){
                        licenceMessageRpcService.sendDeliveryMessage(deliveryInfo);
                    }
                }catch (Exception ex) {
                    log.error("distribution process licence delivery message error,organizationId:{},userId:{},batchNo:{}", batchInfoDO.getOrganizationId(), batchInfoDO.getUserId(), batchInfoDO.getBatchNo(), ex);
                }
            }
        }
        return true;
    }

    @Override
    public boolean specialUser(String organizationCodePath, String positionId){
        if(!LicenceUtil.isAgentOrganization(organizationCodePath)){
            return false;
        }
        String[] codeArray = StringUtils.split(organizationCodePath, ",");
        if(codeArray.length != Constants.CrmConstants.ORGANIZATION_LEVEL_4){
            return false;
        }
        if(StringUtils.isBlank(positionId)){
            return false;
        }
        Map<String, Object> positionMap = salesPositionService.getPositionDetail(positionId);
        if(MapUtils.isEmpty(positionMap)){
            return false;
        }
        String positionName = MapUtils.getString(positionMap, "name", null);
        if(!Objects.equals(positionName, Constants.CrmConstants.SPECIAL_USER_POSITION_NAME)){
            return false;
        }
        return true;
    }


    /**
     * 获取原始组织机构信息
     * @param organizationId
     * @return
     */
    private LicenceOrganizationDO getOriginalOrganizationInfo(String organizationId){
        Map<String, Object> organizationMap = organizationService.getSimpleOrganization(organizationId);
        if(MapUtils.isEmpty(organizationMap)){
            return null;
        }
        String id = MapUtils.getString(organizationMap, "id", null);
        if(StringUtils.isBlank(id)){
            return null;
        }
        Integer level = MapUtils.getInteger(organizationMap, "level", null);
        String codePath = MapUtils.getString(organizationMap, "path", null);
        String namePath = MapUtils.getString(organizationMap, "name_path", null);
        String code = MapUtils.getString(organizationMap, "code", null);
        String name = MapUtils.getString(organizationMap, "name", null);
        String parentId = MapUtils.getString(organizationMap, "parent", null);

        LicenceOrganizationDO organizationDO = new LicenceOrganizationDO();
        organizationDO.setId(id);
        organizationDO.setCode(code);
        organizationDO.setName(name);
        organizationDO.setCodePath(codePath);
        organizationDO.setNamePath(namePath);
        organizationDO.setLevel(level);
        organizationDO.setParentId(parentId);
        return organizationDO;
    }

    /**
     * 获取原始组织机构信息
     * @param organizationCode
     * @return
     */
    private LicenceOrganizationDO getOriginalOrganizationInfoByCode(String organizationCode){
        Map<String, Object> organizationMap = organizationService.getSimpleOrganizationByCode(organizationCode);
        if(MapUtils.isEmpty(organizationMap)){
            return null;
        }
        String id = MapUtils.getString(organizationMap, "id", null);
        if(StringUtils.isBlank(id)){
            return null;
        }
        Integer level = MapUtils.getInteger(organizationMap, "level", null);
        String codePath = MapUtils.getString(organizationMap, "path", null);
        String namePath = MapUtils.getString(organizationMap, "name_path", null);
        String code = MapUtils.getString(organizationMap, "code", null);
        String name = MapUtils.getString(organizationMap, "name", null);
        String parentId = MapUtils.getString(organizationMap, "parent", null);

        LicenceOrganizationDO organizationDO = new LicenceOrganizationDO();
        organizationDO.setId(id);
        organizationDO.setCode(code);
        organizationDO.setName(name);
        organizationDO.setCodePath(codePath);
        organizationDO.setNamePath(namePath);
        organizationDO.setLevel(level);
        organizationDO.setParentId(parentId);
        return organizationDO;
    }

    /**
     * 获取CRM用户信息信息
     * @param userId
     * @return
     */
    private LicenceUserDO getOriginalCrmUserInfo(String userId){
        Map<String, Object> userMap = salesUserService.getUserWithOrganization(userId);
        if(MapUtils.isEmpty(userMap)){
            return null;
        }
        String id = MapUtils.getString(userMap, "id", null);
        if(StringUtils.isBlank(id)){
            return null;
        }
        String userCode = MapUtils.getString(userMap, "code", null);
        String cellphone = MapUtils.getString(userMap, "cellphone", null);
        String userName = MapUtils.getString(userMap, "linkman", null);
        Integer status = MapUtils.getInteger(userMap, "status", null);

        LicenceUserDO licenceUserDO = new LicenceUserDO();
        licenceUserDO.setUserId(id);
        licenceUserDO.setUserCode(userCode);
        licenceUserDO.setUserName(userName);
        licenceUserDO.setCellphone(cellphone);
        licenceUserDO.setStatus(status);

        return licenceUserDO;
    }

    /**
     * 获取CRM用户信息信息
     * @param cellphone
     * @return
     */
    private LicenceUserDO getOriginalCrmUserInfoByCellphone(String cellphone){
        Map<String, Object> userMap = salesUserService.getUserByCellphone(cellphone);
        if(MapUtils.isEmpty(userMap)){
            return null;
        }
        String id = MapUtils.getString(userMap, "id", null);
        if(StringUtils.isBlank(id)){
            return null;
        }
        String userCode = MapUtils.getString(userMap, "code", null);
        String userName = MapUtils.getString(userMap, "linkman", null);

        LicenceUserDO licenceUserDO = new LicenceUserDO();
        licenceUserDO.setUserId(id);
        licenceUserDO.setUserCode(userCode);
        licenceUserDO.setUserName(userName);
        licenceUserDO.setCellphone(cellphone);

        return licenceUserDO;
    }

    /**
     * 获取CRM用户信息,并且保存
     * @param distributionData
     * @return
     */
    private LicenceUserDO getOriginalCrmUserInfoAndSave(LicenceDistributionRequest.DistributionData distributionData){
        return getOriginalCrmUserInfoAndSave(distributionData.getOrganizationId(), distributionData.getUserId());
    }

    /**
     * 获取CRM用户信息,并且保存
     * @param organizationId
     * @param userId
     * @return
     */
    private LicenceUserDO getOriginalCrmUserInfoAndSave(String organizationId, String userId){
        LicenceUserDO licenceUserDO = licenceUserDAO.getLicenceUserByUserId(userId);
        if(null == licenceUserDO){
            licenceUserDO = getOriginalCrmUserInfo(userId);
            if(null == licenceUserDO){
                return null;
            }
            licenceUserDAO.insert(licenceUserDO);
        }
        if(StringUtils.isNotBlank(organizationId)){
            LicenceOrganizationDO organizationDO = licenceOrganizationDAO.getOrganizationById(organizationId);
            if(null == organizationDO){
                organizationDO = getOriginalOrganizationInfo(organizationId);
                if(null == organizationDO){
                    return null;
                }
                organizationDO.setOperationTime(System.currentTimeMillis());
                licenceOrganizationDAO.insert(organizationDO);
            }
        }
        return licenceUserDO;
    }


    /**
     * 根据批次信息生成许可证
     * @param batchInfoDO
     * @param distributionData
     * @return
     */
    private List<LicenceInfoDO> generateLicence(LicenceBatchInfoDO batchInfoDO, LicenceDistributionRequest.DistributionData distributionData){
        List<LicenceInfoDO> licenceInfoDOList = new ArrayList<>();
        for(int i=0; i < distributionData.getQuantity(); i++){
            LicenceInfoDO licenceInfoDO = new LicenceInfoDO();
            licenceInfoDO.setLicenceNo(CommonUtil.getUUID());
            licenceInfoDO.setBatchNo(batchInfoDO.getBatchNo());
            licenceInfoDO.setExpireTime(com.wosai.smartbiz.uc.utils.LicenceUtil.getLicenceExpireTime(distributionData.getVersionType(), null));
            licenceInfoDO.setBrand(distributionData.getBrand());
            licenceInfoDO.setProductType(distributionData.getProductType());
            if(batchInfoDO.getType() == LicenceTypeEnum.GIFT){
                licenceInfoDO.setActiveEndTime(DateUtils.addYear(new Date(), 1).getTime());
            }
            licenceInfoDO.setVersionType(distributionData.getVersionType());
            licenceInfoDO.setOrganizationId(batchInfoDO.getOrganizationId());
            licenceInfoDO.setUserId(batchInfoDO.getUserId());
            licenceInfoDO.setActivated(YesNoEnum.N);
            licenceInfoDO.setStatus(LicenceStatusEnum.NORMAL);
            licenceInfoDO.setBrand(distributionData.getBrand());
            licenceInfoDO.setProductType(distributionData.getProductType());
            if(CollectionUtils.isNotEmpty(distributionData.getSaleInfoDTOList()) && i < distributionData.getSaleInfoDTOList().size()){
                LicenceSaleInfoDTO saleInfoDTO = distributionData.getSaleInfoDTOList().get(i);
                if(null != saleInfoDTO){
                    licenceInfoDO.setOrderItemId(saleInfoDTO.getOrderItemId());
                    licenceInfoDO.setSaleInfo(saleInfoDTO);
                }
            }
            licenceInfoDOList.add(licenceInfoDO);
        }

        int row = licenceInfoDAO.batchInsert(licenceInfoDOList);
        return licenceInfoDOList;
    }

    /**
     * 保存分配信息、分配详细信息
     * @param targetOrganizationId
     * @param targetUserId
     * @param batchNo
     * @param retrieve
     * @param remark
     * @param licenceInfoDOList
     * @param request
     */
    private Long saveDistribution(String targetOrganizationId,
                                  String targetUserId,
                                  String batchNo,
                                  LicenceDistributionRequest.DistributionData distributionData,
                                  YesNoEnum retrieve,
                                  String remark,
                                  List<LicenceInfoDO> licenceInfoDOList,
                                  LicenceDistributionRequest request) {
        LicenceDistributionDO distributionDO = new LicenceDistributionDO();
        distributionDO.setOrganizationId(targetOrganizationId);
        distributionDO.setUserId(targetUserId);
        distributionDO.setBatchNo(batchNo);
        distributionDO.setOperationTime(System.currentTimeMillis());
        distributionDO.setQuantity(distributionData.getQuantity());
        distributionDO.setBrand(distributionData.getBrand());
        distributionDO.setProductType(distributionData.getProductType());
        distributionDO.setRetrieve(retrieve);
        distributionDO.setRemark(remark);
        distributionDO.setFromType(request.getType());
        distributionDO.setFromName(request.getFromName());
        distributionDO.setFromDistributionId(request.getFromDistributionId());
        distributionDO.setFromOrganizationId(request.getFromOrganizationId());

        licenceDistributionDAO.insert(distributionDO);

        //保存分配信息
        saveDistributionRecord(distributionDO, licenceInfoDOList);

        return distributionDO.getId();
    }

    /**
     * 保存分配明细信息
     * @param distributionDO
     * @param licenceInfoDOList
     * @return
     */
    private boolean saveDistributionRecord(LicenceDistributionDO distributionDO, List<LicenceInfoDO> licenceInfoDOList){
        List<LicenceDistributionRecordDO> recordDOList = new ArrayList<>();
        for(LicenceInfoDO licenceInfoDO : licenceInfoDOList){
            LicenceDistributionRecordDO recordDO = new LicenceDistributionRecordDO();
            recordDO.setDistributionId(distributionDO.getId());
            recordDO.setLicenceNo(licenceInfoDO.getLicenceNo());
            recordDO.setAssigned(YesNoEnum.N);

            recordDOList.add(recordDO);
        }

        int row = licenceDistributionRecordDAO.batchInsert(recordDOList);
        return row > 0;
    }

    /**
     * 生成许可证批次号
     * @return
     */
    private String generateBatchNo(){
        Random random = new Random();
        String batchNoRandom = String.format("%04d", random.nextInt(10000));
        String batchNo = new StringBuilder(DateUtils.getFormatString(DateUtils.date_format_yyMMddHHmmssSSS, new Date(System.currentTimeMillis()))).append(batchNoRandom).toString();

        return batchNo;
    }


    /**
     * 用户信息转换
     * @param esUserPageList
     * @return
     */
    private List<LicenceUserDTO> convertToLicenceUserDTO(List<QueryUserResponse> esUserPageList){
        if(CollectionUtils.isEmpty(esUserPageList)){
            return Lists.newArrayList();
        }
        return esUserPageList.stream()
                .map(esUser -> {
                    LicenceUserDTO licenceUserDTO = LicenceUserDTO.builder()
                            .userId(esUser.getId())
                            .userCode(esUser.getCode())
                            .userName(esUser.getLinkman())
                            .cellphone(esUser.getCellphone())
                            .positionId(esUser.getPositionId())
                            .organizationId(esUser.getOrganizationId())
                            .organizationCode(esUser.getCode())
                            .organizationCodePath(esUser.getOrganizationPath())
                            .organizationNamePath(esUser.getOrganizationNames())
                            .build();
                    return licenceUserDTO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取当前登录用户的4级组织的codePath
     * @param organizationCodePath
     * @return
     */
    private String get4LevelOrganizationCodePath(String organizationCodePath){
        if(!LicenceUtil.isAgentOrganization(organizationCodePath)){
            return null;
        }
        String[] codeArray = StringUtils.split(organizationCodePath, ",");
        if(codeArray.length > Constants.CrmConstants.ORGANIZATION_LEVEL_4){
            return new StringBuilder(codeArray[0]).append(",")
                    .append(codeArray[1]).append(",")
                    .append(codeArray[2]).append(",")
                    .append(codeArray[3]).toString();
        }
        return organizationCodePath;
    }

    /**
     * 获取原始的购买/赠送分配记录
     * @param organizationId
     * @param userId
     * @param batchNo
     * @return
     */
    private LicenceDistributionDO getPayOrGiftDistributionInfo(String organizationId, String userId, String batchNo){
        LicenceDistributionDO distributionDO = licenceDistributionDAO.getPayOrGiftDistributionByBatchNo(batchNo);
        if(null == distributionDO) {
            throw new BusinessException("分配信息不存在");
        }
        if(Objects.equals(userId, distributionDO.getUserId()) && Objects.equals(organizationId, distributionDO.getOrganizationId())){
            return distributionDO;
        }
        return null;
    }

    private LicenceOperationTypeEnum getOperationType(LicenceTypeEnum licenceType) {
        if(null == licenceType){
            return null;
        }
        switch (licenceType) {
            case PAY:
                return LicenceOperationTypeEnum.PAY;
            case DISTRIBUTION:
                return LicenceOperationTypeEnum.DISTRIBUTION;
            case GIFT:
                return LicenceOperationTypeEnum.GIFT;
        }
        return null;
    }

    /**
     * 获取组织购买人列表
     * @param organizationId
     * @param query
     * @return
     */
    private List<LicenceUserDTO> getOrgBuyerUserList(String organizationId, String query) {
        OrderBy orderBy = new OrderBy("ctime", OrderBy.OrderType.ASC);
        QueryEsUserReq profitEsUserReq = QueryEsUserReq.builder()
                .page(1)
                .pageSize(100)
                .organizationId(organizationId)
                .minOrgLevel(Constants.CrmConstants.ORGANIZATION_LEVEL_4)
                .status(1)
                .positionName(Constants.CrmConstants.SPECIAL_USER_POSITION_NAME)
                .orderBy(orderBy)
                .build();

        if(StringUtils.isNotBlank(query)){
            if(CellphoneCheckUtils.isNumeric(query)){
                profitEsUserReq.setCellphoneLike(query);
            }else {
                profitEsUserReq.setLinkmanLike(query);
            }
        }
        PageListResult<QueryUserResponse> esUserPageListResult = salesEsUserService.findEsUser(profitEsUserReq);
        if(CollectionUtils.isEmpty(esUserPageListResult.getRecords())) {
            return Lists.newArrayList();
        }

        return convertToLicenceUserDTO(esUserPageListResult.getRecords());
    }

    /**
     * 检查组织之间是否是"总代"和"直代"关系
     * @param organizationDO
     * @param targetOrganizationDO
     * @return
     */
    private boolean checkOrgRelation(LicenceOrganizationDO organizationDO, LicenceOrganizationDO targetOrganizationDO) {
        if(null == targetOrganizationDO || null == organizationDO){
            return false;
        }
        //判断你是否是同组织下
        if(Objects.equals(organizationDO.getId(), targetOrganizationDO.getId())){
            return true;
        }
        //判断组织之间的上下层关系
        if(StringUtils.isNotBlank(organizationDO.getCodePath()) && organizationDO.getCodePath().contains(targetOrganizationDO.getCode())){
            return true;
        }
        if(StringUtils.isNotBlank(targetOrganizationDO.getCodePath()) && targetOrganizationDO.getCodePath().contains(organizationDO.getCode())){
            return true;
        }
        List<ProfitDirectSign> list = profitDirectSignService.findByOrgAndAssociateOrg(organizationDO.getId(), targetOrganizationDO.getId());
        List<ProfitDirectSign> reversalList = profitDirectSignService.findByOrgAndAssociateOrg(targetOrganizationDO.getId(), organizationDO.getId());
        if(CollectionUtils.isNotEmpty(list) || CollectionUtils.isNotEmpty(reversalList)){
            return true;
        }
        return false;
    }

    /**
     * 批量保存许可证流转日志
     * @param userId
     * @param organizationId
     * @param targetUserId
     * @param targetOrganizationId
     * @param batchNo
     * @param quantity
     * @param distributionId
     * @param brand
     * @param productType
     * @param remark
     */
    private void batchSaveLicenceOperationLog(String userId, String organizationId, String targetUserId, String targetOrganizationId,
                                              String batchNo, Integer quantity, Long distributionId, String remark, LicenceBrandEnum brand, LicenceProductTypeEnum productType){
        //保存许可证分配进来日志记录
        LicenceOperationLogDO operationLogDO = new LicenceOperationLogDO();
        operationLogDO.setBatchNo(batchNo);
        operationLogDO.setOperationTime(System.currentTimeMillis());
        operationLogDO.setQuantity(quantity);
        operationLogDO.setOperationType(LicenceOperationTypeEnum.DISTRIBUTION);
        operationLogDO.setOrganizationId(targetOrganizationId);
        operationLogDO.setUserId(targetUserId);

        operationLogDO.setTargetOrganizationId(organizationId);
        operationLogDO.setTargetUserId(userId);
        operationLogDO.setTargetName(null);
        operationLogDO.setDistributionId(distributionId);
        operationLogDO.setBrand(brand);
        operationLogDO.setProductType(productType);
        operationLogDO.setRemark(remark);
        operationLogDO.setBrand(brand);
        operationLogDO.setProductType(productType);

        //保存许可证分配出去日志记录
        LicenceOperationLogDO outOperationLogDO = new LicenceOperationLogDO();
        outOperationLogDO.setBatchNo(batchNo);
        outOperationLogDO.setOperationTime(System.currentTimeMillis());
        outOperationLogDO.setQuantity(quantity);
        outOperationLogDO.setOperationType(LicenceOperationTypeEnum.DISTRIBUTION_OUT);
        outOperationLogDO.setOrganizationId(organizationId);
        outOperationLogDO.setUserId(userId);

        outOperationLogDO.setTargetOrganizationId(targetOrganizationId);
        outOperationLogDO.setTargetUserId(targetUserId);
        outOperationLogDO.setTargetName(null);
        outOperationLogDO.setDistributionId(distributionId);
        outOperationLogDO.setBrand(brand);
        outOperationLogDO.setProductType(productType);
        outOperationLogDO.setRemark(remark);
        outOperationLogDO.setBrand(brand);
        outOperationLogDO.setProductType(productType);

        licenceOperationLogDAO.batchInsert(Lists.newArrayList(operationLogDO, outOperationLogDO));
    }

    /**
     * 批量保存许可证流转日志
     * @param userId
     * @param organizationId
     * @param targetUserId
     * @param targetOrganizationId
     * @param remark
     */
    private void batchSaveLicenceOperationLog(String userId, String organizationId, String targetUserId, String targetOrganizationId, String remark) {
        List<LicenceSummaryDTO> summaryDTOList = licenceInfoDAO.summaryBatchLicenceCount(organizationId, userId);
        log.info("batchSaveLicenceOperationLog查询需要转移的数据,userId:{},organizationId:{},targetUserId:{},targetOrganizationId:{}", userId, organizationId, targetUserId, targetOrganizationId, JSON.toJSONString(summaryDTOList));
        if(CollectionUtils.isEmpty(summaryDTOList)){
            return;
        }
        List<LicenceOperationLogDO> operationLogDOList = new ArrayList<>();
        summaryDTOList.stream().forEach(summaryDTO -> {
            //保存许可证分配进来日志记录
            LicenceOperationLogDO operationLogDO = new LicenceOperationLogDO();
            operationLogDO.setBatchNo(summaryDTO.getBatchNo());
            operationLogDO.setOperationTime(System.currentTimeMillis());
            operationLogDO.setQuantity(summaryDTO.getQuantity());
            operationLogDO.setOperationType(LicenceOperationTypeEnum.DISTRIBUTION);
            operationLogDO.setOrganizationId(targetOrganizationId);
            operationLogDO.setUserId(targetUserId);

            operationLogDO.setTargetOrganizationId(organizationId);
            operationLogDO.setTargetUserId(userId);
            operationLogDO.setTargetName(null);
            operationLogDO.setDistributionId(null);
            operationLogDO.setBrand(summaryDTO.getBrand());
            operationLogDO.setProductType(summaryDTO.getProductType());
            operationLogDO.setRemark(remark);

            operationLogDOList.add(operationLogDO);

            //保存许可证分配出去日志记录
            LicenceOperationLogDO outOperationLogDO = new LicenceOperationLogDO();
            outOperationLogDO.setBatchNo(summaryDTO.getBatchNo());
            outOperationLogDO.setOperationTime(System.currentTimeMillis());
            outOperationLogDO.setQuantity(summaryDTO.getQuantity());
            outOperationLogDO.setOperationType(LicenceOperationTypeEnum.DISTRIBUTION_OUT);
            outOperationLogDO.setOrganizationId(organizationId);
            outOperationLogDO.setUserId(userId);

            outOperationLogDO.setTargetOrganizationId(targetOrganizationId);
            outOperationLogDO.setTargetUserId(targetUserId);
            outOperationLogDO.setTargetName(null);
            outOperationLogDO.setDistributionId(null);
            outOperationLogDO.setBrand(summaryDTO.getBrand());
            outOperationLogDO.setProductType(summaryDTO.getProductType());
            outOperationLogDO.setRemark(remark);

            operationLogDOList.add(outOperationLogDO);
        });

        if(CollectionUtils.isEmpty(operationLogDOList)){
            return;
        }

        licenceOperationLogDAO.batchInsert(operationLogDOList);
    }

    /**
     参数格式
     {
     "oldStoreSn": "12356",
     "reason": "77",
     "audit.operator_id": "6717d59c-4ae8-4fad-bd3e-661fe6e1e7dd",
     "audit.audit_template_id": 135622,
     "channel": "direct",
     "scene": "same",
     "newStoreSn": "777"
     }
     */
    /**
     * 餐饮收银软件继承审批前置校验
     */
    @Override
    public void licenseInheritPreCheck(Map<String, Object> params) {
        String operatorId = MapUtils.getString(params, "audit.operator_id");
        String channel = MapUtils.getString(params, "channel");
        String scene = MapUtils.getString(params, "scene");
        String oldStoreSn = MapUtils.getString(params, "oldStoreSn");
        String newStoreSn = MapUtils.getString(params, "newStoreSn");
        if(Objects.equals(oldStoreSn, newStoreSn)){
            throw new BusinessException("新门店和原门店的sn不能相同");
        }

        Map oldStore = storeService.getStoreByStoreSn(oldStoreSn);
        Map newStore = storeService.getStoreByStoreSn(newStoreSn);
        String oldStoreId = MapUtils.getString(oldStore, "id");
        String oldMerchantId = MapUtils.getString(oldStore, "merchant_id");
        String newStoreId = MapUtils.getString(newStore, "id");
        String newMerchantId = MapUtils.getString(newStore, "merchant_id");
        // 判断旧门店是否有许可证
        if (Objects.isNull(storeDeviceActiveService.getEffectiveStoreActivated(oldStoreId, null))){
            throw new BusinessException("旧门店没有已激活的餐饮/零售许可证，无法迁移");
        }
        // 判断新门店是否已激活
        if (Objects.nonNull(storeDeviceActiveService.getEffectiveStoreActivated(newStoreId, null))){
            throw new BusinessException("新门店已有餐饮/零售许可证");
        }

        if(Objects.equals(channel, "direct") && Objects.equals(scene, "same")){
            // 直营渠道 && 同一个商户
            // 1、判断该用户是否为旧门店的点单维护人或者上级
            if(!checkBDRelation(oldStoreId, operatorId)){
                throw new BusinessException(String.format("您不是门店%s的收银系统维护人或上级，无法发起", oldStoreSn));
            }
            // 2、判断新旧门店是否属于同一个商户
            if(!Objects.equals(oldMerchantId, newMerchantId)){
                throw new BusinessException("新门店和原门店不属于同一个商户");
            }
        }else if(Objects.equals(channel, "direct") && Objects.equals(scene, "duplicate")){
            // 直营渠道 && 不同商户，无需提审批
            throw new BusinessException("直营的重复注册不是在此处提交审批");
        } else if(Objects.equals(channel, "proxy") && Objects.equals(scene, "same")){
            // 服务商渠道 && 同一个结算人（允许不同商户）
            // 1、判断新老门店的点单维护人所属组织、销售用户所属组织是否是同一个level4组织
            if(!checkStoreAndBDRelation(oldStoreId, operatorId)){
                throw new BusinessException("您不是原门店的收银系统维护人或上级，无法发起");
            }
            // 2、判断新旧门店的结算人是否是同一个
            Map oldMerchantBankAccount = merchantService.getMerchantBankAccountByMerchantId(oldMerchantId);
            Map newMerchantBankAccount = merchantService.getMerchantBankAccountByMerchantId(newMerchantId);
            if(!Objects.equals(MapUtils.getString(oldMerchantBankAccount, "identity"),MapUtils.getString(newMerchantBankAccount, "identity"))){
                throw new BusinessException("新旧门店的结算人不是同一个，无法发起");
            }
        } else if(Objects.equals(channel, "proxy") && Objects.equals(scene, "duplicate")){
            // 服务商渠道 && 不同商户
            // 1、判断新老门店的点单维护人所属组织、销售用户所属组织是否是同一个level4组织
            if(!checkStoreAndBDRelation(oldStoreId, operatorId)){
                throw new BusinessException("您不是原门店的收银系统维护人或上级，无法发起");
            }
            // 2、判断该门店所属商户是否是"被重复注册"
            if(!queryDuplicateRegisterLabel(newStoreId)){
                throw new BusinessException("请先发起重复注册审批");
            }
        }
    }

    private Boolean queryDuplicateRegisterLabel(String newStoreId) {
        try {
            SingleTagEntityRecord tag = onlineQueryService.getSingleTagEntityRecordById(tagsConfig.getStore_entity_id(), tagsConfig.getDuplicateRegister(), Arrays.asList(newStoreId), false);
            if (tag == null || tag.getTag() == null) {
                return false;
            }
            String tagValue = (String) tag.getTag().getValue();
            log.info("被重复注册标签查询状态：{}", tagValue);
            if (tagValue == null) {
                return false;
            }
            return Objects.equals(tagValue, "收单") || Objects.equals(tagValue, "收单&点单");
        } catch (Exception e) {
            log.error("被重复注册标签查询状态异常", "onlineQueryService.getSingleTagEntityRecordById", newStoreId, e);
        }
        return false;
    }

    private Boolean checkStoreAndBDRelation(String oldStoreId, String operatorId) {
        QueryCustomerRelationRequestDTO request = new QueryCustomerRelationRequestDTO();
        request.setConfigCode(cashierKeeperRelationCode);
        request.setSellerType(1);
        request.setCustomerType(1);
        request.setCustomerId(oldStoreId);
        List<QueryCustomerRelationResponseDTO> oldCustomerRelationList = iCustomerRelationFacade.findCustomerRelationList(request);
        Map user = userService.getUser(operatorId);
        if (CollectionUtils.isNotEmpty(oldCustomerRelationList) && Objects.nonNull(user)) {
            String oldSellerId = oldCustomerRelationList.get(0).getSellerId();
            Map oldUser = userService.getUser(oldSellerId);
            String organizationName1 = MapUtils.getString(oldUser, "organization_names");
            String organizationName = MapUtils.getString(user, "organization_names");
            if(!Objects.equals(organizationName.split("/")[0], organizationName1.split("/")[0])){
                return false;
            }
            String oldSellerOrganizationLevel4Name = oldCustomerRelationList.get(0).getSellerOrganizationLevel4Name();
            String[] split = organizationName.split("/");
            String level4OrganizationName = split.length > 3 ? split[3].trim() : "";
            if(oldSellerOrganizationLevel4Name.contains(level4OrganizationName)){
                return true;
            }
        }
        return false;
    }

    private Boolean checkBDRelation(String oldStoreId, String operatorId) {
        Map user = userService.getUser(operatorId);
        String operatorOrgPath = MapUtils.getString(user, "organization_path");
        if (null == user || null == operatorOrgPath) {
            return false;
        }
        QueryCustomerRelationRequestDTO request = new QueryCustomerRelationRequestDTO();
        request.setConfigCode(cashierKeeperRelationCode);
        //1BD,2组织
        request.setSellerType(1);
        //客户主体类型，1：门店，2：商户
        request.setCustomerType(1);
        //对应门店id
        request.setCustomerId(oldStoreId);
        try {
            List<QueryCustomerRelationResponseDTO> customerRelationList = iCustomerRelationFacade.findCustomerRelationList(request);
            if (CollectionUtils.isNotEmpty(customerRelationList) && StringUtils.isNotBlank(customerRelationList.get(0).getSellerId())) {
                // 如果申请人是收银系统维护人
                if (Objects.equals(customerRelationList.get(0).getSellerId(), operatorId)) {
                    return true;
                }
                Map keeper = userService.getUser(customerRelationList.get(0).getSellerId());
                String keeperOrgPath = (String) keeper.get("organization_path");
                // 如果申请人是点单维护人的上级，也通过
                if (keeperOrgPath.startsWith(operatorOrgPath)) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("查询点单维护人信息失败", "checkUfoodRelation", oldStoreId, e);
        }
        return false;
    }

    /**
     * 餐饮收银软件继承具体迁移实现
     */
    @Override
    public void licenseInherit(LicenceInheritRequest request) {
        String channel = request.getChannel();
        String scene = request.getScene();
        String oldStoreSn = request.getOldStoreSn();
        String newStoreSn = request.getNewStoreSn();
        if(Objects.equals(oldStoreSn, newStoreSn)){
            throw new BusinessException("新门店和原门店的sn不能相同");
        }

        Map newStore = storeService.getStoreByStoreSn(newStoreSn);
        Map oldStore = storeService.getStoreByStoreSn(oldStoreSn);

        if(Objects.equals(channel, "direct") && Objects.equals(scene, "same")){
            // 直营渠道 && 同一个商户
            storeLevelInherit(request, oldStore, newStore, "直营同一商户");
            // 同步到saas
//            Boolean licenceManagerOpen = ApolloUtils.getBooleanProperty(LicenceManagerConstants.LICENCE_MANAGER_OPEN_KEY, false);
//            if (success && !licenceManagerOpen) {
//                renewProcessor.inheritApply(oldStoreSn, oldMerchantSn, newStoreSn, newMerchantSn);
//            }
        }else if(Objects.equals(channel, "direct") && Objects.equals(scene, "duplicate")){
            // 直营渠道 && 不同商户
            throw new BusinessException("直营的重复注册无需提审批。");
        } else if(Objects.equals(channel, "proxy") && Objects.equals(scene, "same")){
            // 服务商渠道 && 同一个结算人（允许不同商户）
            storeLevelInherit(request, oldStore, newStore, "代理同一结算人");
        } else if(Objects.equals(channel, "proxy") && Objects.equals(scene, "duplicate")){
            // 服务商渠道 && 不同结算人
            storeLevelInherit(request, oldStore, newStore, "代理重复注册");
        }
    }

    private boolean storeLevelInherit(LicenceInheritRequest request, Map oldStore, Map newStore, String channelScene) {
        return moveActiveInfoToNewStore(oldStore, newStore);
    }

    private boolean moveActiveInfoToNewStore(Map oldStore, Map newStore) {
        String newStoreId = MapUtils.getString(newStore, "id");
        String newStoreSn = MapUtils.getString(newStore, "sn");
        String oldStoreId = MapUtils.getString(oldStore, "id");
        String oldStoreSn = MapUtils.getString(oldStore, "sn");

        List<LicenceActiveRes> licenceActiveResList = licenceManagerService.getActivatedLicenceListByOwner(oldStoreId, null);
        licenceActiveResList = licenceActiveResList.stream().filter(LicenceActiveRes::isNowValid).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(licenceActiveResList)) {
            log.info("moveActiveInfoToNewStore：{}, 门店没有有效的激活数据", oldStoreSn);
            return false;
        }

        List<LicenceActiveRes> newStoreLicenceActiveResList = licenceManagerService.getActivatedLicenceListByOwner(newStoreSn, null);
        newStoreLicenceActiveResList = newStoreLicenceActiveResList.stream().filter(LicenceActiveRes::isNowValid).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(newStoreLicenceActiveResList)) {
            log.info("moveActiveInfoToNewStore：{}, 门店已有有效的激活数据", newStoreSn);
            return false;
        }
        return licenceManagerService.moveActiveInfo(oldStoreId, newStoreId, null);
    }

    /**
     * 收银软件继承-直营商户重复注册(商户维度做迁移，将原商户资产分配给新商户，不激活，所以不需要处理saas同步)
     */
    @Override
//    @Transactional(rollbackFor = Exception.class)
    public void licenseInheritRepeatRegister(DirectMerchantRepeatRegister request) {
        String oldMerchantSn = request.getOldMerchantSn();
        String newMerchantSn = request.getNewMerchantSn();
        if(Objects.equals(oldMerchantSn, newMerchantSn)){
            throw new BusinessException("新商户和原商户的sn不能相同");
        }
        MerchantV2VO oldMerchant = merchantV2Service.getMerchantByMerchantSn(oldMerchantSn);
        MerchantV2VO newMerchant = merchantV2Service.getMerchantByMerchantSn(newMerchantSn);

        // 增加许可证资产校验，有许可证才迁移，否则报异常
        //3、将旧商户名下资产置为无效，将旧商户名下资产转移给新商户
        licenceManagerService.moveMerchantRemainingLicence(oldMerchant.getMerchantId(), newMerchant.getMerchantId(), null);
    }

    @Override
    public void syncHistoryActivationInfo(Long startTime, Long endTime) {
        List<StoreDeviceActiveDO> deviceActiveDOList;
        Integer page = 1;
        Integer pageSize = 1000;
        do {
            StoreDeviceActiveRequest request = new StoreDeviceActiveRequest();
            request.setStartRow((page-1)*pageSize);
            request.setPageSize(pageSize);
            deviceActiveDOList = storeDeviceActiveDAO.findDeviceActivePageList(request);
            for (StoreDeviceActiveDO it:deviceActiveDOList) {
                try {
                    Long activeTime = it.getActiveTime();
                    String storeId = it.getStoreId();
                    String licenceNo = it.getLicenceNo();
                    if (startTime <= activeTime && activeTime <= endTime) {
                        String channel = "直营";
                        if (StringUtils.isNotBlank(licenceNo)) {
                            LicenceInfoDO licenceInfoDO = null;
                            try {
                                licenceInfoDO = licenceInfoDAO.getLicenceInfoByLicenceNo(licenceNo);
                                if (licenceInfoDO.getUserId()!=null) {
                                    channel = "服务商";
                                }
                            } catch (Exception e) {
                                log.error("根据licenceNo查询不到许可证，licenceNo:{}", licenceNo, e);
                            }
                        }
                        dataEvent4Active(storeId, channel, true);
                        log.info("同步火山激活信息，storeId:{}, channel:{}, activeTime:{}", storeId, channel, activeTime);
                    } else {
                        log.info("同步火山激活信息忽略，storeId:{}, activeTime:{}", storeId, activeTime);
                    }
                } catch (Exception e) {
                    log.error("同步历史激活信息发生异常: {}", it, e);
                }
            }
          sleep(1000L);
            page++;
        } while (!deviceActiveDOList.isEmpty());
    }

    private void sleep(Long times) {
        try {
            Thread.sleep(times);
        } catch (InterruptedException e) {
            log.error("线程休眠发生异常", e);
        }
    }

    public void dataEvent4Active(String storeId, String channel, Boolean history) {
        CompletableFuture.runAsync(() -> {
            StoreSaveRequest storeSaveRequest = new StoreSaveRequest();
            storeSaveRequest.setStoreId(storeId);
            try {
                storeRemoteService.saveStoreInfo(storeSaveRequest);
            } catch (Exception e) {
                log.error("保存门店信息失败, storeId:{}", storeId, e);
            }

            StoreDeviceActiveDO activate = getStoreDeviceActive(storeId);
//            StoreDeviceActiveDO activate = storeDeviceActiveDAO.getActivatedStore(storeId);
            Map simpleUser = null;
            try {
                simpleUser = userService.getUser(activate.getLicenceRelatedCrmUserId());
            } catch (Exception e) {
                log.error("获取用户信息失败, storeId:{}，userId:{}", storeId, activate.getLicenceRelatedCrmUserId(), e);
            }
            Map<String, Object> extra = new HashMap<>();
            LocalDateTime dateTime = Instant.ofEpochMilli(activate.getActiveTime()).atZone(ZoneId.systemDefault()).toLocalDateTime();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
            String formattedTime = dateTime.format(formatter);
            extra.put("activated_time", formattedTime);
            extra.put("soft_biztype", activate.getType() == DeviceTypeEnum.CASHIER ? "dinner" : "retail");
            extra.put("activated_channel", channel);
            extra.put("activated_person", MapUtils.getString(simpleUser, "linkman"));
            extra.put("activated_person_organization", MapUtils.getString(simpleUser, "organization_names"));
            if (history) {
                dataEventsService.storeDataEventSendWithExtraForHistory(storeId, null, Constants.DataEventsConstants.CASHIER_ACTIVE_EVENT_TYPE, extra, false, activate.getActiveTime());
            } else {
                dataEventsService.storeDataEventSendWithExtra(storeId, null, Constants.DataEventsConstants.CASHIER_ACTIVE_EVENT_TYPE, extra, false);
            }
            log.info("收银机激活事件记录调用成功，storeId={}", storeId);
        }, syncDataAsyncExecutor).whenComplete((r, e) -> {
            if (e != null) {
                log.error("收银机激活事件记录调用异常，storeId={}", storeId, e);
            }
        });
    }

    @Override
    public void afterMoveActiveInfo(AfterMoveActiveInfoRequest request) {
        checkAfterMoveActiveInfoRequest(request);
        String activeUserId = request.getActiveUserId();
        DeviceTypeEnum deviceType = request.getDeviceType();
        String oldStoreId = request.getOldStoreId();
        String newStoreId = request.getNewStoreId();
        Map oldStore = storeService.getStore(oldStoreId);
        String oldMerchantId = MapUtils.getString(oldStore, "merchant_id");

        // 迁移完成后
        Map<String, Object> contentMap = new HashMap<>();
        contentMap.put("storeId", oldStoreId);
        contentMap.put("unbindTime", System.currentTimeMillis());
        storeDeviceActiveService.sendStoreCashierMessage(oldMerchantId, oldStoreId, NoticeType.CASHIER_UNBIND, contentMap);
        // 发送旧门店收银机解绑消息
        SceneEnum scene = SceneEnum.RESTAURANT;
        if (Objects.equals(deviceType, RETAIL_CASHIER)) {
            scene = SceneEnum.RETAIL;
        }
        storeDeviceActiveService.sendCashierBindMessage(oldStoreId, activeUserId, BindStatusEnum.UNBIND, scene);
        // 服务商的，没有许可证的，没有批次号的 这种激活数据，会之前把激活信息迁移，才需要发送新门店收银机绑定消息
        storeDeviceActiveService.sendCashierBindMessage(newStoreId, activeUserId, BindStatusEnum.BIND, scene);
    }

    @Override
    public void afterCancelActiveInfo(AfterCancelActiveInfoRequest request) {
        checkAfterCancelActiveInfoRequest(request);
        String storeId = request.getStoreId();
        Map store = storeService.getStore(storeId);
        String merchantId = MapUtils.getString(store, "merchant_id");

        Map<String, Object> contentMap = new HashMap<>();
        contentMap.put("storeId", storeId);
        contentMap.put("unbindTime", System.currentTimeMillis());
        storeDeviceActiveService.sendStoreCashierMessage(merchantId, storeId, NoticeType.CASHIER_UNBIND, contentMap);

        SceneEnum scene = SceneEnum.RESTAURANT;
        if (Objects.equals(request.getDeviceType(), RETAIL_CASHIER)) {
            scene = SceneEnum.RETAIL;
        }
        storeDeviceActiveService.sendCashierBindMessage(storeId, request.getActiveUserId(), BindStatusEnum.UNBIND, scene);
    }

    @Override
    public void afterActiveInfoExpire(AfterCancelActiveInfoRequest request) {
        checkAfterCancelActiveInfoRequest(request);
        String storeId = request.getStoreId();
        Map store = storeService.getStore(storeId);
        String merchantId = MapUtils.getString(store, "merchant_id");

        Map<String, Object> contentMap = new HashMap<>();
        contentMap.put("storeId", storeId);
        contentMap.put("unbindTime", System.currentTimeMillis());
        storeDeviceActiveService.sendStoreCashierMessage(merchantId, storeId, NoticeType.CASHIER_UNBIND, contentMap);
    }

    private void checkAfterCancelActiveInfoRequest(AfterCancelActiveInfoRequest request) {
        String storeId = request.getStoreId();
        if (StringUtils.isBlank(storeId)) {
            throw new IllegalArgumentException("storeId is blank");
        }
        DeviceTypeEnum deviceType = request.getDeviceType();
        if (null == deviceType) {
            throw new IllegalArgumentException("deviceType is null");
        }
    }

    private void checkAfterMoveActiveInfoRequest(AfterMoveActiveInfoRequest request) {
        String oldStoreId = request.getOldStoreId();
        String newStoreId = request.getNewStoreId();
        if (StringUtils.isAnyBlank(oldStoreId, newStoreId)) {
            throw new IllegalArgumentException("oldStoreId or newStoreId is blank");
        }
        DeviceTypeEnum deviceType = request.getDeviceType();
        if (null == deviceType) {
            throw new IllegalArgumentException("deviceType is null");
        }
    }
}
