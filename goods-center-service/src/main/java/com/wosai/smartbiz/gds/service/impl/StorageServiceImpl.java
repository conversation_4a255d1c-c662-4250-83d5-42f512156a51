package com.wosai.smartbiz.gds.service.impl;

import java.util.Date;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.shouqianba.saas.customer.model.response.MerchantMsgAccountResp;
import com.shouqianba.saas.customer.service.MemberMessageMerchantAccountService;
import com.shouqianba.saas.customer.service.MemberMessageSendRecordService;
import com.shouqianba.saas.customer.service.MemberMsgTaskService;
import com.wosai.app.dto.QueryMerchantUserReq;
import com.wosai.app.dto.V2.UcMerchantUserInfo;
import com.wosai.app.dto.V2.UcUserInfo;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.market.user.dto.UserDTO;
import com.wosai.smartbiz.dto.StorageItemConfigDTO;
import com.wosai.smartbiz.dto.req.storage.*;
import com.wosai.smartbiz.dto.res.storage.*;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.market.mcc.api.dto.request.BatchFindConfigByNameRequest;
import com.wosai.market.mcc.api.dto.request.CreateConfigRequest;
import com.wosai.market.mcc.api.dto.response.ConfigResponse;
import com.wosai.market.mcc.api.enums.AppId;
import com.wosai.market.mcc.api.enums.OwnerType;
import com.wosai.market.mcc.api.service.ConfigRemoteService;
import com.wosai.smartbiz.base.exceptions.BusinessException;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.base.res.CursorQueryResult;
import com.wosai.smartbiz.enums.*;
import com.wosai.smartbiz.gds.service.StorageService;
import com.wosai.smartbiz.goods.direct.*;
import com.wosai.smartbiz.goods.domain.*;
import com.wosai.smartbiz.goods.dto.StorageItemConfigInfoDTO;
import com.wosai.smartbiz.goods.manager.StorageOperateManager;
import com.wosai.smartbiz.goods.query.StorageOperateRecordQuery;
import com.wosai.smartbiz.goods.query.StorageOrderQuery;
import com.wosai.smartbiz.uc.constants.Constants;
import com.wosai.smartbiz.uc.utils.common.DateUtils;
import com.wosai.smartbiz.user.user.enums.sqb.SqbUserRoleEnum;
import com.wosai.smartbiz.utils.MobilePhoneUtil;
import com.wosai.smartbiz.utils.RedisLock;
import com.wosai.smartbiz.utils.RedisStringUtils;
import com.wosai.smartbiz.utils.RedisUtil;
import com.wosai.uc.dto.UcUserInfoResp;
import com.wosai.uc.service.UcUserService;
import com.wosai.upay.core.service.StoreService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2025/7/3
 */
@Service
@Slf4j
public class StorageServiceImpl implements StorageService {
    @Resource
    private RedisLock redisLock;
    @Resource
    private RedisStringUtils redisStringUtils;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private StorageOperateManager storageOperateManager;
    @Resource
    private StorageOrderMapper storageOrderMapper;
    @Resource
    private StorageItemMapper storageItemMapper;
    @Autowired
    private ConfigRemoteService configRemoteService;
    @Autowired
    private MemberMessageMerchantAccountService memberMessageMerchantAccountService;
    @Autowired
    private MemberMsgTaskService memberMsgTaskService;
    @Autowired
    private MemberMessageSendRecordService memberMessageSendRecordService;
    @Autowired
    private StoreService storeService;
    @Autowired
    private UcUserService ucUserService;
    @Autowired
    private com.wosai.market.user.service.UserService userService;
    @Autowired
    private MerchantUserServiceV2 sqbMerchantUserServiceV2;
    @Resource
    private StorageItemConfigMapper storageItemConfigMapper;
    @Resource
    private StorageOperateRecordMapper storageOperateRecordMapper;
    @Resource
    private MessageTaskInfoMapper messageTaskInfoMapper;
    @Resource(name = "storageExecutor")
    ThreadPoolExecutor storageExecutor;

    @Value("${smart.sendMsg.templateId:43}")
    private Integer sendMsgTemplate;
    @Value("${job.batch.size:50}")
    private Integer jobBatchSize;
    @Value("${send.msg.log.switch:true}")
    private Boolean sendMsgLogSwitch;

    private static final Config appConfig = ConfigService.getAppConfig();

    private static final String STORAGE_REDIS_PREFIX = "storage:";
    private static final String STORAGE_OPERATE_REDIS_PREFIX = "storage:operate:";
    private static final String STORAGE_GENERATE_STORAGE_NO_REDIS_PREFIX = "generate:storage:no";

    @Override
    public boolean storageItem(StorageItemRequest request) {
        //1.获取分布式锁
        String redisKey = STORAGE_REDIS_PREFIX + request.getStoreId();
        String lockValue = UUID.randomUUID().toString();
        try {
            //1.获取锁
            if (!redisLock.tryLock(redisKey, lockValue, 5)) {
                //未获取到锁，报错
                log.warn("storageItem can not acquire redisLock,storeId={},customerPhone={},customerName={}",
                        request.getStoreId(), request.getPhoneNumber(), request.getName());
                throw new BusinessException("寄存失败，当前系统繁忙，请稍后重试！");
            }
            //2.参数校验
            checkStorageParam(request);
            //3.生成寄存单号
            String storageNo = generateStorageNo();
            //4.从mcc查询配置信息
            Integer advanceDays = queryAdvanceDays(request.getStoreId());
            //5.组装数据
            //5.1 寄存单主表信息
            StorageOrderDO storageOrderDO = buildStorageOrderDO(storageNo, request, advanceDays);
            //5.2 物品表信息
            List<StorageItemDO> storageItemDOS = buildStorageItemDOs(storageNo, request);
            //5.3 操作记录信息
            List<StorageOperateRecordDO> operateRecordDOS = buildStorageOperateRecordDOs(storageNo, request);
            //6.保存数据到数据库-事务操作
            storageOperateManager.storageItem(storageOrderDO, storageItemDOS, operateRecordDOS);
            //7.是否需要发短信
            StorageMsgConfigDTO msgConfigDTO = queryMsgConfigDTO(request.getStoreId());
            if (Objects.isNull(msgConfigDTO) || !Constants.ONE.equals(msgConfigDTO.getStorage())) {
                //无需发短信
                log.info("存物成功，无需发短信，storageNo={}", storageNo);
                return true;
            }
            storageItemSendMsg(request, storageOrderDO);
            log.info("存物成功，需要发短信，storageNo={}", storageNo);
            return true;
        } catch (ParamException | BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("寄存失败，请稍后重试", e);
        } finally {
            redisLock.unlock(redisKey, lockValue);
        }
    }


    @Override
    public boolean takeoutItem(StorageTakeoutItemRequest request) {
        String redisKey = STORAGE_OPERATE_REDIS_PREFIX + request.getStorageNo();
        String lockValue = UUID.randomUUID().toString();
        try {
            if (!redisLock.tryLock(redisKey, lockValue, 5)) {
                //没获取到redis锁
                log.warn("takeoutItem can not acquire redisLock,storageNo={}", request.getStorageNo());
                throw new BusinessException("取物失败，当前系统繁忙，请稍后重试！");
            }
            //1.查询寄存单信息
            String storageNo = request.getStorageNo();
            List<TakeoutItemDTO> takeoutItemList = request.getItemList();
            StorageOrderDO storageOrderDO = storageOrderMapper.loadByStorageNo(storageNo);
            basicCheck(storageOrderDO, request.getItemList());
            //2.查询寄存商品信息，可取数量大于0的
            List<StorageItemDO> storageItemDOS = storageOperateManager.getAllAvailableItemByStorageNo(storageNo);
            Map<Long, StorageItemDO> currentMap = storageItemDOS.stream().collect(Collectors.toMap(StorageItemDO::getId, v -> v, (a, b) -> b, Maps::newHashMap));
            //3.可取出校验
            checkIfCanTakeout(currentMap, request.getItemList());
            //4.取出操作，更新到数据库
            //4.1构建商品表信息
            List<StorageItemDO> toUpdateItemList = buildTakeoutItemDOs(currentMap, request.getItemList());
            //4.2构建主表记录
            Integer statusAfterTakeout = getStatusAfterTakeout(storageItemDOS, takeoutItemList, storageOrderDO.getStatus());
            StorageOrderDO toUpdateOrder = new StorageOrderDO();
            toUpdateOrder.setId(storageOrderDO.getId());
            toUpdateOrder.setStatus(statusAfterTakeout);
            //4.3构建操作记录
            List<StorageOperateRecordDO> operateRecordList = buildTakeoutRecordDOs(currentMap, request.getItemList(), request, storageOrderDO);
            //5.更新记录-事务操作
            storageOperateManager.takeoutItem(toUpdateOrder, toUpdateItemList, operateRecordList);
            //6.是否发送取物短信
            StorageMsgConfigDTO msgConfigDTO = queryMsgConfigDTO(storageOrderDO.getStoreId());
            if (Objects.isNull(msgConfigDTO) || !Constants.ONE.equals(msgConfigDTO.getTakeout())) {
                //无需发短信
                log.info("取物成功，无需发短信，storageNo={}", storageNo);
                return true;
            }
            takeoutItemSendMsg(storageOrderDO, operateRecordList, request);
            log.info("取物成功，需要发短信，storageNo={}", storageNo);
            return true;
        } catch (ParamException | BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("取物失败，请稍后重试", e);
        } finally {
            redisLock.unlock(redisKey, lockValue);
        }
    }


    @Override
    public boolean delay(StorageDelayRequest request) {
        String redisKey = STORAGE_OPERATE_REDIS_PREFIX + request.getStorageNo();
        String lockValue = UUID.randomUUID().toString();
        try {
            //1.获取锁
            if (!redisLock.tryLock(redisKey, lockValue, 5)) {
                //没获取到redis锁
                log.warn("delay can not acquire redisLock,storageNo={}", request.getStorageNo());
                throw new BusinessException("延期失败，当前系统繁忙，请稍后重试！");
            }
            //2.查询寄存单信息
            StorageOrderDO storageOrderDO = storageOrderMapper.loadByStorageNo(request.getStorageNo());
            delayParamCheck(storageOrderDO);
            Date newExpireDate = DateUtils.addDate(new Date(), request.getDelayDays());
            List<StorageItemDO> availableItems = storageOperateManager.getAllAvailableItemByStorageNo(request.getStorageNo());
            //3.构建更新对象
            StorageOrderDO toUpdateOrder = buildDelayUpdateOrderInfo(storageOrderDO, newExpireDate);
            //4.构建延期操作记录
            List<StorageOperateRecordDO> operateRecordDOS = buildDelayOperateRecord(storageOrderDO, request, availableItems, newExpireDate);
            //5.事务操作，保存到db
            storageOperateManager.delay(operateRecordDOS, toUpdateOrder);
            log.info("延期成功，storageNo={}", request.getStorageNo());
            return true;
        } catch (ParamException | BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("延期失败，请稍后重试", e);
        } finally {
            redisLock.unlock(redisKey, lockValue);
        }
    }

    private StorageOrderDO buildDelayUpdateOrderInfo(StorageOrderDO storageOrderDO, Date newExpireDate) {
        StorageOrderDO toUpdateOrder = new StorageOrderDO();
        toUpdateOrder.setId(storageOrderDO.getId());
        toUpdateOrder.setStatus(StorageStatusEnum.IN_STORAGE.getStatus());
        toUpdateOrder.setExpireTime(newExpireDate);
        //更新提醒日期
        Integer advanceDays = queryAdvanceDays(storageOrderDO.getStoreId());
        if (Objects.nonNull(advanceDays)) {
            String newRemindDay = DateUtils.getFormatString(DateUtils.date_format_yyyyMMdd, DateUtils.addDate(newExpireDate, -advanceDays));
            toUpdateOrder.setRemindDate(newRemindDay);
        }
        toUpdateOrder.setRemindMsgSend(Constants.ZERO);
        return toUpdateOrder;
    }

    private List<StorageOperateRecordDO> buildDelayOperateRecord(StorageOrderDO storageOrderDO, StorageDelayRequest request,
                                                                 List<StorageItemDO> availableItems, Date newExpireDate) {
        List<StorageOperateRecordDO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(availableItems)) {
            return result;
        }
        String batchId = UUID.randomUUID().toString();
        for (StorageItemDO availableItem : availableItems) {
            StorageOperateRecordDO operateRecordDO = new StorageOperateRecordDO();
            operateRecordDO.setStorageNo(storageOrderDO.getStorageNo());
            operateRecordDO.setItemName(availableItem.getItemName());
            operateRecordDO.setItemFromType(availableItem.getItemFromType());
            operateRecordDO.setWaiterId(null);
            operateRecordDO.setWaiterName(null);
            operateRecordDO.setOperatorId(request.getOperatorId());
            operateRecordDO.setOperatorName(request.getOperatorName());
            operateRecordDO.setOperateType(StorageOperateTypeEnum.DELAY.getCode());
            operateRecordDO.setOperateNum(availableItem.getAvailableNum());
            operateRecordDO.setUnit(availableItem.getUnit());
            operateRecordDO.setMerchantId(storageOrderDO.getMerchantId());
            operateRecordDO.setStoreId(storageOrderDO.getStoreId());
            operateRecordDO.setExpireTime(newExpireDate);
            operateRecordDO.setStartTime(storageOrderDO.getCtime());
            operateRecordDO.setCustomerName(storageOrderDO.getCustomerName());
            operateRecordDO.setCustomerPhone(storageOrderDO.getCustomerPhone());
            operateRecordDO.setBatchId(batchId);
            result.add(operateRecordDO);
        }
        return result;
    }

    @Override
    public CursorQueryResult<StorageListDTO> queryStorageList(StorageListRequest request) {
        //1.参数校验
        checkStorageListParam(request);
        //2.查询
        Long anchorId = StringUtils.isBlank(request.getLastMark()) ? null : Long.parseLong(request.getLastMark());
        StorageOrderQuery query = StorageOrderQuery.builder().
                storeId(request.getStoreId()).
                searchParam(request.getSearchParams()).
                startTime(request.getStartTime()).
                endTime(request.getEndTime()).
                status(request.getStatus()).
                anchorId(anchorId).
                pageSize(request.getPageSize()).
                build();
        List<StorageOrderDO> storageOrderList = storageOrderMapper.pageList(query);
        //3.结果返回
        return buildStorageListResult(storageOrderList, request.getPageSize());
    }

    @Override
    public Integer queryStorageCount(StorageListRequest request) {
        //1.参数校验
        checkStorageListParam(request);
        //2.查询
        StorageOrderQuery query = StorageOrderQuery.builder().
                storeId(request.getStoreId()).
                searchParam(request.getSearchParams()).
                startTime(request.getStartTime()).
                endTime(request.getEndTime()).
                status(request.getStatus()).
                build();
        return storageOrderMapper.pageCount(query);
    }

    @Override
    public CursorQueryResult<StorageItemDTO> queryStorageItemList(StorageItemListRequest request) {
        //1.查询寄存单信息
        String storageNo = request.getStorageNo();
        StorageOrderDO storageOrderDO = storageOrderMapper.loadByStorageNo(storageNo);
        if (Objects.isNull(storageOrderDO)) {
            log.error("queryStorageItemList can not find storageNo={}", storageNo);
            return CursorQueryResult.emptyResult();
        }
        //2.校验状态
        if (StorageStatusEnum.COMPLETED.getStatus().equals(storageOrderDO.getStatus())) {
            //已取完，则不展示商品列表
            return CursorQueryResult.emptyResult();
        }
        //3.查询列表，按照创建时间升序排列
        Long anchorId = StringUtils.isBlank(request.getLastMark()) ? 0L : Long.parseLong(request.getLastMark());
        List<StorageItemDO> records = storageItemMapper.pageList(request.getStorageNo(), request.getPageSize(), anchorId);
        return buildStorageItemListResult(records, storageOrderDO, request.getPageSize());
    }

    @Override
    public List<StorageOperateRecordAggrDTO> queryStorageOperateRecordList(StorageOperateRecordRequest request) {
        List<StorageOperateRecordAggrDTO> result = Lists.newArrayList();
        //1.查询该寄存单下所有”存入和取出和延期“类型的操作记录
        List<StorageOperateRecordDO> recordList = storageOperateManager.getAllOperateRecordByStorageNo(request.getStorageNo());
        //2.按照batchId分组
        Map<String, List<StorageOperateRecordDO>> map = recordList.stream()
                .collect(Collectors.groupingBy(StorageOperateRecordDO::getBatchId));
        //3.对分组中的商品名称进行聚合
        map.forEach((batchId, records) -> {
            StorageOperateRecordAggrDTO aggrDTO = new StorageOperateRecordAggrDTO();
            StorageOperateRecordDO oneRecord = records.get(0);
            aggrDTO.setOperateDate(oneRecord.getCtime());
            aggrDTO.setOperateType(oneRecord.getOperateType());
            List<StorageItemDTO> itemList = Lists.newArrayList();
            records.forEach(record -> {
                StorageItemDTO itemDTO = new StorageItemDTO();
                itemDTO.setItemName(record.getItemName());
                itemDTO.setNum(record.getOperateNum());
                itemDTO.setUnit(record.getUnit());
                itemList.add(itemDTO);
            });
            aggrDTO.setItemList(itemList);
            aggrDTO.setOperatorName(oneRecord.getOperatorName());
            aggrDTO.setWaiterName(oneRecord.getWaiterName());
            result.add(aggrDTO);
        });
        //4.按时间正序排列并返回
        return result.stream()
                .sorted(Comparator.comparing(StorageOperateRecordAggrDTO::getOperateDate).reversed())
                .collect(Collectors.toList());
    }

    @Override
    public CursorQueryResult<StorageOperatorRecordDTO> queryStorageOperateDetailList(StorageOperateRecordDetailRequest request) {
        //1.参数校验
        checkOperateRecordDetailParam(request);
        //2.查询
        Long anchorId = StringUtils.isBlank(request.getLastMark()) ? null : Long.parseLong(request.getLastMark());
        StorageOperateRecordQuery query = StorageOperateRecordQuery.builder().
                storeId(request.getStoreId()).
                searchParam(request.getSearchParams()).
                startTime(request.getStartTime()).
                endTime(request.getEndTime()).
                operateType(request.getOperateType()).
                anchorId(anchorId).
                pageSize(request.getPageSize()).
                build();
        List<StorageOperateRecordDO> records = storageOperateRecordMapper.pageList(query);
        return buildOperateRecordDetailListResult(records, request.getPageSize());
    }

    @Override
    public StorageListDTO queryStorageInfo(StorageInfoRequest request) {
        StorageOrderDO storageOrderDO = storageOrderMapper.loadByStorageNo(request.getStorageNo());
        if (Objects.isNull(storageOrderDO)) {
            return null;
        }
        return convert(storageOrderDO);
    }

    @Override
    public List<CashierUserVO> queryStoreCashierUser(CashierUserQueryRequest request) {
        List<CashierUserVO> result = Lists.newArrayList();
        //1.查接口
        List<String> roles = Lists.newArrayList(SqbUserRoleEnum.SUPER_ADMIN.getCode(), SqbUserRoleEnum.ADMIN.getCode(), SqbUserRoleEnum.CASHIER.getCode());
        QueryMerchantUserReq cashierUserReq = new QueryMerchantUserReq()
                .setMerchant_id(request.getMerchantId())
                .setStore_id(request.getStoreId())
                .setContain_partner(true)
                .setRoles(roles);
        List<UcMerchantUserInfo> list = sqbMerchantUserServiceV2.getMerchantUser(cashierUserReq);
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        for (UcMerchantUserInfo userInfo : list) {
            CashierUserVO cashierUserVO = new CashierUserVO();
            UcUserInfo ucUserInfo = userInfo.getUcUserInfo();
            if (Objects.nonNull(ucUserInfo)) {
                cashierUserVO.setCashierId(ucUserInfo.getUc_user_id());
                cashierUserVO.setCashierName(ucUserInfo.getNickname());
            }
            result.add(cashierUserVO);
        }
        return result;
    }

    @Override
    public boolean saveStorageConfig(StorageConfigSaveRequest request) {
        //1.校验
        checkSaveConfig(request);
        //2.调mcc服务，查询开关和短信相关信息
        List<ConfigResponse> oldConfigs = getSwitchAndSmsConfig(request.getStoreId());
        //3.调mcc服务，保存开关和短信相关信息
        saveMccConfig(request);
        //4.保存商品信息到配置表中
        saveItemConfig(request);
        //5.异步操作：区分开关或者短信配置是否变动，有变动则修改对应的提醒时间
        CompletableFuture.runAsync(() -> checkSwitchAndUpdateRemindDate(oldConfigs, request), storageExecutor);
        return true;
    }


    @Override
    public StorageConfigDTO queryStorageConfig(StorageConfigQueryRequest request) {
        StorageConfigDTO result = new StorageConfigDTO();
        List<ConfigResponse> currentConfigs = getSwitchAndSmsConfig(request.getStoreId());
        //1.查询开关信息
        String switchConfig = currentConfigs.stream().filter(v -> MccConfigEnum.STORAGE_SWITCH.getName().equals(v.getName())).map(ConfigResponse::getValue).findFirst().orElse("0");
        //2.查询剩余短信条数
        Integer availableMsgCount = queryAvailableMsgCount(request.getMerchantId());
        if ("0".equals(switchConfig)) {
            //开关关闭
            result.setStorageSwitch(0);
            result.setMsgConfigInfo(null);
            result.setItemConfigInfo(null);
            result.setRemindMsgCount(availableMsgCount);
            return result;
        }
        //3.开关开启，查询商品信息
        StorageItemConfigDO itemConfigDO = storageItemConfigMapper.loadByStoreId(request.getStoreId());
        //4.构建返回值
        String msgConfig = currentConfigs.stream().filter(v -> MccConfigEnum.STORAGE_SMS_CONFIG.getName().equals(v.getName())).map(ConfigResponse::getValue).findFirst().orElse(null);
        return buildResult(switchConfig, itemConfigDO, msgConfig, availableMsgCount);
    }


    @Override
    public void updateExpireStatus() {
        Long anchor = 0L;
        //获取前一天的23时59分59秒
        Date end = DateUtils.getEndOfBeforeDay();
        List<StorageOrderDO> storageOrderList = storageOrderMapper.queryExpiredOrders(anchor, end, jobBatchSize);
        while (CollectionUtils.isNotEmpty(storageOrderList)) {
            if (!appConfig.getBooleanProperty("storage.expire.job.switch", true)) {
                return;
            }
            //更新状态
            List<Long> ids = storageOrderList.stream().map(StorageOrderDO::getId).collect(Collectors.toList());
            storageOrderMapper.batchUpdateStatus(ids, StorageStatusEnum.EXPIRED.getStatus());
            //插入操作记录表
            insertExpireRecord(storageOrderList);
            log.info("更新过期状态，本批次处理{}条，anchor={}", ids.size(), anchor);
            anchor = storageOrderList.get(storageOrderList.size() - 1).getId();
            storageOrderList = storageOrderMapper.queryExpiredOrders(anchor, end, jobBatchSize);
        }
    }

    private void insertExpireRecord(List<StorageOrderDO> storageOrderList) {
        for (StorageOrderDO storageOrderDO : storageOrderList) {
            List<StorageOperateRecordDO> recordDOS = new ArrayList<>();
            //1.查剩余可取商品
            List<StorageItemDO> availableItems = storageOperateManager.getAllAvailableItemByStorageNo(storageOrderDO.getStorageNo());
            if (CollectionUtils.isEmpty(availableItems)) {
                continue;
            }
            String batchId = UUID.randomUUID().toString();
            for (StorageItemDO availableItem : availableItems) {
                StorageOperateRecordDO recordDO = new StorageOperateRecordDO();
                recordDO.setStorageNo(storageOrderDO.getStorageNo());
                recordDO.setItemId(availableItem.getItemId());
                recordDO.setItemName(availableItem.getItemName());
                recordDO.setItemFromType(availableItem.getItemFromType());
                recordDO.setOperatorId("system");
                recordDO.setOperatorName("system");
                recordDO.setOperateType(StorageOperateTypeEnum.EXPIRED.getCode());
                recordDO.setOperateNum(availableItem.getAvailableNum());
                recordDO.setUnit(availableItem.getUnit());
                recordDO.setMerchantId(storageOrderDO.getMerchantId());
                recordDO.setStoreId(storageOrderDO.getStoreId());
                recordDO.setExpireTime(storageOrderDO.getExpireTime());
                recordDO.setStartTime(storageOrderDO.getCtime());
                recordDO.setCustomerName(storageOrderDO.getCustomerName());
                recordDO.setCustomerPhone(storageOrderDO.getCustomerPhone());
                recordDO.setRemark("定时任务操作过期");
                recordDO.setBatchId(batchId);
                recordDOS.add(recordDO);
            }
            storageOperateRecordMapper.batchInsert(recordDOS);
        }
    }

    @Override
    public void sendRemindMsg() {
        Long anchor = 0L;
        //获取当前的日期格式
        String remindDate = DateUtils.getFormatString(DateUtils.date_format_yyyyMMdd, new Date());
        List<StorageOrderDO> storageOrderList = storageOrderMapper.queryRemindOrders(anchor, remindDate, jobBatchSize);

        while (CollectionUtils.isNotEmpty(storageOrderList)) {
            //校验开关
            if (!appConfig.getBooleanProperty("storage.remind.job.switch", true)) {
                return;
            }
            //批处理发送短信
            batchHandleMsgRemind(storageOrderList);
            log.info("发送到期提醒短信，本批次处理{}条，anchor={}", storageOrderList.size(), anchor);
            anchor = storageOrderList.get(storageOrderList.size() - 1).getId();
            storageOrderList = storageOrderMapper.queryRemindOrders(anchor, remindDate, jobBatchSize);
        }
    }

    private void storageItemSendMsg(StorageItemRequest request, StorageOrderDO storageOrderDO) {
        //发短信
        try {
            Long messageTaskId = getMessageTaskId(request.getMerchantId(), MessageTypeEnum.STORAGE_SAVE);
            if (Objects.nonNull(messageTaskId)) {
                //构建短信信息
                String msgContent = buildSaveMsgInfo(request.getStoreId(), storageOrderDO, request.getItemList());
                //发送短信
                String userId = queryOrCreateUserId(request.getPhoneNumber());
                sendMsg(request.getMerchantId(), messageTaskId, userId, msgContent);
            }
        } catch (Exception e) {
            log.error("storageItemSendMsg error,request={}", JSON.toJSONString(request), e);
        }
    }

    private void takeoutItemSendMsg(StorageOrderDO storageOrderDO, List<StorageOperateRecordDO> operateRecordList, StorageTakeoutItemRequest request) {
        //发短信
        try {
            Long messageTaskId = getMessageTaskId(storageOrderDO.getMerchantId(), MessageTypeEnum.STORAGE_TAKEOUT);
            if (Objects.nonNull(messageTaskId)) {
                //构建短信信息
                String msgContent = buildTakeoutMsgInfo(storageOrderDO.getStoreId(), storageOrderDO, operateRecordList);
                //发送短信
                String userId = queryOrCreateUserId(storageOrderDO.getCustomerPhone());
                sendMsg(storageOrderDO.getMerchantId(), messageTaskId, userId, msgContent);
            }
        } catch (Exception e) {
            log.error("takeoutItemSendMsg error,", e);
        }

    }

    private void checkSaveConfig(StorageConfigSaveRequest request) {
        StorageItemConfigDTO itemConfigInfo = request.getItemConfigInfo();
        if (Objects.nonNull(itemConfigInfo) && Constants.TWO.equals(itemConfigInfo.getItemType())) {
            //指定商品时，分类id和物品id不能同时为空
            if (CollectionUtils.isEmpty(itemConfigInfo.getCategoryIds()) && CollectionUtils.isEmpty(itemConfigInfo.getItemIds())) {
                throw new ParamException("指定商品不能为空");
            }
        }
    }

    private void batchHandleMsgRemind(List<StorageOrderDO> storageOrderList) {
        //1.查询店铺信息
        List<String> distinctStoreIds = storageOrderList.stream().map(StorageOrderDO::getStoreId).distinct().collect(Collectors.toList());
        Map<String, Map<String, Object>> storeInfo = new HashMap<>();
        for (String storeId : distinctStoreIds) {
            Map store = storeService.getStore(storeId);
            Map<String, Object> info = new HashMap<>();
            info.put("storeName", MapUtils.getString(store, "name"));
            info.put("phone", MapUtils.getString(store, "contact_phone"));
            storeInfo.put(storeId, info);
        }
        //2.循环发送短信
        for (StorageOrderDO storageOrderDO : storageOrderList) {
            handleSingleOrderMsgRemind(storageOrderDO, storeInfo);
        }
        //3.更新remind_msg_send字段为1
        List<Long> toUpdateIds = storageOrderList.stream().map(StorageOrderDO::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(toUpdateIds)) {
            storageOrderMapper.batchUpdateMsgSendFlag(toUpdateIds, Constants.ONE);
        }
    }

    private void handleSingleOrderMsgRemind(StorageOrderDO storageOrderDO, Map<String, Map<String, Object>> storeInfo) {
        try {
            //1.查询物品信息
            String storageNo = storageOrderDO.getStorageNo();
            String storeId = storageOrderDO.getStoreId();
            List<StorageItemDO> storageItemDOS = storageItemMapper.queryAvailable(storageNo, 0L, 100);
            List<String> itemDescList = new ArrayList<>();
            for (StorageItemDO itemDO : storageItemDOS) {
                String desc = itemDO.getAvailableNum().stripTrailingZeros().toPlainString() + itemDO.getUnit() + itemDO.getItemName();
                itemDescList.add(desc);
            }
            String itemDesc = Joiner.on("、").join(itemDescList);
            //2.查询店铺信息
            Map<String, Object> info = storeInfo.getOrDefault(storeId, new HashMap<>());
            String storeName = MapUtils.getString(info, "storeName");
            String createTime = DateUtils.getFormatString(DateUtils.date_format_MM_line_dd, storageOrderDO.getCtime());
            String expireTime = DateUtils.getFormatString(DateUtils.date_format_MM_line_dd, storageOrderDO.getExpireTime());
            //3.获取短信任务id
            Long messageTaskId = getMessageTaskId(storageOrderDO.getMerchantId(), MessageTypeEnum.STORAGE_EXPIRE);
            //4.发信息
            //构建短信信息
            String msgContent = String.format(MessageTypeEnum.STORAGE_EXPIRE.getMsgTemplate(), createTime, itemDesc, storageNo, expireTime, storeName);
            //发送短信
            String userId = queryOrCreateUserId(storageOrderDO.getCustomerPhone());
            sendMsg(storageOrderDO.getMerchantId(), messageTaskId, userId, msgContent);
        } catch (Exception e) {
            log.error("handleSingleOrderMsgRemind error,storageNo={}", storageOrderDO.getStorageNo(), e);
        }
    }

    private Long getMessageTaskId(String merchantId, MessageTypeEnum typeEnum) {
        //1.查询短信任务id
        MessageTaskInfoDO taskInfoDO = messageTaskInfoMapper.queryByMerchantIdAndType(merchantId, typeEnum.getType());
        if (Objects.nonNull(taskInfoDO) && StringUtils.isNotBlank(taskInfoDO.getMessageTaskId())) {
            return Long.valueOf(taskInfoDO.getMessageTaskId());
        }
        //没有就生成任务id,并保存
        Map<String, Object> params = Maps.newHashMap();
        params.put("merchantId", merchantId);
        params.put("tempId", sendMsgTemplate);
        params.put("taskName", typeEnum.getTaskName());
        params.put("taskContent", typeEnum.getMsgContent());
        try {
            Map<String, Object> externalMsgTask = memberMsgTaskService.createExternalMsgTask(params);
            if (MapUtils.isEmpty(externalMsgTask)) {
                return null;
            }
            Long taskId = Optional.ofNullable(externalMsgTask.get("taskId")).map(String::valueOf).map(Long::parseLong).orElse(null);
            if (Objects.isNull(taskId)) {
                return null;
            }
            //save
            MessageTaskInfoDO infoDO = new MessageTaskInfoDO();
            infoDO.setMerchantId(merchantId);
            infoDO.setMessageType(typeEnum.getType());
            infoDO.setMessageTaskId(taskId.toString());
            messageTaskInfoMapper.insert(infoDO);
            return taskId;
        } catch (Exception e) {
            log.error("memberMsgTaskService.createExternalMsgTask error,params={}", JSON.toJSONString(params));
            return null;
        }
    }

    private Integer queryAvailableMsgCount(String merchantId) {
        try {
            MerchantMsgAccountResp msgAccountResp = memberMessageMerchantAccountService.findByMerchantId(merchantId);
            if (Objects.nonNull(msgAccountResp) && Objects.nonNull(msgAccountResp.getResidueSendNumber())) {
                return msgAccountResp.getResidueSendNumber();
            }
        } catch (Exception e) {
            log.error("memberMessageMerchantAccountService.findByMerchantId error,merchantId={}", merchantId, e);
        }
        return 0;
    }

    private void delayParamCheck(StorageOrderDO storageOrderDO) {
        if (Objects.isNull(storageOrderDO)) {
            throw new BusinessException("寄存单不存在！");
        }
        if (StorageStatusEnum.COMPLETED.getStatus().equals(storageOrderDO.getStatus())) {
            throw new BusinessException("当前状态为已取完，不可续期！");
        }
        if (StorageStatusEnum.IN_STORAGE.getStatus().equals(storageOrderDO.getStatus())) {
            throw new BusinessException("当前状态为寄存中，不可续期！");
        }
    }

    /**
     * 根据门店id获取配置信息
     */
    private List<ConfigResponse> getSwitchAndSmsConfig(String storeId) {
        BatchFindConfigByNameRequest mccRequest = new BatchFindConfigByNameRequest();
        mccRequest.setAppId(AppId.UFOOD.getAppId());
        mccRequest.setOwnerType(OwnerType.STORE_ID.getOwnerType());
        mccRequest.setOwnerId(storeId);
        mccRequest.setNames(Lists.newArrayList(MccConfigEnum.STORAGE_SWITCH.getName(), MccConfigEnum.STORAGE_SMS_CONFIG.getName()));
        return configRemoteService.batchFindByNames(mccRequest);
    }

    /**
     * 查询提前发送短信的天数
     */
    private Integer queryAdvanceDays(String storeId) {
        StorageMsgConfigDTO msgConfigDTO = queryMsgConfigDTO(storeId);
        if (Objects.isNull(msgConfigDTO)) {
            return null;
        }
        Integer expire = msgConfigDTO.getExpire();
        if (!Constants.ONE.equals(expire)) {
            return null;
        }
        return msgConfigDTO.getAdvanceDays();
    }

    /**
     * 获取短信配置信息
     */
    private StorageMsgConfigDTO queryMsgConfigDTO(String storeId) {
        List<ConfigResponse> mccConfigs = getSwitchAndSmsConfig(storeId);
        //获取总开关
        String value = mccConfigs.stream().filter(v -> MccConfigEnum.STORAGE_SWITCH.getName().equals(v.getName())).map(ConfigResponse::getValue).findFirst().orElse(null);
        if (!"1".equals(value)) {
            return null;
        }
        String msgConfig = mccConfigs.stream().filter(v -> MccConfigEnum.STORAGE_SMS_CONFIG.getName().equals(v.getName())).map(ConfigResponse::getValue).findFirst().orElse(null);
        if (StringUtils.isBlank(msgConfig)) {
            return null;
        }
        return JSON.parseObject(msgConfig, StorageMsgConfigDTO.class);
    }


    public void checkSwitchAndUpdateRemindDate(List<ConfigResponse> oldConfigs, StorageConfigSaveRequest request) {
        //1.判断总开关是开还是关
        Integer storageSwitch = request.getStorageSwitch();
        if (Objects.isNull(storageSwitch) || !Constants.ONE.equals(storageSwitch)) {
            //总开关关闭不做任何处理
            log.info("总开关关闭，不做任何处理，merchantId={},storeId={}", request.getMerchantId(), request.getStoreId());
            return;
        }
        //2.总开关开启,再判断到期短信的开关变化情况
        Integer newMsgSwitch = Optional.ofNullable(request.getMsgConfigInfo()).map(StorageMsgConfigDTO::getExpire).orElse(0);
        Integer oldMsgSwitch = 0;
        String value = oldConfigs.stream().filter(v -> MccConfigEnum.STORAGE_SMS_CONFIG.getName().equals(v.getName())).map(ConfigResponse::getValue).findFirst().orElse(null);
        if (StringUtils.isNotBlank(value)) {
            StorageMsgConfigDTO storageMsgConfigDTO = JSON.parseObject(value, StorageMsgConfigDTO.class);
            oldMsgSwitch = storageMsgConfigDTO.getExpire();
        }
        //判断变化情况
        if (newMsgSwitch.equals(oldMsgSwitch)) {
            //开关未变动
            log.info("过期提醒短信开关未变动，merchantId={},storeId={}，request={}", request.getMerchantId(), request.getStoreId(), JSON.toJSONString(request));
            return;
        }
        if (Constants.ZERO.equals(newMsgSwitch)) {
            //从开到关,把寄存中的提醒时间设置为空，发送状态设置为0
            log.info("过期提醒短信从开到关，merchantId={},storeId={}，request={}", request.getMerchantId(), request.getStoreId(), JSON.toJSONString(request));
            handleSwitchClose(request.getStoreId());
        }
        if (Constants.ONE.equals(newMsgSwitch)) {
            //从关到开,设置为对应的时间
            Integer advanceDays = Optional.ofNullable(request.getMsgConfigInfo()).map(StorageMsgConfigDTO::getAdvanceDays).orElse(null);
            if (Objects.nonNull(advanceDays)) {
                log.info("过期提醒短信从关到开，merchantId={},storeId={}，advanceDays={}，request={}", request.getMerchantId(), request.getStoreId(), advanceDays, JSON.toJSONString(request));
                handleSwitchOpen(request.getStoreId(), advanceDays);
            }
        }

    }

    private void handleSwitchClose(String storeId) {
        //查询当前门店状态为寄存中的寄存单
        Long anchor = 0L;
        List<StorageOrderDO> storageOrderDOS = storageOrderMapper.queryOnStorageOrders(storeId, anchor, jobBatchSize);
        while (CollectionUtils.isNotEmpty(storageOrderDOS)) {
            //计算并更新
            for (StorageOrderDO storageOrderDO : storageOrderDOS) {
                if (Objects.nonNull(storageOrderDO.getExpireTime())) {
                    storageOrderDO.setRemindDate(null);
                    storageOrderDO.setRemindMsgSend(0);
                }
            }
            //批量更新提醒日期和发送标识
            storageOrderMapper.batchUpdateRemindDate(storageOrderDOS);
            //查询下一页数据
            anchor = storageOrderDOS.get(storageOrderDOS.size() - 1).getId();
            storageOrderDOS = storageOrderMapper.queryOnStorageOrders(storeId, anchor, jobBatchSize);
        }
    }

    private void handleSwitchOpen(String storeId, Integer newRemindDays) {
        Long anchor = 0L;
        List<StorageOrderDO> storageOrderDOS = storageOrderMapper.queryOnStorageOrders(storeId, anchor, jobBatchSize);
        while (CollectionUtils.isNotEmpty(storageOrderDOS)) {
            //计算并更新
            for (StorageOrderDO storageOrderDO : storageOrderDOS) {
                if (Objects.nonNull(storageOrderDO.getExpireTime())) {
                    Date newRemindDate = DateUtils.addDate(storageOrderDO.getExpireTime(), -newRemindDays);
                    storageOrderDO.setRemindDate(DateUtils.getFormatString(DateUtils.date_format_yyyyMMdd, newRemindDate));
                    storageOrderDO.setRemindMsgSend(0);
                }
            }
            //批量更新提醒日期和发送标识
            storageOrderMapper.batchUpdateRemindDate(storageOrderDOS);
            //查询下一页数据
            anchor = storageOrderDOS.get(storageOrderDOS.size() - 1).getId();
            storageOrderDOS = storageOrderMapper.queryOnStorageOrders(storeId, anchor, jobBatchSize);
        }
    }

    private void saveItemConfig(StorageConfigSaveRequest request) {
        StorageItemConfigDO storageItemConfig = new StorageItemConfigDO();
        storageItemConfig.setMerchantId(request.getMerchantId());
        storageItemConfig.setStoreId(request.getStoreId());
        storageItemConfig.setConfigInfo(JSON.parseObject(JSON.toJSONString(request.getItemConfigInfo()), StorageItemConfigInfoDTO.class));
        storageItemConfigMapper.insert(storageItemConfig);
    }

    private void saveMccConfig(StorageConfigSaveRequest request) {
        Integer storageSwitch = request.getStorageSwitch();
        List<CreateConfigRequest> toSaveMccConfigs = Lists.newArrayList();
        CreateConfigRequest switchConfig = MccConfigEnum.STORAGE_SWITCH.toMccRequest(AppId.UFOOD, OwnerType.STORE_ID, request.getStoreId(), storageSwitch.toString());
        toSaveMccConfigs.add(switchConfig);
        StorageMsgConfigDTO msgConfig = request.getMsgConfigInfo();
        if (Constants.ZERO.equals(storageSwitch)) {
            //开关关闭
            msgConfig = StorageMsgConfigDTO.builder().storage(Constants.ZERO).takeout(Constants.ZERO).expire(Constants.ZERO).advanceDays(null).build();
        }
        CreateConfigRequest msgConfigRequest = MccConfigEnum.STORAGE_SMS_CONFIG.toMccRequest(AppId.UFOOD, OwnerType.STORE_ID, request.getStoreId(), JSON.toJSONString(msgConfig));
        toSaveMccConfigs.add(msgConfigRequest);
        configRemoteService.batchUpsertConfigByName(toSaveMccConfigs);
    }

    private StorageConfigDTO buildResult(String switchConfig, StorageItemConfigDO itemConfigDO,
                                         String msgConfig, Integer availableMsgCount) {
        StorageConfigDTO result = new StorageConfigDTO();
        result.setStorageSwitch(Integer.valueOf(switchConfig));
        if (Objects.nonNull(itemConfigDO)) {
            result.setItemConfigInfo(JSON.parseObject(JSON.toJSONString(itemConfigDO.getConfigInfo()), StorageItemConfigDTO.class));
        }
        result.setMsgConfigInfo(StringUtils.isBlank(msgConfig) ? null : JSON.parseObject(msgConfig, StorageMsgConfigDTO.class));
        result.setRemindMsgCount(availableMsgCount);
        return result;
    }


    private void basicCheck(StorageOrderDO storageOrderDO, List<TakeoutItemDTO> itemList) {
        if (CollectionUtils.isEmpty(itemList)) {
            throw new BusinessException("取物列表为空！");
        }
        if (storageOrderDO == null) {
            throw new BusinessException("寄存单不存在！");
        }
        //2.状态校验
        if (StorageStatusEnum.COMPLETED.getStatus().equals(storageOrderDO.getStatus())) {
            throw new BusinessException("当前寄存单下商品已全部取完！");
        }
    }

    /**
     * 可取出校验
     */
    private void checkIfCanTakeout(Map<Long, StorageItemDO> currentMap, List<TakeoutItemDTO> takeOutList) {
        for (TakeoutItemDTO takeoutItemDTO : takeOutList) {
            StorageItemDO storageItemDO = currentMap.get(Long.valueOf(takeoutItemDTO.getId()));
            //1.是否存在该商品
            if (storageItemDO == null) {
                throw new BusinessException(takeoutItemDTO.getItemName() + "不存在");
            }
            //2.数量是否正确，取出数量<=剩余数量
            BigDecimal availableNum = storageItemDO.getAvailableNum();
            BigDecimal takeoutNum = takeoutItemDTO.getTakeoutNum();
            if (Objects.isNull(takeoutNum) || takeoutNum.compareTo(BigDecimal.ZERO) <= 0) {
                throw new BusinessException(takeoutItemDTO.getItemName() + "取出数量有误");
            }
            if (takeoutNum.compareTo(availableNum) > 0) {
                throw new BusinessException(takeoutItemDTO.getItemName() + "取出数量超限");
            }
            //3.开瓶校验，需一次性取出
            if (StorageItemTypeEnum.OPENED.getCode().equals(storageItemDO.getStorageType())) {
                if (availableNum.compareTo(takeoutNum) != 0) {
                    throw new BusinessException("开瓶商品需一次性取出");
                }
            }
        }
    }


    /**
     * 构建取物-商品表信息
     */
    private List<StorageItemDO> buildTakeoutItemDOs(Map<Long, StorageItemDO> currentMap,
                                                    List<TakeoutItemDTO> takeOutList) {
        List<StorageItemDO> toUpdateList = new ArrayList<>();
        for (TakeoutItemDTO takeoutItemDTO : takeOutList) {
            StorageItemDO storageItemDO = currentMap.get(Long.valueOf(takeoutItemDTO.getId()));
            StorageItemDO toUpdate = new StorageItemDO();
            toUpdate.setId(storageItemDO.getId());
            toUpdate.setAvailableNum(storageItemDO.getAvailableNum().subtract(takeoutItemDTO.getTakeoutNum()));
            toUpdateList.add(toUpdate);
        }
        return toUpdateList;
    }

    /**
     * 构建取物-操作记录表信息
     */
    private List<StorageOperateRecordDO> buildTakeoutRecordDOs(Map<Long, StorageItemDO> currentMap,
                                                               List<TakeoutItemDTO> takeOutList,
                                                               StorageTakeoutItemRequest request,
                                                               StorageOrderDO storageOrderDO) {
        List<StorageOperateRecordDO> toUpdateList = new ArrayList<>();
        String batchId = UUID.randomUUID().toString();
        for (TakeoutItemDTO takeoutItemDTO : takeOutList) {
            StorageItemDO storageItemDO = currentMap.get(Long.valueOf(takeoutItemDTO.getId()));
            StorageOperateRecordDO recordDO = new StorageOperateRecordDO();
            recordDO.setStorageNo(storageItemDO.getStorageNo());
            recordDO.setItemId(storageItemDO.getItemId());
            recordDO.setItemName(storageItemDO.getItemName());
            recordDO.setItemFromType(storageItemDO.getItemFromType());
            recordDO.setWaiterId(request.getWaiterId());
            recordDO.setWaiterName(request.getWaiterName());
            recordDO.setOperatorId(request.getOperatorId());
            recordDO.setOperatorName(request.getOperatorName());
            recordDO.setOperateType(StorageOperateTypeEnum.TAKEOUT.getCode());
            recordDO.setOperateNum(takeoutItemDTO.getTakeoutNum());
            recordDO.setUnit(storageItemDO.getUnit());
            recordDO.setExpireTime(storageOrderDO.getExpireTime());
            recordDO.setStartTime(storageOrderDO.getCtime());
            recordDO.setCustomerName(storageOrderDO.getCustomerName());
            recordDO.setCustomerPhone(storageOrderDO.getCustomerPhone());
            recordDO.setBatchId(batchId);
            recordDO.setStoreId(storageOrderDO.getStoreId());
            recordDO.setMerchantId(storageOrderDO.getMerchantId());
            toUpdateList.add(recordDO);
        }
        return toUpdateList;
    }

    /**
     * 计算取物操作后，寄存单的状态
     * 寄存中->寄存中/已取完
     * 已过期->已过期/已取完
     */
    private Integer getStatusAfterTakeout(List<StorageItemDO> currentItemList,
                                          List<TakeoutItemDTO> takeoutItemList,
                                          Integer originStatus) {
        Map<String, TakeoutItemDTO> takoutMap = takeoutItemList.stream().collect(Collectors.toMap(TakeoutItemDTO::getId, v -> v, (a, b) -> b, Maps::newHashMap));
        for (StorageItemDO currentItem : currentItemList) {
            TakeoutItemDTO takeoutItemDTO = takoutMap.get(currentItem.getId().toString());
            if (Objects.isNull(takeoutItemDTO)) {
                return originStatus;
            }
            if (takeoutItemDTO.getTakeoutNum().compareTo(currentItem.getAvailableNum()) < 0) {
                return originStatus;
            }
        }
        return StorageStatusEnum.COMPLETED.getStatus();
    }


    private CursorQueryResult<StorageItemDTO> buildStorageItemListResult(List<StorageItemDO> records,
                                                                         StorageOrderDO storageOrderDO,
                                                                         Integer pageSize) {
        CursorQueryResult<StorageItemDTO> pageResult = new CursorQueryResult<>();
        pageResult.setHasMore(Boolean.TRUE);
        //结果为空
        if (CollectionUtils.isEmpty(records)) {
            pageResult.setHasMore(false);
            pageResult.setRecords(Lists.newArrayList());
            return pageResult;
        }
        //结果不为空
        List<StorageItemDTO> result = records.stream().map(v -> convert(v, storageOrderDO)).collect(Collectors.toList());
        pageResult.setRecords(result);
        if (records.size() < pageSize) {
            pageResult.setHasMore(false);
        }
        // 取最后一条数据的ID作为下一页的游标
        pageResult.setLastMark(records.get(records.size() - 1).getId().toString());
        return pageResult;
    }

    private StorageItemDTO convert(StorageItemDO storageItemDO, StorageOrderDO storageOrderDO) {
        StorageItemDTO storageItemDTO = new StorageItemDTO();
        storageItemDTO.setId(storageItemDO.getId().toString());
        storageItemDTO.setItemId(storageItemDO.getItemId());
        storageItemDTO.setItemName(storageItemDO.getItemName());
        storageItemDTO.setStorageType(storageItemDO.getStorageType());
        storageItemDTO.setItemType(storageItemDO.getItemFromType());
        storageItemDTO.setNum(storageItemDO.getNum());
        storageItemDTO.setUnit(storageItemDO.getUnit());
        storageItemDTO.setAvailableNum(storageItemDO.getAvailableNum());
        storageItemDTO.setItemDesc(storageItemDO.getItemDesc());
        storageItemDTO.setRemark(storageItemDO.getRemark());
        if (Objects.nonNull(storageOrderDO.getExpireTime())) {
            storageItemDTO.setExpireTime(storageOrderDO.getExpireTime());
        }
        storageItemDTO.setWaiterName(storageOrderDO.getWaiterName());
        storageItemDTO.setOperatorName(storageOrderDO.getOperatorName());
        return storageItemDTO;
    }

    private CursorQueryResult<StorageListDTO> buildStorageListResult(List<StorageOrderDO> records, Integer pageSize) {
        CursorQueryResult<StorageListDTO> pageResult = new CursorQueryResult<>();
        pageResult.setHasMore(Boolean.TRUE);
        //结果为空
        if (CollectionUtils.isEmpty(records)) {
            pageResult.setHasMore(false);
            pageResult.setRecords(Lists.newArrayList());
            return pageResult;
        }
        //结果不为空
        List<StorageListDTO> result = records.stream().map(this::convert).collect(Collectors.toList());
        pageResult.setRecords(result);
        if (records.size() < pageSize) {
            pageResult.setHasMore(false);
        }
        // 取最后一条数据的ID作为下一页的游标
        pageResult.setLastMark(records.get(records.size() - 1).getId().toString());
        return pageResult;
    }

    private CursorQueryResult<StorageOperatorRecordDTO> buildOperateRecordDetailListResult(List<StorageOperateRecordDO> records, Integer pageSize) {

        CursorQueryResult<StorageOperatorRecordDTO> pageResult = new CursorQueryResult<>();
        pageResult.setHasMore(Boolean.TRUE);
        //结果为空
        if (CollectionUtils.isEmpty(records)) {
            pageResult.setHasMore(false);
            pageResult.setRecords(Lists.newArrayList());
            return pageResult;
        }
        //结果不为空
        List<StorageOperatorRecordDTO> result = records.stream().map(this::convert).collect(Collectors.toList());
        pageResult.setRecords(result);
        if (records.size() < pageSize) {
            pageResult.setHasMore(false);
        }
        // 取最后一条数据的ID作为下一页的游标
        pageResult.setLastMark(records.get(records.size() - 1).getId().toString());
        return pageResult;
    }

    private StorageOperatorRecordDTO convert(StorageOperateRecordDO recordDO) {
        StorageOperatorRecordDTO storageOperatorRecordDTO = new StorageOperatorRecordDTO();
        storageOperatorRecordDTO.setOperateType(recordDO.getOperateType());
        storageOperatorRecordDTO.setName(recordDO.getCustomerName());
        storageOperatorRecordDTO.setPhoneNumber(recordDO.getCustomerPhone());
        storageOperatorRecordDTO.setItemName(recordDO.getItemName());
        storageOperatorRecordDTO.setUnit(recordDO.getUnit());
        storageOperatorRecordDTO.setNum(recordDO.getOperateNum());
        storageOperatorRecordDTO.setStartTime(recordDO.getStartTime());
        storageOperatorRecordDTO.setOperateTime(recordDO.getCtime());
        if (Objects.nonNull(recordDO.getExpireTime())) {
            storageOperatorRecordDTO.setExpireTime(recordDO.getExpireTime());
        }
        storageOperatorRecordDTO.setOperatorName(recordDO.getOperatorName());
        storageOperatorRecordDTO.setWaiterName(recordDO.getWaiterName());
        storageOperatorRecordDTO.setRemark(recordDO.getRemark());
        return storageOperatorRecordDTO;
    }

    private StorageListDTO convert(StorageOrderDO orderDO) {
        StorageListDTO dto = new StorageListDTO();
        dto.setStorageNo(orderDO.getStorageNo());
        dto.setName(orderDO.getCustomerName());
        dto.setPhoneNumber(orderDO.getCustomerPhone());
        dto.setStartTime(orderDO.getCtime());
        if (Objects.nonNull(orderDO.getExpireTime())) {
            dto.setExpireTime(orderDO.getExpireTime());
        }
        dto.setStatus(orderDO.getStatus());
        dto.setRemark(orderDO.getRemark());
        return dto;
    }


    private void checkStorageParam(StorageItemRequest request) {
        List<StorageItemDTO> itemList = request.getItemList();
        //基础校验
        if (CollectionUtils.isEmpty(itemList)) {
            throw new ParamException("寄存商品为空");
        }
        if (Objects.isNull(request.getStorageDays())) {
            throw new ParamException("寄存天数不能为空");
        }
        //开瓶、整瓶数量校验
        for (StorageItemDTO storageItemDTO : itemList) {
            StorageItemTypeEnum itemTypeEnum = StorageItemTypeEnum.getEnumByCode(storageItemDTO.getStorageType());
            BigDecimal num = storageItemDTO.getNum();
            if (StorageItemTypeEnum.OPENED.equals(itemTypeEnum)) {
                //开瓶校验,数量大于0小于1，且最多两位小数
                if (Objects.isNull(num) || num.compareTo(BigDecimal.ZERO) < 0
                        || num.compareTo(BigDecimal.ONE) >= 0 || num.scale() > 2) {
                    throw new ParamException("商品数量有误");
                }
            } else if (StorageItemTypeEnum.WHOLE.equals(itemTypeEnum)) {
                //整瓶校验 大于等于1且是整数
                if (num.compareTo(BigDecimal.ONE) < 0 || num.remainder(BigDecimal.ONE).compareTo(BigDecimal.ZERO) != 0) {
                    throw new ParamException("商品数量有误");
                }
            } else {
                throw new ParamException("商品寄存类型有误");
            }
        }
        //手机号校验
        request.setPhoneNumber(StringUtils.trim(request.getPhoneNumber()));
        boolean valid = MobilePhoneUtil.checkMobilePhoneValid(request.getPhoneNumber());
        if (!valid) {
            throw new ParamException("手机号格式有误");
        }
    }


    /**
     * 寄存单号生成规则：日期（4位数）+序列号（4位数）
     * 最多支持到27年后，日期部分就会溢出。。。
     */
    private String generateStorageNo() {
        //获取日期
        Long days = DateUtils.calDiffDays();
        //从redis获取序列号
        String redisKey = STORAGE_GENERATE_STORAGE_NO_REDIS_PREFIX +
                DateUtils.getFormatString(DateUtils.date_format_yyyyMMdd, new Date());
        long seqNo = redisUtil.incr(redisKey, 1L);
        //设置过期时间为第二天凌晨0点
        DateTime expireAt = new DateTime().plusDays(1).withTimeAtStartOfDay();
        redisUtil.expireAt(redisKey, expireAt.toDate());
        if (seqNo > 9999) {
            log.warn("超出当天可寄存最大数量，当前数量为{}", seqNo);
            throw new BusinessException("超出当天可寄存最大数量9999！");
        }
        return new DecimalFormat("0000").format(days) +
                new DecimalFormat("0000").format(seqNo);
    }

    private StorageOrderDO buildStorageOrderDO(String storageNo, StorageItemRequest request, Integer advanceDays) {
        StorageOrderDO storageOrderDO = new StorageOrderDO();
        storageOrderDO.setStorageNo(storageNo);
        storageOrderDO.setMerchantId(request.getMerchantId());
        storageOrderDO.setStoreId(request.getStoreId());
        storageOrderDO.setCustomerName(request.getName());
        storageOrderDO.setCustomerPhone(request.getPhoneNumber());
        storageOrderDO.setStatus(StorageStatusEnum.IN_STORAGE.getStatus());
        if (!Constants.NEGATIVE_ONE.equals(request.getStorageDays())) {
            //不是长期的
            storageOrderDO.setExpireTime(DateUtils.addDate(new Date(), request.getStorageDays()));
        }
        storageOrderDO.setWaiterId(request.getWaiterId());
        storageOrderDO.setWaiterName(request.getWaiterName());
        storageOrderDO.setOperatorId(request.getOperatorId());
        storageOrderDO.setOperatorName(request.getOperatorName());
        //name中间用逗号拼起来
        List<String> names = request.getItemList().stream().map(StorageItemDTO::getItemName).collect(Collectors.toList());
        storageOrderDO.setItemsName(Joiner.on(",").join(names));
        storageOrderDO.setRemark(request.getRemark());
        //发送提醒短信日期
        if (Objects.nonNull(storageOrderDO.getExpireTime()) && Objects.nonNull(advanceDays)) {
            Date remindDate = DateUtils.addDate(storageOrderDO.getExpireTime(), -advanceDays);
            storageOrderDO.setRemindDate(DateUtils.getFormatString(DateUtils.date_format_yyyyMMdd, remindDate));
        }
        storageOrderDO.setRemindMsgSend(Constants.ZERO);
        return storageOrderDO;
    }

    private List<StorageItemDO> buildStorageItemDOs(String storageNo, StorageItemRequest request) {
        List<StorageItemDO> storageItemDOs = Lists.newArrayList();
        for (StorageItemDTO storageItemDTO : request.getItemList()) {
            StorageItemDO itemDO = new StorageItemDO();
            itemDO.setStorageNo(storageNo);
            itemDO.setItemId(storageItemDTO.getItemId());
            itemDO.setItemName(storageItemDTO.getItemName());
            itemDO.setStorageType(storageItemDTO.getStorageType());
            itemDO.setItemFromType(storageItemDTO.getItemType());
            itemDO.setNum(storageItemDTO.getNum());
            itemDO.setUnit(storageItemDTO.getUnit());
            itemDO.setAvailableNum(storageItemDTO.getNum());
            itemDO.setItemDesc(storageItemDTO.getItemDesc());
            itemDO.setRemark(storageItemDTO.getRemark());
            storageItemDOs.add(itemDO);
        }
        return storageItemDOs;
    }

    private List<StorageOperateRecordDO> buildStorageOperateRecordDOs(String storageNo, StorageItemRequest request) {
        List<StorageOperateRecordDO> storageOperateRecordDOs = Lists.newArrayList();
        String batchId = UUID.randomUUID().toString();
        for (StorageItemDTO storageItemDTO : request.getItemList()) {
            StorageOperateRecordDO operateRecordDO = new StorageOperateRecordDO();
            operateRecordDO.setStorageNo(storageNo);
            operateRecordDO.setStoreId(request.getStoreId());
            operateRecordDO.setMerchantId(request.getMerchantId());
            operateRecordDO.setItemId(storageItemDTO.getItemId());
            operateRecordDO.setItemName(storageItemDTO.getItemName());
            operateRecordDO.setItemFromType(storageItemDTO.getItemType());
            operateRecordDO.setWaiterId(request.getWaiterId());
            operateRecordDO.setWaiterName(request.getWaiterName());
            operateRecordDO.setOperatorId(request.getOperatorId());
            operateRecordDO.setOperatorName(request.getOperatorName());
            operateRecordDO.setOperateType(StorageOperateTypeEnum.STORAGE.getCode());
            operateRecordDO.setOperateNum(storageItemDTO.getNum());
            operateRecordDO.setUnit(storageItemDTO.getUnit());
            if (!Constants.NEGATIVE_ONE.equals(request.getStorageDays())) {
                //不是长期的
                operateRecordDO.setExpireTime(DateUtils.addDate(new Date(), request.getStorageDays()));
            }
            operateRecordDO.setStartTime(new Date());
            operateRecordDO.setCustomerName(request.getName());
            operateRecordDO.setCustomerPhone(request.getPhoneNumber());
            operateRecordDO.setRemark(request.getRemark());
            operateRecordDO.setBatchId(batchId);
            storageOperateRecordDOs.add(operateRecordDO);
        }
        return storageOperateRecordDOs;
    }

    private void checkStorageListParam(StorageListRequest request) {
        Date startTime = request.getStartTime();
        Date endTime = request.getEndTime();

        //日期有一个缺省默认查当天
        if (Objects.isNull(startTime) && Objects.isNull(endTime)) {
            throw new ParamException("查询日期有误！");
        }
        //结束日期大于开始日期,且跨度不能超过31天
        if (endTime.before(startTime)) {
            throw new ParamException("查询日期有误！");
        }
        if (DateUtils.getDateInterval(startTime, endTime) + 1 > 31) {
            throw new ParamException("查询日期超出最大范围，仅支持跨度31天的时间范围查询");
        }
    }

    private void checkOperateRecordDetailParam(StorageOperateRecordDetailRequest request) {
        //结束日期大于开始日期,且跨度不能超过31天
        Date startDate = request.getStartTime();
        Date endDate = request.getEndTime();
        if (endDate.before(startDate)) {
            throw new ParamException("查询日期有误！");
        }
        if (DateUtils.getDateInterval(startDate, endDate) + 1 > 31) {
            throw new ParamException("查询日期超出最大范围，仅支持跨度31天的时间范围查询");
        }
    }

    private String buildSaveMsgInfo(String storeId, StorageOrderDO storageOrderDO,
                                    List<StorageItemDTO> itemList) {
        //查询店铺信息
        Map store = storeService.getStore(storeId);
        String storeName = MapUtils.getString(store, "name");
        List<String> itemDescList = new ArrayList<>();
        for (StorageItemDTO storageItemDTO : itemList) {
            String desc = storageItemDTO.getNum().toString() + storageItemDTO.getUnit() + storageItemDTO.getItemName();
            itemDescList.add(desc);
        }
        String itemDesc = Joiner.on("、").join(itemDescList);
        String expireTimeDesc = Objects.nonNull(storageOrderDO.getExpireTime()) ? DateUtils.getFormatString(DateUtils.date_format_MM_line_dd, storageOrderDO.getExpireTime()) : "永久有效";
        return String.format(MessageTypeEnum.STORAGE_SAVE.getMsgTemplate(),
                DateUtils.getFormatString(DateUtils.date_format_MM_line_dd, new Date()),
                itemDesc, storageOrderDO.getStorageNo(), expireTimeDesc, storeName);
    }

    private String buildTakeoutMsgInfo(String storeId, StorageOrderDO storageOrderDO,
                                       List<StorageOperateRecordDO> operateRecordList) {
        //查询店铺信息
        Map store = storeService.getStore(storeId);
        String storeName = MapUtils.getString(store, "name");
        List<String> itemDescList = new ArrayList<>();
        for (StorageOperateRecordDO recordDO : operateRecordList) {
            String desc = recordDO.getOperateNum().toString() + recordDO.getUnit() + recordDO.getItemName();
            itemDescList.add(desc);
        }
        String itemDesc = Joiner.on("、").join(itemDescList);
        return String.format(MessageTypeEnum.STORAGE_TAKEOUT.getMsgTemplate(),
                DateUtils.getFormatString(DateUtils.date_format_MM_line_dd, new Date()),
                itemDesc, storageOrderDO.getStorageNo(), storeName);
    }

    public void sendMsg(String merchantId, Long taskId, String userId, String content) {
        //发送短信
        Map<String, Object> params = new HashMap<>();
        params.put("merchantId", merchantId);
        params.put("taskId", taskId);
        params.put("userId", userId);
        params.put("sendContent", content);
        memberMessageSendRecordService.sendExternalMessage(params);
        //开关打开，则记录发送短信的日志
        if (sendMsgLogSwitch) {
            log.info("寄存功能发送短信成功，merchantId={},taskId={},userId={},sendContent={}", merchantId, taskId, userId, content);
        }
    }

    public String queryOrCreateUserId(String mobile) {
        //1.查询用户信息
        try {
            UcUserInfoResp userInfo = ucUserService.getUcUserByIdentifier(mobile, "customer");
            if (Objects.nonNull(userInfo)) {
                return userInfo.getId();
            }
            //2.没有则创建用户
            UserDTO userWithCellphone = userService.createUserWithCellphone(mobile);
            if (Objects.nonNull(userWithCellphone)) {
                return userWithCellphone.getId();
            }
        } catch (Exception e) {
            log.error("queryOrCreateUserId error,mobile:{}", mobile, e);
        }
        return null;
    }


}
