package com.wosai.smartbiz.gds.jsonrpc.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.market.awesome.message.dto.RmqxConfig;
import com.wosai.market.awesome.message.enums.DeviceTypeEnum;
import com.wosai.market.awesome.message.enums.RdsNoticeType;
import com.wosai.market.mcc.api.dto.request.BatchFindConfigByNameRequest;
import com.wosai.market.mcc.api.dto.request.CreateConfigRequest;
import com.wosai.market.mcc.api.dto.response.ConfigResponse;
import com.wosai.market.mcc.api.enums.AppId;
import com.wosai.market.mcc.api.enums.OwnerType;
import com.wosai.market.mcc.api.service.ConfigRemoteService;
import com.wosai.smartbiz.base.enums.YesNoEnum;
import com.wosai.smartbiz.base.exceptions.BusinessException;
import com.wosai.smartbiz.base.service.LockService;
import com.wosai.smartbiz.base.utils.DateUtil;
import com.wosai.smartbiz.base.utils.WebUtil;
import com.wosai.smartbiz.converter.gds.StoreTvConverter;
import com.wosai.smartbiz.gds.enums.ErrorCodeEnum;
import com.wosai.smartbiz.gds.enums.RotationPositionEnum;
import com.wosai.smartbiz.gds.service.MccConfigHelperService;
import com.wosai.smartbiz.gds.service.StoreTvRpcService;
import com.wosai.smartbiz.gds.service.TvMessageService;
import com.wosai.smartbiz.gds.vo.StoreCallConfigVO;
import com.wosai.smartbiz.gds.vo.StoreTvConfigVO;
import com.wosai.smartbiz.gds.vo.StoreTvDeviceMqttInfoVO;
import com.wosai.smartbiz.gds.vo.StoreTvDeviceVO;
import com.wosai.smartbiz.goods.constants.GdsConstants;
import com.wosai.smartbiz.goods.constants.MccConfigConstants;
import com.wosai.smartbiz.goods.direct.StoreDeviceInfoDAO;
import com.wosai.smartbiz.goods.direct.TvConfigDAO;
import com.wosai.smartbiz.goods.direct.TvConfigMediaDAO;
import com.wosai.smartbiz.goods.direct.TvDeviceDAO;
import com.wosai.smartbiz.goods.domain.TvConfigDO;
import com.wosai.smartbiz.goods.domain.TvConfigMediaDO;
import com.wosai.smartbiz.goods.domain.TvDeviceDO;
import com.wosai.smartbiz.message.MqttConfig;
import com.wosai.smartbiz.oms.api.services.OrderCallRpcService;
import com.wosai.smartbiz.uc.config.RmqxConfigInitializer;
import com.wosai.smartbiz.uc.constants.Constants;
import com.wosai.smartbiz.uc.dao.MainCashierBindInfoMapper;
import com.wosai.smartbiz.uc.jsonrpc.QrCodeRemoteService;
import com.wosai.smartbiz.uc.manager.StoreDeviceInfoService;
import com.wosai.smartbiz.uc.manager.StoreV2Service;
import com.wosai.smartbiz.uc.utils.UcRedisUtil;
import com.wosai.smartbiz.uc.vo.RemoteQrCodeVO;
import com.wosai.smartbiz.user.user.enums.UcErrorCodeEnum;
import com.wosai.smartbiz.user.user.vo.StoreV2VO;
import com.wosai.smartbiz.utils.SecretKeyUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@AutoJsonRpcServiceImpl
public class StoreTvRpcServiceImpl implements StoreTvRpcService {

    private static final String ACTION_CONNECTED = com.wosai.smartbiz.Constants.RmqxConstants.MQTT_WEB_HOOK_ACTION_CLIENT_CONNECTED;
    private static final String ACTION_DISCONNECTED = com.wosai.smartbiz.Constants.RmqxConstants.MQTT_WEB_HOOK_ACTION_CLIENT_DISCONNECTED;
    private static final String ACTION_SESSION_SUBSCRIBED = com.wosai.smartbiz.Constants.RmqxConstants.MQTT_WEB_HOOK_ACTION_SESSION_SUBSCRIBED;
    private static final String ACTION_SESSION_UNSUBSCRIBED = com.wosai.smartbiz.Constants.RmqxConstants.MQTT_WEB_HOOK_ACTION_SESSION_UNSUBSCRIBED;
    private static final List<String> PROCESS_ACTION_LIST = Lists.newArrayList(ACTION_CONNECTED, ACTION_DISCONNECTED, ACTION_SESSION_SUBSCRIBED, ACTION_SESSION_UNSUBSCRIBED);

    @Autowired
    private TvDeviceDAO tvDeviceDAO;

    @Autowired
    private StoreDeviceInfoDAO storeDeviceInfoDAO;

    @Autowired
    private StoreDeviceInfoService storeDeviceInfoService;

    @Autowired
    private MainCashierBindInfoMapper mainCashierBindInfoMapper;

    @Autowired
    private TvConfigDAO tvConfigDAO;

    @Autowired
    private TvConfigMediaDAO tvConfigMediaDAO;

    @Autowired
    private QrCodeRemoteService qrCodeRemoteService;

    @Autowired
    private TvMessageService tvMessageService;

    @Autowired
    private ConfigRemoteService sqbConfigRemoteService;

    @Autowired
    private MccConfigHelperService mccConfigHelperService;

    @Autowired
    private StoreV2Service storeV2Service;

    @Autowired
    private LockService lockService;

    @Autowired
    private UcRedisUtil redisUtil;

    @Autowired
    private MqttConfig mqttConfig;
    @Autowired
    private RmqxConfigInitializer rmqxConfigInitializer;

    @Autowired
    private OrderCallRpcService orderCallRpcService;

    /**
     * 检查设备是否在线的请求地址
     */
    @Value("${rmqx.mk-tv-check-online-url}")
    private String checkOnlineUrl;

    private HashMap<String, Object> unSubscribe = new HashMap<>();

    private final static Integer TV_CONFIG_MODE_ONE = Integer.valueOf(1);
    private final static Integer TV_CONFIG_MODE_TWO = Integer.valueOf(2);
    private final static Integer TV_CONFIG_MODE_THREE = Integer.valueOf(3);
    private final static int TV_CONFIG_DEFAULT_ROTATION_DURATION = 5;
    private final static RotationPositionEnum TV_CONFIG_DEFAULT_ROTATION_POSITION = RotationPositionEnum.BOTTOM;

    @Override
    public boolean tvWebHook(Map<String, String> bodyMap) {
        String action = MapUtils.getString(bodyMap, "action");
        String clientId = MapUtils.getString(bodyMap, "client_id");
        String topic = MapUtils.getString(bodyMap, "topic");
        if(StringUtils.isBlank(action) || StringUtils.isBlank(clientId)){
            return false;
        }
        if(!PROCESS_ACTION_LIST.contains(action)){
            return false;
        }
        RmqxConfig rmqxConfig = rmqxConfigInitializer.getRmqxConfig();
        if(!clientId.startsWith(rmqxConfig.getTvClientIdPrefix())
                && !clientId.startsWith(rmqxConfig.getCashierClientIdPrefix())
                && !clientId.startsWith(rmqxConfig.getCashierRetailClientIdPrefix())
        ){
            return false;
        }
        log.info("StoreTvRpcServiceImpl.tvWebHook,bodyMap:{}", JSON.toJSONString(bodyMap));
        return processDeviceOnlineStatus(clientId, action, rmqxConfig, topic);
    }

    @Override
    public StoreTvDeviceVO tvUseDeviceDetail(String sn) {
        TvDeviceDO tvDeviceDO = tvDeviceDAO.getTvDeviceWithSecretKeyBySn(sn);
        StoreV2VO storeV2VO = null;
        if(tvDeviceDO == null){
            tvDeviceDO = new TvDeviceDO();
            tvDeviceDO.setSn(sn);
        }else {
            //如果已经绑定后，需要更新设备的最后一次上线时间
            if(StringUtils.isNotBlank(tvDeviceDO.getStoreId()) && null != tvDeviceDO.getBindTime()){
                tvDeviceDO.setLastLoginTime(System.currentTimeMillis());
                tvDeviceDAO.updateLastLoginTimeById(tvDeviceDO.getId(), tvDeviceDO.getLastLoginTime());

                storeV2VO = storeV2Service.getStoreByStoreId(tvDeviceDO.getStoreId());
            }
        }
        StoreTvDeviceVO storeTvDeviceVO = StoreTvConverter.toDeviceVOWithSecretKey(tvDeviceDO);
        if(null != storeV2VO){
            storeTvDeviceVO.setMerchantSn(storeV2VO.getMerchantCode());
            storeTvDeviceVO.setMerchantName(storeV2VO.getMerchantName());
            storeTvDeviceVO.setStoreSn(storeV2VO.getStoreCode());
            storeTvDeviceVO.setStoreName(storeV2VO.getStoreName());
        }
        return storeTvDeviceVO;
    }

    @Override
    public StoreTvDeviceVO tvSignVerifyDeviceDetail(String sn) {
        TvDeviceDO tvDeviceDO = tvDeviceDAO.getTvDeviceWithSecretKeyBySn(sn);
        return StoreTvConverter.toDeviceVOWithSecretKey(tvDeviceDO);
    }

    @Override
    public StoreTvDeviceVO tvUseDeviceAndConfigDetail(String merchantId, String storeId, String sn) {
        if(StringUtils.isBlank(storeId)){
            throw new BusinessException(ErrorCodeEnum.PARAMETER_ERROR);
        }
        if(StringUtils.isBlank(sn)){
            throw new BusinessException(ErrorCodeEnum.PARAMETER_ERROR);
        }
        TvDeviceDO tvDeviceDO = tvDeviceDAO.getStoreTvDeviceBySn(sn, storeId);
        if(null == tvDeviceDO){
            throw new BusinessException(ErrorCodeEnum.TV_DEVICE_NOT_EXISTS);
        }
        StoreTvDeviceVO storeTvDeviceVO = assembleTvDeviceDetail(tvDeviceDO, true);
        return storeTvDeviceVO;
    }

    @Override
    public List<StoreTvDeviceVO> storeTvDeviceList(String storeId) {
        List<TvDeviceDO> tvDeviceDOList = tvDeviceDAO.listByStoreId(storeId);
        if(CollectionUtils.isEmpty(tvDeviceDOList)){
            return Lists.newArrayList();
        }
        List<Long> deviceIdList = tvDeviceDOList.stream()
                .map(TvDeviceDO::getId)
                .collect(Collectors.toList());
        List<TvConfigDO> tvConfigDOList = tvConfigDAO.usingListByDeviceIdList(deviceIdList);
        Map<Long, List<TvConfigDO>> configMap = ListUtils.defaultIfNull(tvConfigDOList, Lists.newArrayList()).stream()
                .collect(Collectors.groupingBy(TvConfigDO::getDeviceId));

        List<StoreTvDeviceVO> deviceVOList = StoreTvConverter.toDeviceVOList(tvDeviceDOList);
        deviceVOList.stream().forEach(deviceVO -> {
            List<TvConfigDO> configDOList = configMap.get(deviceVO.getDeviceId());
            if(CollectionUtils.isNotEmpty(configDOList)){
                deviceVO.setMode(configDOList.get(0).getMode());
            }
        });
        return deviceVOList;
    }

    @Override
    public StoreTvDeviceVO storeTvDeviceDetail(String storeId, Long deviceId) {
        if(StringUtils.isBlank(storeId)){
            throw new BusinessException(ErrorCodeEnum.PARAMETER_ERROR);
        }
        if(null == deviceId){
            throw new BusinessException(ErrorCodeEnum.PARAMETER_ERROR);
        }
        TvDeviceDO tvDeviceDO = tvDeviceDAO.getStoreTvDeviceById(deviceId, storeId);
        if(null == tvDeviceDO){
            throw new BusinessException(ErrorCodeEnum.TV_DEVICE_NOT_EXISTS);
        }

        return assembleTvDeviceDetail(tvDeviceDO, false);
    }

    @Override
    public StoreTvDeviceVO storeTvDeviceDetailBySn(String storeId, String sn) {
        if(StringUtils.isBlank(storeId)){
            throw new BusinessException(ErrorCodeEnum.PARAMETER_ERROR);
        }
        if(StringUtils.isBlank(sn)){
            throw new BusinessException(ErrorCodeEnum.PARAMETER_ERROR);
        }
        TvDeviceDO tvDeviceDO = tvDeviceDAO.getStoreTvDeviceBySn(sn, storeId);
        if(null == tvDeviceDO){
            tvDeviceDO = new TvDeviceDO();
            tvDeviceDO.setStoreId(storeId);
        }
        return assembleTvDeviceDetail(tvDeviceDO, false);
    }

    /**
     * 组装设备详细信息
     * @param tvDeviceDO 设备信息
     * @param tvUse 是否是TV端使用
     * @return
     */
    private StoreTvDeviceVO assembleTvDeviceDetail(TvDeviceDO tvDeviceDO, boolean tvUse){
        if(null == tvDeviceDO){
            return null;
        }
        StoreTvDeviceVO storeTvDeviceVO = StoreTvConverter.toDeviceVO(tvDeviceDO, tvUse);

        List<StoreTvConfigVO> configVOList = null;
        List<TvConfigDO> configDOList = tvConfigDAO.listByDeviceId(tvDeviceDO.getId());
        if(CollectionUtils.isNotEmpty(configDOList)){
            List<Long> configIdList = configDOList.stream()
                    .map(TvConfigDO::getId)
                    .collect(Collectors.toList());

            List<TvConfigMediaDO> configMediaDOList = tvConfigMediaDAO.listByConfigIdList(configIdList);

            Map<Long, List<TvConfigMediaDO>> configMediaMap = ListUtils.defaultIfNull(configMediaDOList, Lists.newArrayList()).stream()
                    .collect(Collectors.groupingBy(TvConfigMediaDO::getConfigId));

            configVOList = StoreTvConverter.toConfigVOList(configDOList);
            configVOList.forEach(configVO -> {
                configVO.setMediaList(StoreTvConverter.toConfigMediaVOList(configMediaMap.get(configVO.getConfigId())));

                if(tvUse && configVO.getUse() == YesNoEnum.Y){
                    if(configVO.getShowQrCode() == YesNoEnum.Y){
                        try {
                            RemoteQrCodeVO qrCodeVO = qrCodeRemoteService.applyStoreQrCode(tvDeviceDO.getMerchantId(), tvDeviceDO.getStoreId(), tvDeviceDO.getStoreId());
                            if(null != qrCodeVO && StringUtils.isNotBlank(qrCodeVO.getQrPrintCode())){
                                StoreV2VO storeV2VO = storeV2Service.getSimpleStoreByStoreId(tvDeviceDO.getStoreId());
                                String storeName = (null != storeV2VO && StringUtils.isNotBlank(storeV2VO.getStoreName())) ? storeV2VO.getStoreName() : "门店名称";
                                //idx:5 代表TV独有的门店点单码(新版)  scene:2代表门店点单码
                                String scanQrCodeUrl = qrCodeRemoteService.getQrCodeImageUrl(qrCodeVO.getQrPrintCode(), storeName, 5, 2);
                                configVO.setScanQrCodeUrl(scanQrCodeUrl);
                            }
                        } catch (Exception ex){
                            log.error("assembleTvDeviceDetail error, merchantID:{}, storeId:{}, sn:{}", tvDeviceDO.getMerchantId(), tvDeviceDO.getStoreId(), tvDeviceDO.getSn(), ex);
                        }
                    }
                    storeTvDeviceVO.setUsingConfig(configVO);
                }
            });
        }

        if(!tvUse){
            //没有配置信息的配置项设置默认值
            storeTvDeviceVO.setConfigList(assembleDefaultConfig(storeTvDeviceVO.getDeviceId(), configVOList));
        }
        return storeTvDeviceVO;
    }

    /**
     * 组装默认配置信息
     * @param deviceId
     * @param configList
     * @return
     */
    private List<StoreTvConfigVO> assembleDefaultConfig(Long deviceId, List<StoreTvConfigVO> configList){

        StoreTvConfigVO configModeOne = null, configModeTwo = null, configModeThree = null;
        if(CollectionUtils.isNotEmpty(configList)){
            Map<Integer, List<StoreTvConfigVO>> configMap = configList.stream().collect(Collectors.groupingBy(StoreTvConfigVO::getMode));

            configModeOne = CollectionUtils.isNotEmpty(configMap.get(TV_CONFIG_MODE_ONE))?configMap.get(TV_CONFIG_MODE_ONE).get(0):null;
            configModeTwo = CollectionUtils.isNotEmpty(configMap.get(TV_CONFIG_MODE_TWO))?configMap.get(TV_CONFIG_MODE_TWO).get(0):null;
            configModeThree = CollectionUtils.isNotEmpty(configMap.get(TV_CONFIG_MODE_THREE))?configMap.get(TV_CONFIG_MODE_THREE).get(0):null;
        }

        List<StoreTvConfigVO> resultConfigVOList = new ArrayList<>();
        if(null == configModeOne){
            configModeOne = new StoreTvConfigVO()
                    .setMode(TV_CONFIG_MODE_ONE)
                    .setDeviceId(deviceId)
                    .setUse(YesNoEnum.Y)
                    .setRotationDuration(TV_CONFIG_DEFAULT_ROTATION_DURATION)
                    .setRotationPosition(TV_CONFIG_DEFAULT_ROTATION_POSITION)
                    .setMediaList(Lists.newArrayList());
        }
        resultConfigVOList.add(configModeOne);

        if(null == configModeTwo){
            configModeTwo = new StoreTvConfigVO()
                    .setMode(TV_CONFIG_MODE_TWO)
                    .setDeviceId(deviceId)
                    .setUse(YesNoEnum.N)
                    .setShowQrCode(YesNoEnum.N)
                    .setShowNickname(YesNoEnum.Y)
                    .setMediaList(Lists.newArrayList());
        }
        resultConfigVOList.add(configModeTwo);

        if(null == configModeThree){
            configModeThree = new StoreTvConfigVO()
                    .setMode(TV_CONFIG_MODE_THREE)
                    .setDeviceId(deviceId)
                    .setUse(YesNoEnum.N)
                    .setShowNickname(YesNoEnum.Y)
                    .setRotationDuration(TV_CONFIG_DEFAULT_ROTATION_DURATION)
                    .setMediaList(Lists.newArrayList());
        }
        resultConfigVOList.add(configModeThree);

        return resultConfigVOList;
    }

    @Override
    public boolean deleteTvDevice(String storeId, Long deviceId) {
        TvDeviceDO tvDeviceDO = tvDeviceDAO.getStoreTvDeviceById(deviceId, storeId);
        if(null == tvDeviceDO){
            throw new BusinessException(ErrorCodeEnum.TV_DEVICE_NOT_EXISTS);
        }

        List<TvConfigDO> tvConfigDOList = tvConfigDAO.listByDeviceId(deviceId);
        if(CollectionUtils.isNotEmpty(tvConfigDOList)){
            List<Long> configIdList = tvConfigDOList.stream()
                    .map(TvConfigDO::getId)
                    .collect(Collectors.toList());

            tvConfigMediaDAO.deleteByConfigIdList(configIdList);
        }
        tvConfigDAO.deleteByDeviceId(deviceId);
        int row = tvDeviceDAO.deleteById(deviceId);
        if(row > 0){
            storeDeviceInfoService.expireStoreDeviceUseCache(storeId);
        }

        tvMessageService.sendDeviceConfigUpdateMessage(tvDeviceDO.getStoreId(), tvDeviceDO.getSn(), RdsNoticeType.UNBIND);

        int deviceCount = tvDeviceDAO.countBindTvDeviceByStoreId(tvDeviceDO.getStoreId());
        if(deviceCount == 0){
            mccConfigHelperService.syncStoreBindTvDeviceToMcc(tvDeviceDO.getStoreId(), YesNoEnum.N);
        }
        return true;
    }

    @Override
    public boolean saveTvDevice(StoreTvDeviceVO storeTvDeviceVO) {
        String lockKey = redisUtil.generateKey(Constants.RedisConstants.TV_DEVICE_BIND_PREFIX, storeTvDeviceVO.getSn());
        return lockService.performWithLock(GdsConstants.GDS_MODULE, lockKey,100L,()->{
            String storeId = storeTvDeviceVO.getStoreId();
            Long paramDeviceId = storeTvDeviceVO.getDeviceId();

            TvDeviceDO tvDeviceDO = null;
            if(null != paramDeviceId){
                tvDeviceDO = tvDeviceDAO.getStoreTvDeviceById(paramDeviceId, storeId);
                if(null == tvDeviceDO){
                    throw new BusinessException(ErrorCodeEnum.TV_DEVICE_NOT_EXISTS);
                }
                tvDeviceDO.setName(storeTvDeviceVO.getName());

                tvDeviceDAO.updateDeviceNameById(tvDeviceDO);
            }else {
                StoreV2VO storeV2VO = storeV2Service.getStoreByStoreId(storeId);
                if(null == storeV2VO){
                    throw new BusinessException(UcErrorCodeEnum.STORE_NOT_EXISTS);
                }

                TvDeviceDO existsTvDeviceDO = tvDeviceDAO.getTvDeviceBySn(storeTvDeviceVO.getSn());
                if(null != existsTvDeviceDO && StringUtils.isNotBlank(existsTvDeviceDO.getStoreId())){
                    throw new BusinessException(ErrorCodeEnum.TV_DEVICE_BOUND);
                }

                boolean online = checkTvOnlineStatus(storeTvDeviceVO.getSn());

                tvDeviceDO = new TvDeviceDO();
                tvDeviceDO.setMerchantId(storeTvDeviceVO.getMerchantId());
                tvDeviceDO.setMerchantSn(storeV2VO.getMerchantCode());
                tvDeviceDO.setStoreId(storeTvDeviceVO.getStoreId());
                tvDeviceDO.setStoreSn(storeV2VO.getStoreCode());
                tvDeviceDO.setName(storeTvDeviceVO.getName());
                tvDeviceDO.setSn(storeTvDeviceVO.getSn());
                tvDeviceDO.setOnline((online)?YesNoEnum.Y:YesNoEnum.N);
                tvDeviceDO.setBindTime(DateUtil.getCurrentTimeMillis());
                tvDeviceDO.setLastLoginTime(null);
                tvDeviceDO.setDeviceClientId(storeTvDeviceVO.getDeviceClientId());
                tvDeviceDO.setDeviceModel(storeTvDeviceVO.getDeviceModel());

                try {
                    Map<String, String> secretKeyMap = SecretKeyUtil.initKey();
                    tvDeviceDO.setPublicKey(MapUtils.getString(secretKeyMap, SecretKeyUtil.PUBLIC_KEY));
                    tvDeviceDO.setPrivateKey(MapUtils.getString(secretKeyMap, SecretKeyUtil.PRIVATE_KEY));
                } catch (Exception ex) {
                    throw new BusinessException(ErrorCodeEnum.TV_DEVICE_SECRET_KEY_INIT_ERROR);
                }
                int row = tvDeviceDAO.insert(tvDeviceDO);
                if(row > 0){
                    storeDeviceInfoService.expireStoreDeviceUseCache(tvDeviceDO.getStoreId());
                }
            }

            Long deviceId = tvDeviceDO.getId();
            List<StoreTvConfigVO> configList = storeTvDeviceVO.getConfigList();
            if(CollectionUtils.isNotEmpty(configList)){
                configList.stream().forEach(config -> {
                    TvConfigDO configDO = null;
                    if(null != config.getConfigId()){
                        configDO = tvConfigDAO.getById(config.getConfigId());
                        if(null == configDO || !Objects.equals(configDO.getDeviceId(), deviceId)){
                            return;
                        }

                        configDO.setUse(config.getUse());
                        configDO.setRotationDuration(config.getRotationDuration());
                        configDO.setRotationPosition(config.getRotationPosition());
                        configDO.setRotationContent(config.getRotationContent());
                        configDO.setShowQrCode(config.getShowQrCode());
                        configDO.setShowNickname(config.getShowNickname());

                        tvConfigDAO.updateById(configDO);
                    }else {
                        configDO = new TvConfigDO();
                        configDO.setDeviceId(deviceId);
                        configDO.setUse(config.getUse());
                        configDO.setMode(config.getMode());
                        configDO.setRotationDuration(config.getRotationDuration());
                        configDO.setRotationPosition(config.getRotationPosition());
                        configDO.setRotationContent(config.getRotationContent());
                        configDO.setShowQrCode(config.getShowQrCode());
                        configDO.setShowNickname(config.getShowNickname());

                        tvConfigDAO.insert(configDO);
                    }

                    Long configId = configDO.getId();
                    //删除媒体信息
                    tvConfigMediaDAO.deleteByConfigId(configId);
                    if(CollectionUtils.isNotEmpty(config.getMediaList())){
                        List<TvConfigMediaDO> configMediaDOList = new ArrayList<>();
                        config.getMediaList().stream().forEach(media -> {
                            TvConfigMediaDO configMediaDO = new TvConfigMediaDO()
                                    .setConfigId(configId)
                                    .setType(media.getType())
                                    .setUrl(media.getUrl())
                                    .setCoverURL(media.getCoverURL());

                            configMediaDOList.add(configMediaDO);
                        });
                        //插入媒体信息
                        tvConfigMediaDAO.batchInsert(configMediaDOList);
                    }
                });
            }

            if(null != paramDeviceId){
                tvMessageService.sendDeviceConfigUpdateMessage(storeId, storeTvDeviceVO.getSn(), RdsNoticeType.DEVICE_CONFIG_UPDATE);
            }else {
                tvMessageService.sendDeviceConfigUpdateMessage(storeId, storeTvDeviceVO.getSn(), RdsNoticeType.BIND_SUCCESS);
            }

            //是否绑定TV设备配置更新到MCC
            mccConfigHelperService.syncStoreBindTvDeviceToMcc(storeId, YesNoEnum.Y);

            return true;
        });
    }

    @Override
    public boolean updateCallConfig(StoreCallConfigVO storeCallConfigVO) {
        List<CreateConfigRequest> requests = new ArrayList<>();

        if(null != storeCallConfigVO.getOrderCallFinishDuration()){
            CreateConfigRequest request = new CreateConfigRequest(
                    AppId.UFOOD,
                    OwnerType.STORE_ID,
                    storeCallConfigVO.getStoreId(),
                    MccConfigConstants.ORDER_CALL_FINISH_DURATION,
                    String.valueOf(storeCallConfigVO.getOrderCallFinishDuration()),
                    true);
            requests.add(request);
        }

        if(null != storeCallConfigVO.getOrderCallCount()){
            CreateConfigRequest request = new CreateConfigRequest(
                    AppId.UFOOD,
                    OwnerType.STORE_ID,
                    storeCallConfigVO.getStoreId(),
                    MccConfigConstants.ORDER_CALL_COUNT,
                    String.valueOf(storeCallConfigVO.getOrderCallCount()),
                    true);
            requests.add(request);
        }

        if(null != storeCallConfigVO.getOrderCallDisplayDuration()){
            CreateConfigRequest request = new CreateConfigRequest(
                    AppId.UFOOD,
                    OwnerType.STORE_ID,
                    storeCallConfigVO.getStoreId(),
                    MccConfigConstants.ORDER_CALL_DISPLAY_DURATION,
                    String.valueOf(storeCallConfigVO.getOrderCallDisplayDuration()),
                    true);
            requests.add(request);
        }

        if(StringUtils.isNotBlank(storeCallConfigVO.getOrderCallDisplayType())){
            CreateConfigRequest request = new CreateConfigRequest(
                    AppId.UFOOD,
                    OwnerType.STORE_ID,
                    storeCallConfigVO.getStoreId(),
                    MccConfigConstants.ORDER_CALL_DISPLAY_TYPE,
                    String.valueOf(storeCallConfigVO.getOrderCallDisplayType()),
                    true);
            requests.add(request);
        }
        sqbConfigRemoteService.batchUpsertConfigByName(requests);
        tvMessageService.sendStoreCallConfigUpdateMessage(storeCallConfigVO.getStoreId());
        orderCallRpcService.expireCacheCallDataSummary(storeCallConfigVO.getStoreId());
        return true;
    }

    @Override
    public StoreCallConfigVO callConfigDetail(String storeId) {
        BatchFindConfigByNameRequest request = new BatchFindConfigByNameRequest();
        request.setAppId(AppId.UFOOD.getAppId());
        request.setOwnerType(OwnerType.STORE_ID.getOwnerType());
        request.setOwnerId(storeId);
        List<String> names = Lists.newArrayList(MccConfigConstants.ORDER_CALL_FINISH_DURATION, MccConfigConstants.ORDER_CALL_COUNT, MccConfigConstants.ORDER_CALL_DISPLAY_DURATION, MccConfigConstants.ORDER_CALL_DISPLAY_TYPE);
        request.setNames(names);
        List<ConfigResponse> responseList = sqbConfigRemoteService.batchFindByNames(request);

        StoreCallConfigVO storeCallConfigVO = new StoreCallConfigVO();
        if(CollectionUtils.isNotEmpty(responseList)){
            responseList.stream().forEach(response -> {
                if(StringUtils.isNotBlank(response.getValue())){
                    switch (response.getName()) {
                        case MccConfigConstants.ORDER_CALL_FINISH_DURATION:
                            storeCallConfigVO.setOrderCallFinishDuration(Integer.valueOf(response.getValue()));
                            break;
                        case MccConfigConstants.ORDER_CALL_COUNT:
                            storeCallConfigVO.setOrderCallCount(Integer.parseInt(response.getValue()));
                            break;
                        case MccConfigConstants.ORDER_CALL_DISPLAY_DURATION:
                            storeCallConfigVO.setOrderCallDisplayDuration(Integer.parseInt(response.getValue()));
                            break;
                        case MccConfigConstants.ORDER_CALL_DISPLAY_TYPE:
                            storeCallConfigVO.setOrderCallDisplayType(response.getValue());
                            break;
                    }
                }
            });
        }
        return storeCallConfigVO;
    }


    /**
     * 修改设备在线状态
     * @param clientId
     * @param online
     * @return
     */
    private boolean updateRdsDeviceOnlineStatus(String clientId, YesNoEnum online, String clientIdPrefix) {
        String sn = StringUtils.substringAfter(clientId, clientIdPrefix);
        if(StringUtils.isBlank(sn)){
            return false;
        }
        TvDeviceDO tvDeviceDO = tvDeviceDAO.getTvDeviceBySn(sn);
        if(null == tvDeviceDO){
            return false;
        }
        int row = tvDeviceDAO.updateOnlineById(tvDeviceDO.getId(), online);
        return row > 0;
    }
    /**
     * 修改餐饮收银机设备在线状态
     * @param clientId
     * @param online
     * @return
     */
    private boolean updateCashierDeviceOnlineStatus(String storeId, String clientId, YesNoEnum online) {
        mainCashierBindInfoMapper.updateOnline(storeId, clientId, online);
        storeDeviceInfoDAO.updateOnline(storeId, clientId, online);
        return true;
    }

    /**
     * 修改零售收银机设备在线状态
     * @param storeId
     * @param clientId
     * @param online
     * @return
     */
    private boolean updateRetailCashierDeviceOnlineStatus(String storeId, String clientId, YesNoEnum online) {
        storeDeviceInfoDAO.updateOnline(storeId, clientId, online);
        return true;
    }

    /**
     * 处理设备在线状态
     * @param clientId
     * @param action
     * @param rmqxConfig
     * @param topic
     * @return
     */
    private boolean processDeviceOnlineStatus(String clientId, String action, RmqxConfig rmqxConfig, String topic) {
        if(clientId.startsWith(rmqxConfig.getCashierClientIdPrefix())){
            if(Objects.equals(action, ACTION_SESSION_SUBSCRIBED) || Objects.equals(action, ACTION_SESSION_UNSUBSCRIBED)){
                String storeId = getMqttTopicStoreId(topic, DeviceTypeEnum.CASHIER);
                if(StringUtils.isNotBlank(storeId)){
                    switch (action) {
                        case ACTION_SESSION_SUBSCRIBED:
                            return updateCashierDeviceOnlineStatus(storeId, clientId, YesNoEnum.Y);
                        case ACTION_SESSION_UNSUBSCRIBED:
                            return updateCashierDeviceOnlineStatus(storeId, clientId, YesNoEnum.N);
                    }
                }
            }
            return false;
        }else if(clientId.startsWith(rmqxConfig.getCashierRetailClientIdPrefix())){
            if(Objects.equals(action, ACTION_SESSION_SUBSCRIBED) || Objects.equals(action, ACTION_SESSION_UNSUBSCRIBED)){
                String storeId = getMqttTopicStoreId(topic, DeviceTypeEnum.RETAIL_CASHIER);
                if(StringUtils.isNotBlank(storeId)){
                    switch (action) {
                        case ACTION_SESSION_SUBSCRIBED:
                            return updateRetailCashierDeviceOnlineStatus(storeId, clientId, YesNoEnum.Y);
                        case ACTION_SESSION_UNSUBSCRIBED:
                            return updateRetailCashierDeviceOnlineStatus(storeId, clientId, YesNoEnum.N);
                    }
                }
            }
            return false;
        }else if(clientId.startsWith(rmqxConfig.getTvClientIdPrefix())){
            if(Objects.equals(action, ACTION_CONNECTED) || Objects.equals(action, ACTION_DISCONNECTED)){
                switch (action) {
                    case ACTION_CONNECTED:
                        return updateRdsDeviceOnlineStatus(clientId, YesNoEnum.Y, rmqxConfig.getTvClientIdPrefix());
                    case ACTION_DISCONNECTED:
                        return updateRdsDeviceOnlineStatus(clientId, YesNoEnum.N, rmqxConfig.getTvClientIdPrefix());
                }
            }
            return false;
        }else {
            return false;
        }
    }

    /**
     * 通过topic获取门店ID
     * @param topic
     * @param deviceType
     * @return
     */
    private String getMqttTopicStoreId(String topic, DeviceTypeEnum deviceType) {
        if(StringUtils.isBlank(topic)){
            return null;
        }
        String storeSubTopicPrefix = rmqxConfigInitializer.getStoreSubTopicPrefix(deviceType);
        if(StringUtils.isBlank(storeSubTopicPrefix)){
            return null;
        }
        if(!topic.startsWith(storeSubTopicPrefix)){
            return null;
        }
        return StringUtils.substringAfter(topic, storeSubTopicPrefix);
    }


    /**
     * 获取设备在线状态
     * @param deviceSn
     * @return
     */
    private boolean checkTvOnlineStatus(String deviceSn) {
        try {
            String checkUrl = new StringBuilder(checkOnlineUrl).append("/").append(mqttConfig.getTvClientIdPrefix()).append(deviceSn).toString();
            String response = WebUtil.doGet(checkUrl, null);
            JSONObject jsonObject = JSON.parseObject(response);
            if(null == jsonObject){
                return false;
            }
            JSONArray data = jsonObject.getJSONArray("data");
            if(null == data){
                return false;
            }
            List<StoreTvDeviceMqttInfoVO> list = data.toJavaList(StoreTvDeviceMqttInfoVO.class);
            if(CollectionUtils.isEmpty(list)){
                return false;
            }
            StoreTvDeviceMqttInfoVO mqttInfoVO = list.get(0);
            if(null == mqttInfoVO){
                return false;
            }
            return mqttInfoVO.getConnected();
        } catch (Exception ex) {
            log.error("checkTvOnlineStatus error", ex);
            return false;
        }
    }
}
