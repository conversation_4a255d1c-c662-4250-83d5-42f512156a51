package com.wosai.smartbiz.fallbackconfig.client;

import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCFallbackDefine;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatcher;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.NamedElement;

import static com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers.is;

/**
 * @author: redhat
 */
public class ExternalRpcServiceFallBack extends JsonRPCFallbackDefine {
    @Override
    public ElementMatcher<NamedElement.TypeElement> handleClass() {
        return is(com.wosai.market.mcc.api.service.ConfigRemoteService.class)
                .or(is(com.wosai.app.service.MchUserLoginService.class))
                .or(is(com.wosai.market.user.service.UserService.class))
                .or(is(com.wosai.upay.core.service.TerminalService.class))
                .or(is(com.wosai.market.merchant.api.QRCodeRemoteService.class))
                .or(is(com.wosai.aop.gateway.service.ClientSideNoticeService.class));
    }

    @Override
    public Provider getProvider() {
        return Provider.CLIENT;
    }
}
