package com.wosai.smartbiz.goods.domain;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/7/3
 * 寄存单表
 */
@Data
public class StorageOrderDO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 寄存单号
     */
    private String storageNo;

    /**
     * 商户ID
     */
    private String merchantId;

    /**
     * 门店ID
     */
    private String storeId;

    /**
     * 顾客姓名
     */
    private String customerName;

    /**
     * 顾客手机号
     */
    private String customerPhone;

    /**
     * 寄存状态：1-寄存中 2-已取完 3-已过期
     */
    private Integer status;

    /**
     * 到期时间
     */
    private Date expireTime;

    /**
     * 服务员ID
     */
    private String waiterId;

    /**
     * 服务员姓名
     */
    private String waiterName;

    /**
     * 操作员ID
     */
    private String operatorId;

    /**
     * 操作员姓名
     */
    private String operatorName;

    /**
     * 商品名称
     */
    private String itemsName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 提醒短信发送日期 20250809
     */
    private String remindDate;

    /**
     * 提醒短信是否发送 1已发送 0未发送
     */
    private Integer remindMsgSend;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 更新时间
     */
    private Date mtime;

}
