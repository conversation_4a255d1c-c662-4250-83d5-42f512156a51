<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.smartbiz.goods.direct.LicenceClientInfoDAO" >
  <resultMap id="BaseResultMap" type="com.wosai.smartbiz.goods.domain.LicenceClientInfoDO" >
    <id column="id" property="id"/>
    <result column="licence_no" property="licenceNo"/>
    <result column="store_id" property="storeId"/>
    <result column="store_sn" property="storeSn"/>
    <result column="merchant_id" property="merchantId"/>
    <result column="merchant_sn" property="merchantSn"/>
    <result column="client_id" property="clientId"/>
    <result column="client_name" property="clientName"/>
    <result column="app_id" property="appId"/>
    <result column="qr_id" property="qrId"/>
    <result column="active_time" property="activeTime"/>
    <result column="activated" property="activated"/>
    <result column="delete_mark" property="deleteMark"/>
    <result column="ctime" property="ctime"/>
    <result column="mtime" property="mtime"/>
  </resultMap>

  <resultMap id="LicenceClientInfoResultMap" type="com.wosai.smartbiz.gds.dto.licence.LicenceClientInfoDTO" >
    <id column="id" property="id"/>
    <result column="licence_no" property="licenceNo"/>
    <result column="store_id" property="storeId"/>
    <result column="store_sn" property="storeSn"/>
    <result column="merchant_id" property="merchantId"/>
    <result column="merchant_sn" property="merchantSn"/>
    <result column="brand" property="brand"/>
    <result column="product_type" property="productType"/>
    <result column="client_id" property="clientId"/>
    <result column="client_name" property="clientName"/>
    <result column="app_id" property="appId"/>
    <result column="qr_id" property="qrId"/>
    <result column="status" property="licenceStatus"/>
    <result column="activated" property="activated"/>
    <result column="active_time" property="activeTime"/>
    <result column="expire_time" property="expireTime"/>
    <result column="bind_time" property="bindTime"/>
  </resultMap>

  <sql id="insertColumns" >
    licence_no, store_id, store_sn, merchant_id, merchant_sn, client_id, client_name, app_id, qr_id, active_time, activated, delete_mark, ctime, mtime
  </sql>
  <sql id="clientDTOColumns">
    i.licence_no,
    i.brand,
    i.product_type,
    i.status,
    i.expire_time,
    ci.store_id,
    ci.store_sn,
    ci.merchant_id,
    ci.merchant_sn,
    ci.id,
    ci.client_id,
    ci.client_name,
    ci.app_id,
    ci.qr_id,
    ci.activated,
    ci.active_time,
    ci.ctime AS bind_time
  </sql>

  <sql id="columns" >
    id, <include refid="insertColumns" />
  </sql>

  <insert id="insert" keyProperty="id" useGeneratedKeys="true">
    INSERT INTO licence_client_info (<include refid="insertColumns"/>)
    VALUES (
      #{licenceNo},
      #{storeId},
      #{storeSn},
      #{merchantId},
      #{merchantSn},
      #{clientId},
      #{clientName},
      #{appId},
      #{qrId},
      #{activeTime},
      #{activated},
      #{deleteMark},
      unix_timestamp(now()) * 1000,
      unix_timestamp(now()) * 1000
    )
  </insert>

  <insert id="batchInsert" keyProperty="id" useGeneratedKeys="true">
    INSERT INTO licence_client_info (<include refid="insertColumns"/>) VALUES
    <foreach collection="list" item="item" separator=",">
      (
      #{item.licenceNo},
      #{item.storeId},
      #{item.storeSn},
      #{item.merchantId},
      #{item.merchantSn},
      #{item.clientId},
      #{item.clientName},
      #{item.appId},
      #{item.qrId},
      #{item.activeTime},
      #{item.activated},
      #{item.deleteMark},
      unix_timestamp(now()) * 1000,
      unix_timestamp(now()) * 1000
      )
    </foreach>
  </insert>

  <update id="update">
    UPDATE licence_client_info SET
    <if test="clientId != null">
      client_id = #{clientId},
    </if>
    <if test="clientName != null">
      client_name = #{clientName},
    </if>
    <if test="appId != null">
      app_id = #{appId},
    </if>
    <if test="qrId != null">
      qr_id = #{qrId},
    </if>
    <if test="activeTime != null">
      active_time = #{activeTime},
    </if>
    <if test="activated != null">
      activated = #{activated},
    </if>
    <if test="deleteMark != null">
      delete_mark = #{deleteMark},
    </if>
    <if test="licenceNo != null">
      licence_no = #{licenceNo},
    </if>
    mtime = unix_timestamp(now()) * 1000
    WHERE id = #{id}
  </update>

  <update id="updateActivatedById">
    UPDATE licence_client_info SET activated = #{activated}, active_time = #{activeTime}, mtime = unix_timestamp(now()) * 1000 WHERE id = #{id}
  </update>

  <update id="updateActivatedByLicenceNo">
    UPDATE licence_client_info SET activated = #{activated}, active_time = #{activeTime}, mtime = unix_timestamp(now()) * 1000 WHERE licence_no = #{licenceNo} AND delete_mark = 'N'
  </update>

  <select id="getActivatedCountByLicenceNo" resultType="java.lang.Integer">
    SELECT COUNT(id) FROM licence_client_info WHERE licence_no = #{licenceNo} AND activated = 'Y' AND delete_mark = 'N'
  </select>

  <update id="cleanClientInfoById">
    UPDATE licence_client_info SET client_id = null, client_name = null, app_id = null, qr_id = null, mtime = unix_timestamp(now()) * 1000 WHERE id = #{id}
  </update>

  <update id="cleanClientInfoAndActivatedByByIds">
    UPDATE licence_client_info
    SET
    activated = 'N',
    active_time = null,
    client_id = null,
    client_name = null,
    app_id = null,
    qr_id = null,
    mtime = unix_timestamp(now()) * 1000
    WHERE id IN
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </update>

  <select id="getById" resultMap="BaseResultMap">
    SELECT <include refid="columns" /> FROM licence_client_info WHERE id = #{id} AND delete_mark = 'N'
  </select>

  <delete id="deleteById">
    UPDATE licence_client_info SET delete_mark = 'Y', mtime = unix_timestamp(now()) * 1000 WHERE id = #{id}
  </delete>

  <delete id="deleteByIds">
    UPDATE licence_client_info SET delete_mark = 'Y', mtime = unix_timestamp(now()) * 1000 WHERE id IN
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>

  <select id="getActivatedLicenceClientInfoByClientId" resultMap="LicenceClientInfoResultMap">
    SELECT <include refid="clientDTOColumns" />
    FROM licence_client_info AS ci
    JOIN licence_info AS i ON ci.licence_no = i.licence_no
    WHERE ci.client_id = #{clientId}
    AND ci.licence_no IS NOT NULL
    AND ci.activated = 'Y'
    AND ci.delete_mark = 'N'
    AND i.`status` != 'DELETED'
    <if test="merchantId != null">
      AND ci.merchant_id = #{merchantId}
    </if>
    <if test="brand != null">
      AND i.brand = #{brand}
    </if>
    <if test="productTypeList != null">
      AND i.product_type IN
      <foreach collection="productTypeList" item="productType" open="(" separator="," close=")">
        #{productType}
      </foreach>
    </if>
    ORDER BY ci.id ASC
    LIMIT 1
  </select>

  <select id="getLicenceClientInfoByStoreIdAndProductType" resultMap="LicenceClientInfoResultMap">
    SELECT <include refid="clientDTOColumns" />
    FROM licence_client_info AS ci
    JOIN licence_info AS i ON ci.licence_no = i.licence_no
    WHERE ci.store_id = #{storeId}
    AND ci.licence_no IS NOT NULL
    AND ci.delete_mark = 'N'
    AND i.`status` != 'DELETED'
    <if test="clientId != null">
      AND ci.client_id = #{clientId}
    </if>
    <if test="activated != null">
      AND ci.activated = #{activated}
    </if>
    <if test="brand != null">
      AND i.brand = #{brand}
    </if>
    <if test="productTypeList != null">
      AND i.product_type IN
      <foreach collection="productTypeList" item="productType" open="(" separator="," close=")">
        #{productType}
      </foreach>
    </if>
    ORDER BY ci.id ASC
    LIMIT 1
  </select>

  <select id="findLicenceBrandListByByStoreId" resultType="com.wosai.smartbiz.gds.enums.LicenceBrandEnum">
    SELECT DISTINCT i.brand
    FROM licence_client_info AS ci
    JOIN licence_info AS i ON ci.licence_no = i.licence_no
    WHERE ci.store_id = #{storeId}
    AND ci.licence_no IS NOT NULL
    AND ci.delete_mark = 'N'
    AND i.`status` != 'DELETED'
    <if test="clientId != null">
      AND ci.client_id = #{clientId}
    </if>
    <if test="activated != null">
      AND ci.activated = #{activated}
    </if>
    <if test="productTypeList != null">
      AND i.product_type IN
      <foreach collection="productTypeList" item="productType" open="(" separator="," close=")">
        #{productType}
      </foreach>
    </if>
  </select>

  <select id="findListByLicenceNo" resultMap="BaseResultMap">
    SELECT <include refid="columns" />
    FROM licence_client_info
    WHERE licence_no = #{licenceNo}
    <if test="clientId != null">
      AND client_id = #{clientId}
    </if>
    AND delete_mark = 'N'
    ORDER BY id ASC
  </select>

  <select id="findListByLicenceNos" resultMap="BaseResultMap">
    SELECT <include refid="columns" />
    FROM licence_client_info
    WHERE licence_no IN
    <foreach collection="licenceNos" item="licenceNo" open="(" separator="," close=")">
      #{licenceNo}
    </foreach>
    AND delete_mark = 'N'
    ORDER BY id ASC
  </select>

  <select id="findStoreBindLicenceListByBrandAndProductType" resultMap="LicenceClientInfoResultMap">
    SELECT <include refid="clientDTOColumns" />
    FROM licence_client_info AS ci
    JOIN licence_info AS i ON ci.licence_no = i.licence_no
    WHERE ci.store_id = #{storeId}
    AND ci.licence_no IS NOT NULL
    AND ci.delete_mark = 'N'
    AND i.`status` != 'DELETED'
    AND i.brand IN
    <foreach collection="brandList" item="brand" open="(" separator="," close=")">
      #{brand}
    </foreach>
    <if test="productTypeList != null">
      AND i.product_type IN
      <foreach collection="productTypeList" item="productType" open="(" separator="," close=")">
        #{productType}
      </foreach>
    </if>
    ORDER BY ci.id ASC
  </select>

  <select id="findStoreBindLicenceListByProductType" resultMap="LicenceClientInfoResultMap">
    SELECT <include refid="clientDTOColumns" />
    FROM licence_client_info AS ci
    JOIN licence_info AS i ON ci.licence_no = i.licence_no
    WHERE ci.store_id = #{storeId}
    AND ci.licence_no IS NOT NULL
    AND ci.delete_mark = 'N'
    AND i.brand = #{brand}
    AND i.product_type IN
    <foreach collection="productTypeList" item="productType" open="(" separator="," close=")">
      #{productType}
    </foreach>
    AND i.`status` != 'DELETED'
    ORDER BY ci.id ASC
  </select>

  <select id="getBindLicenceCountByProductType" resultType="java.lang.Integer">
    SELECT COUNT(ci.id)
    FROM licence_client_info AS ci
    JOIN licence_info AS i ON ci.licence_no = i.licence_no
    WHERE ci.merchant_id = #{merchantId}
    <if test="storeId != null">
      AND ci.store_id = #{storeId}
    </if>
    AND ci.licence_no IS NOT NULL
    AND ci.delete_mark = 'N'
    <if test="activated != null">
      AND ci.activated = #{activated}
    </if>
    AND i.brand IN
    <foreach collection="brandList" item="brand" open="(" separator="," close=")">
      #{brand}
    </foreach>
    AND i.product_type IN
    <foreach collection="productTypeList" item="productType" open="(" separator="," close=")">
      #{productType}
    </foreach>
    AND i.`status` != 'DELETED'
  </select>

  <sql id="listCondition">
    <where>
      <if test="storeSn != null">
        AND ci.store_sn = #{storeSn}
      </if>
      <if test="merchantSn != null">
        AND ci.merchant_sn = #{merchantSn}
      </if>
      <if test="activated != null">
        AND ci.activated = #{activated}
      </if>
      <if test="beginTime != null">
        AND ci.active_time <![CDATA[ >= ]]> #{beginTime}
      </if>
      <if test="endTime != null">
        AND ci.active_time <![CDATA[ < ]]> #{endTime}
      </if>
      <if test="brand != null">
        AND i.brand = #{brand}
      </if>
      <if test="productType != null">
        AND i.product_type = #{productType}
      </if>
      AND ci.delete_mark = 'N'
      AND i.`status` != 'DELETED'
    </where>
  </sql>

  <select id="findActivePageList" resultMap="LicenceClientInfoResultMap">
    SELECT <include refid="clientDTOColumns" />
    FROM licence_client_info AS ci
    LEFT JOIN licence_info AS i ON ci.licence_no = i.licence_no
    <include refid="listCondition" />
    ORDER BY ci.ctime DESC
    LIMIT #{startRow},#{pageSize}
  </select>

  <select id="countActivePageList" resultType="java.lang.Integer">
    SELECT COUNT(ci.id) FROM licence_client_info AS ci
    LEFT JOIN licence_info AS i ON ci.licence_no = i.licence_no
    <include refid="listCondition" />
  </select>

</mapper>